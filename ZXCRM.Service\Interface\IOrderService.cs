using ZXCRM.Service.DTOs;

namespace ZXCRM.Service.Interface
{
    public interface IOrderService
    {
        Task<IEnumerable<OrderDTO>> GetAllOrdersAsync();
        Task<IEnumerable<OrderDTO>> GetOrdersByUserIdAsync(int userId);
        Task<OrderDTO?> GetOrderByIdAsync(int id);
        Task<OrderDTO> CreateOrderAsync(CreateOrderDTO createOrderDto);
        Task<OrderDTO?> UpdateOrderAsync(UpdateOrderDTO updateOrderDto);
        Task<bool> DeleteOrderAsync(int id);
    }
}
