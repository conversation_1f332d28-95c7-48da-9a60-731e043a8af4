using AutoMapper;
using ZXCRM.Data.Entities;
using ZXCRM.Service.DTOs;

namespace ZXCRM.Service.Mappings
{
    public class AutoMapperProfile : Profile
    {
        public AutoMapperProfile()
        {
            // User映射
            CreateMap<User, UserDTO>()
                .ForMember(dest => dest.DepartmentName, opt => opt.MapFrom(src => src.Department != null ? src.Department.Name : string.Empty));

            CreateMap<CreateUserDTO, User>();
            CreateMap<UpdateUserDTO, User>();

            // Department映射
            CreateMap<Department, DepartmentDTO>()
                .ForMember(dest => dest.ParentName, opt => opt.MapFrom(src => src.Parent != null ? src.Parent.Name : null))
                .ForMember(dest => dest.UserCount, opt => opt.MapFrom(src => src.Users != null ? src.Users.Count : 0))
                .ForMember(dest => dest.Children, opt => opt.Ignore());

            CreateMap<DepartmentDTO, Department>();
            CreateMap<CreateDepartmentDTO, Department>();
            CreateMap<UpdateDepartmentDTO, Department>();

            // Opportunity映射
            CreateMap<Opportunity, OpportunityDTO>()
                .ForMember(dest => dest.CreatedByName, opt => opt.MapFrom(src => src.CreatedBy.Name));

            CreateMap<CreateOpportunityDTO, Opportunity>();
            CreateMap<UpdateOpportunityDTO, Opportunity>();

            // Order映射
            CreateMap<Order, OrderDTO>()
                .ForMember(dest => dest.OpportunityName, opt => opt.MapFrom(src => src.Opportunity != null ? src.Opportunity.Name : null))
                .ForMember(dest => dest.AccountManagerName, opt => opt.MapFrom(src => src.AccountManager != null ? src.AccountManager.Name : string.Empty))
                .ForMember(dest => dest.DepartmentName, opt => opt.MapFrom(src => src.Department != null ? src.Department.Name : string.Empty))
                .ForMember(dest => dest.ProjectManagerName, opt => opt.MapFrom(src => src.ProjectManager != null ? src.ProjectManager.Name : null))
                .ForMember(dest => dest.PerformanceDepartmentName, opt => opt.MapFrom(src => src.PerformanceDepartment != null ? src.PerformanceDepartment.Name : null))
                .ForMember(dest => dest.CreatedByName, opt => opt.MapFrom(src => src.CreatedBy != null ? src.CreatedBy.Name : string.Empty));

            CreateMap<CreateOrderDTO, Order>();
            CreateMap<UpdateOrderDTO, Order>();

            // Payment映射
            CreateMap<Payment, PaymentDTO>()
                .ForMember(dest => dest.OrderName, opt => opt.MapFrom(src => src.Order != null ? src.Order.Name : string.Empty))
                .ForMember(dest => dest.OrderCode, opt => opt.MapFrom(src => src.Order != null ? src.Order.Code : string.Empty))
                .ForMember(dest => dest.CustomerName, opt => opt.MapFrom(src => src.Order != null ? src.Order.CustomerName : string.Empty))
                .ForMember(dest => dest.AccountManagerName, opt => opt.MapFrom(src => src.Order != null && src.Order.AccountManager != null ? src.Order.AccountManager.Name : string.Empty))
                .ForMember(dest => dest.ProjectManagerName, opt => opt.MapFrom(src => src.Order != null && src.Order.ProjectManager != null ? src.Order.ProjectManager.Name : null));

            CreateMap<CreatePaymentDTO, Payment>();
            CreateMap<UpdatePaymentDTO, Payment>();

            // Invoice映射
            CreateMap<Invoice, InvoiceDTO>()
                .ForMember(dest => dest.PaymentName, opt => opt.MapFrom(src => src.Payment != null ? src.Payment.Name : string.Empty))
                .ForMember(dest => dest.PaymentCode, opt => opt.MapFrom(src => src.Payment != null ? src.Payment.Code : string.Empty))
                .ForMember(dest => dest.OrderName, opt => opt.MapFrom(src => src.Payment != null && src.Payment.Order != null ? src.Payment.Order.Name : string.Empty))
                .ForMember(dest => dest.CustomerName, opt => opt.MapFrom(src => src.Payment != null && src.Payment.Order != null ? src.Payment.Order.CustomerName : string.Empty))
                .ForMember(dest => dest.CreatedByName, opt => opt.MapFrom(src => src.CreatedBy != null ? src.CreatedBy.Name : string.Empty));

            CreateMap<CreateInvoiceDTO, Invoice>();
            CreateMap<UpdateInvoiceDTO, Invoice>();
        }
    }
}
