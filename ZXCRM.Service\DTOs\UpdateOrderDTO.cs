using System;
using System.ComponentModel.DataAnnotations;

namespace ZXCRM.Service.DTOs
{
    public class UpdateOrderDTO
    {
        [Required(ErrorMessage = "订单ID不能为空")]
        public int Id { get; set; }

        [Required(ErrorMessage = "订单名称不能为空")]
        [StringLength(100, ErrorMessage = "订单名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "订单编号不能为空")]
        [StringLength(50, ErrorMessage = "订单编号长度不能超过50个字符")]
        public string Code { get; set; } = string.Empty;

        [Required(ErrorMessage = "客户名称不能为空")]
        [StringLength(100, ErrorMessage = "客户名称长度不能超过100个字符")]
        public string CustomerName { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "联系人长度不能超过50个字符")]
        public string? ContactName { get; set; }

        [StringLength(20, ErrorMessage = "联系电话长度不能超过20个字符")]
        public string? ContactPhone { get; set; }

        public int? OpportunityId { get; set; }

        [Required(ErrorMessage = "订单金额不能为空")]
        [Range(0.01, double.MaxValue, ErrorMessage = "订单金额必须大于0")]
        public decimal Amount { get; set; }

        [Required(ErrorMessage = "订单币种不能为空")]
        [StringLength(20, ErrorMessage = "订单币种长度不能超过20个字符")]
        public string Currency { get; set; } = string.Empty;

        [Required(ErrorMessage = "结算金额不能为空")]
        [Range(0.01, double.MaxValue, ErrorMessage = "结算金额必须大于0")]
        public decimal SettlementAmount { get; set; }

        [Required(ErrorMessage = "客户经理不能为空")]
        public int AccountManagerId { get; set; }

        [Required(ErrorMessage = "创建部门不能为空")]
        public int DepartmentId { get; set; }

        [Required(ErrorMessage = "签订日期不能为空")]
        public DateTime SignDate { get; set; }

        [Required(ErrorMessage = "订单状态不能为空")]
        [StringLength(20, ErrorMessage = "订单状态长度不能超过20个字符")]
        public string Status { get; set; } = string.Empty;

        public int? ProjectManagerId { get; set; }

        public int? PerformanceDepartmentId { get; set; }
    }
}
