@*
    This partial view includes the client-side validation scripts for ASP.NET Core MVC.
    It should be included in pages that use forms with validation.
*@

<!-- jQuery Validation from CDN -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.min.js"
        integrity="sha512-rstIgDs0xPgmG6RX1Aba4KV5cWJbAMcvRCVmglpam9SoHZiUCyQVDdH2LPlxoHtrv17XWblE/V/PP+Tr04hbtA=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validation-unobtrusive/3.2.12/jquery.validate.unobtrusive.min.js"
        integrity="sha512-0AUGqSdKRrUGnhBt8VbJjPwG+Aq0LjSISO23tFf4VbKU8FYhQ8Vz6QGu2qrAXd7+Kd6hxnqTLD3L4gX0YtfaAw=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>

<script>
    // 自定义验证配置
    $(document).ready(function() {
        // 设置jQuery验证的默认配置
        $.validator.setDefaults({
            errorClass: 'is-invalid',
            validClass: 'is-valid',
            errorElement: 'div',
            errorPlacement: function(error, element) {
                error.addClass('invalid-feedback');
                if (element.prop('type') === 'checkbox') {
                    error.insertAfter(element.next('label'));
                } else {
                    error.insertAfter(element);
                }
            },
            highlight: function(element, errorClass, validClass) {
                $(element).addClass(errorClass).removeClass(validClass);
            },
            unhighlight: function(element, errorClass, validClass) {
                $(element).removeClass(errorClass).addClass(validClass);
            }
        });

        // 自定义验证方法
        $.validator.addMethod('requiredif', function(value, element, params) {
            var dependentElement = $(params.dependentelement);
            var dependentValue = params.dependentvalue;
            
            if (dependentElement.val() == dependentValue) {
                return value && value.length > 0;
            }
            return true;
        });

        // 中文验证消息
        $.extend($.validator.messages, {
            required: "此字段为必填项。",
            remote: "请修正此字段。",
            email: "请输入有效的电子邮件地址。",
            url: "请输入有效的网址。",
            date: "请输入有效的日期。",
            dateISO: "请输入有效的日期 (YYYY-MM-DD)。",
            number: "请输入有效的数字。",
            digits: "只能输入数字。",
            creditcard: "请输入有效的信用卡号码。",
            equalTo: "两次输入不一致。",
            extension: "请输入有效的后缀名。",
            maxlength: $.validator.format("最多可以输入 {0} 个字符。"),
            minlength: $.validator.format("最少要输入 {0} 个字符。"),
            rangelength: $.validator.format("请输入长度在 {0} 到 {1} 之间的字符串。"),
            range: $.validator.format("请输入范围在 {0} 到 {1} 之间的数值。"),
            max: $.validator.format("请输入不大于 {0} 的数值。"),
            min: $.validator.format("请输入不小于 {0} 的数值。")
        });

        // 实时验证配置
        $('form').each(function() {
            var form = $(this);
            
            // 为表单添加实时验证
            form.find('input, select, textarea').on('blur change', function() {
                if (form.data('validator')) {
                    form.validate().element(this);
                }
            });

            // 提交时验证
            form.on('submit', function(e) {
                if (!form.valid()) {
                    e.preventDefault();
                    
                    // 滚动到第一个错误字段
                    var firstError = form.find('.is-invalid').first();
                    if (firstError.length) {
                        $('html, body').animate({
                            scrollTop: firstError.offset().top - 100
                        }, 500);
                        firstError.focus();
                    }
                }
            });
        });

        // Bootstrap 5 表单验证样式集成
        $('.needs-validation').each(function() {
            var form = $(this);
            
            form.on('submit', function(event) {
                if (!this.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.addClass('was-validated');
            });
        });

        // 自定义验证规则示例
        $.validator.addMethod('phone', function(value, element) {
            return this.optional(element) || /^1[3-9]\d{9}$/.test(value);
        }, '请输入有效的手机号码');

        $.validator.addMethod('idcard', function(value, element) {
            return this.optional(element) || /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(value);
        }, '请输入有效的身份证号码');

        // 金额验证
        $.validator.addMethod('money', function(value, element) {
            return this.optional(element) || /^(0|[1-9]\d*)(\.\d{1,2})?$/.test(value);
        }, '请输入有效的金额（最多两位小数）');

        // 权限代码验证
        $.validator.addMethod('permissioncode', function(value, element) {
            return this.optional(element) || /^[A-Z][A-Z0-9_]*$/.test(value);
        }, '权限代码必须以大写字母开头，只能包含大写字母、数字和下划线');
    });
</script>

<style>
    /* 验证样式 */
    .is-invalid {
        border-color: #dc3545;
        padding-right: calc(1.5em + 0.75rem);
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    }

    .is-valid {
        border-color: #198754;
        padding-right: calc(1.5em + 0.75rem);
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.94-.94 1.38 1.38 3.68-**********-4.62 4.62z'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    }

    .invalid-feedback {
        width: 100%;
        margin-top: 0.25rem;
        font-size: 0.875em;
        color: #dc3545;
    }

    .valid-feedback {
        width: 100%;
        margin-top: 0.25rem;
        font-size: 0.875em;
        color: #198754;
    }

    /* 表单验证动画 */
    .form-control {
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .form-control:focus {
        border-color: #86b7fe;
        outline: 0;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }

    .form-control.is-invalid:focus {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
    }

    .form-control.is-valid:focus {
        border-color: #198754;
        box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
    }

    /* 必填字段标记 */
    .required::after {
        content: " *";
        color: #dc3545;
        font-weight: bold;
    }

    /* 验证提示样式 */
    .validation-summary-errors {
        color: #dc3545;
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 0.375rem;
        padding: 0.75rem 1.25rem;
        margin-bottom: 1rem;
    }

    .validation-summary-errors ul {
        margin-bottom: 0;
        padding-left: 1.25rem;
    }

    .validation-summary-errors li {
        margin-bottom: 0.25rem;
    }
</style>
