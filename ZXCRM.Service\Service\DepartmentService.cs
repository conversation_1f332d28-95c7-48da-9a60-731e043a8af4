using AutoMapper;
using ZXCRM.Data.Entities;
using ZXCRM.Data.UnitOfWork;
using ZXCRM.Service.DTOs;
using ZXCRM.Service.Interface;

namespace ZXCRM.Service.Service
{
    public class DepartmentService : IDepartmentService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public DepartmentService(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task<IEnumerable<DepartmentDTO>> GetAllDepartmentsAsync()
        {
            var departments = await _unitOfWork.Departments.GetAllWithIncludesAsync(d => d.Parent);
            var departmentDTOs = _mapper.Map<IEnumerable<DepartmentDTO>>(departments).ToList();

            // 手动统计每个部门的用户数量
            foreach (var departmentDTO in departmentDTOs)
            {
                var userCount = await _unitOfWork.Users.CountAsync(u => u.DepartmentId == departmentDTO.Id);
                departmentDTO.UserCount = userCount;
            }

            return departmentDTOs;
        }

        public async Task<DepartmentDTO?> GetDepartmentByIdAsync(int id)
        {
            var department = await _unitOfWork.Departments.GetByIdWithIncludesAsync(id, d => d.Parent);
            if (department == null) return null;

            var departmentDTO = _mapper.Map<DepartmentDTO>(department);

            // 手动统计用户数量
            departmentDTO.UserCount = await _unitOfWork.Users.CountAsync(u => u.DepartmentId == id);

            return departmentDTO;
        }

        public async Task<IEnumerable<DepartmentDTO>> GetDepartmentTreeAsync()
        {
            var allDepartments = await _unitOfWork.Departments.GetAllAsync();
            var departmentDtos = _mapper.Map<IEnumerable<DepartmentDTO>>(allDepartments).ToList();

            // 构建部门树
            var rootDepartments = departmentDtos.Where(d => d.ParentId == null).ToList();
            foreach (var department in rootDepartments)
            {
                BuildDepartmentTree(department, departmentDtos);
            }

            return rootDepartments;
        }

        private void BuildDepartmentTree(DepartmentDTO parent, List<DepartmentDTO> allDepartments)
        {
            var children = allDepartments.Where(d => d.ParentId == parent.Id).ToList();
            parent.Children.AddRange(children);

            foreach (var child in children)
            {
                BuildDepartmentTree(child, allDepartments);
            }
        }

        public async Task<DepartmentDTO> CreateDepartmentAsync(DepartmentDTO departmentDto)
        {
            // 检查部门编码是否已存在
            if (!string.IsNullOrEmpty(departmentDto.Code))
            {
                var existingDepartments = await _unitOfWork.Departments.FindAsync(d => d.Code == departmentDto.Code);
                if (existingDepartments.Any())
                {
                    throw new Exception("部门编码已存在");
                }
            }

            // 如果有父部门，检查父部门是否存在
            if (departmentDto.ParentId.HasValue)
            {
                var parentDepartment = await _unitOfWork.Departments.GetByIdAsync(departmentDto.ParentId.Value);
                if (parentDepartment == null)
                {
                    throw new Exception("父部门不存在");
                }
            }

            // 创建部门
            var department = new Department
            {
                Name = departmentDto.Name,
                Code = departmentDto.Code,
                ParentId = departmentDto.ParentId
            };

            await _unitOfWork.Departments.AddAsync(department);
            await _unitOfWork.SaveChangesAsync();

            return _mapper.Map<DepartmentDTO>(department);
        }

        public async Task<DepartmentDTO> CreateDepartmentAsync(CreateDepartmentDTO createDepartmentDto)
        {
            // 检查部门编码是否已存在
            if (!string.IsNullOrEmpty(createDepartmentDto.Code))
            {
                var existingDepartments = await _unitOfWork.Departments.FindAsync(d => d.Code == createDepartmentDto.Code);
                if (existingDepartments.Any())
                {
                    throw new Exception("部门编码已存在");
                }
            }

            // 如果有父部门，检查父部门是否存在
            if (createDepartmentDto.ParentId.HasValue)
            {
                var parentDepartment = await _unitOfWork.Departments.GetByIdAsync(createDepartmentDto.ParentId.Value);
                if (parentDepartment == null)
                {
                    throw new Exception("父部门不存在");
                }
            }

            // 生成部门编码（如果没有提供）
            var code = createDepartmentDto.Code;
            if (string.IsNullOrEmpty(code))
            {
                code = GenerateDepartmentCode(createDepartmentDto.Name);
            }

            // 创建部门
            var department = new Department
            {
                Name = createDepartmentDto.Name,
                Code = code,
                ParentId = createDepartmentDto.ParentId
            };

            await _unitOfWork.Departments.AddAsync(department);
            await _unitOfWork.SaveChangesAsync();

            return _mapper.Map<DepartmentDTO>(department);
        }

        public async Task<DepartmentDTO?> UpdateDepartmentAsync(DepartmentDTO departmentDto)
        {
            var department = await _unitOfWork.Departments.GetByIdAsync(departmentDto.Id);
            if (department == null)
            {
                return null;
            }

            // 检查部门编码是否已被其他部门使用
            var existingDepartments = await _unitOfWork.Departments.FindAsync(d => d.Code == departmentDto.Code && d.Id != departmentDto.Id);
            if (existingDepartments.Any())
            {
                throw new Exception("部门编码已被其他部门使用");
            }

            // 如果有父部门，检查父部门是否存在，并且不能将部门设为自己的子部门
            if (departmentDto.ParentId.HasValue)
            {
                if (departmentDto.ParentId.Value == departmentDto.Id)
                {
                    throw new Exception("不能将部门设为自己的子部门");
                }

                var parentDepartment = await _unitOfWork.Departments.GetByIdAsync(departmentDto.ParentId.Value);
                if (parentDepartment == null)
                {
                    throw new Exception("父部门不存在");
                }

                // 检查是否形成循环依赖
                if (IsCircularDependency(departmentDto.Id, departmentDto.ParentId.Value))
                {
                    throw new Exception("不能形成循环依赖");
                }
            }

            // 更新部门信息
            department.Name = departmentDto.Name;
            department.Code = departmentDto.Code;
            department.ParentId = departmentDto.ParentId;

            await _unitOfWork.Departments.UpdateAsync(department);
            await _unitOfWork.SaveChangesAsync();

            return _mapper.Map<DepartmentDTO>(department);
        }

        public async Task<DepartmentDTO?> UpdateDepartmentAsync(UpdateDepartmentDTO updateDepartmentDto)
        {
            var department = await _unitOfWork.Departments.GetByIdAsync(updateDepartmentDto.Id);
            if (department == null)
            {
                return null;
            }

            // 检查部门编码是否已被其他部门使用
            if (!string.IsNullOrEmpty(updateDepartmentDto.Code))
            {
                var existingDepartments = await _unitOfWork.Departments.FindAsync(d => d.Code == updateDepartmentDto.Code && d.Id != updateDepartmentDto.Id);
                if (existingDepartments.Any())
                {
                    throw new Exception("部门编码已被其他部门使用");
                }
            }

            // 如果有父部门，检查父部门是否存在，并且不能将部门设为自己的子部门
            if (updateDepartmentDto.ParentId.HasValue)
            {
                if (updateDepartmentDto.ParentId.Value == updateDepartmentDto.Id)
                {
                    throw new Exception("不能将部门设为自己的子部门");
                }

                var parentDepartment = await _unitOfWork.Departments.GetByIdAsync(updateDepartmentDto.ParentId.Value);
                if (parentDepartment == null)
                {
                    throw new Exception("父部门不存在");
                }

                // 检查是否形成循环依赖
                if (IsCircularDependency(updateDepartmentDto.Id, updateDepartmentDto.ParentId.Value))
                {
                    throw new Exception("不能形成循环依赖");
                }
            }

            // 更新部门信息
            department.Name = updateDepartmentDto.Name;
            department.Code = updateDepartmentDto.Code;
            department.ParentId = updateDepartmentDto.ParentId;

            await _unitOfWork.Departments.UpdateAsync(department);
            await _unitOfWork.SaveChangesAsync();

            return _mapper.Map<DepartmentDTO>(department);
        }

        public async Task<IEnumerable<UserDTO>> GetDepartmentUsersAsync(int departmentId)
        {
            var users = await _unitOfWork.Users.FindAsync(u => u.DepartmentId == departmentId);
            return _mapper.Map<IEnumerable<UserDTO>>(users);
        }

        private string GenerateDepartmentCode(string departmentName)
        {
            // 简单的编码生成逻辑：取部门名称的拼音首字母 + 时间戳
            // 这里简化处理，实际项目中可以使用更复杂的编码规则
            var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
            var code = $"DEPT_{timestamp}";
            return code;
        }

        private bool IsCircularDependency(int departmentId, int parentId)
        {
            // 检查是否形成循环依赖
            var visited = new HashSet<int>();
            var currentId = parentId;

            while (currentId != 0)
            {
                if (visited.Contains(currentId))
                {
                    return true;
                }

                if (currentId == departmentId)
                {
                    return true;
                }

                visited.Add(currentId);

                var parent = _unitOfWork.Departments.GetByIdAsync(currentId).Result;
                currentId = parent?.ParentId ?? 0;
            }

            return false;
        }

        public async Task<bool> DeleteDepartmentAsync(int id)
        {
            var department = await _unitOfWork.Departments.GetByIdAsync(id);
            if (department == null)
            {
                return false;
            }

            // 检查是否有子部门
            var childDepartments = await _unitOfWork.Departments.FindAsync(d => d.ParentId == id);
            if (childDepartments.Any())
            {
                throw new Exception("不能删除有子部门的部门");
            }

            // 检查是否有用户属于该部门
            var users = await _unitOfWork.Users.FindAsync(u => u.DepartmentId == id);
            if (users.Any())
            {
                throw new Exception("不能删除有用户的部门");
            }

            // 软删除
            department.IsDeleted = true;

            await _unitOfWork.Departments.UpdateAsync(department);
            await _unitOfWork.SaveChangesAsync();

            return true;
        }
    }
}
