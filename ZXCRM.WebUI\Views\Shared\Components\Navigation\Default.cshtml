@model ZXCRM.WebUI.Models.UserNavigationViewModel

<!-- Sidebar Menu -->
<nav class="mt-2">
    <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
        @foreach (var item in Model.VisibleNavigationItems)
        {
            var isActive = string.Equals(item.Controller, Model.CurrentController, StringComparison.OrdinalIgnoreCase);
            var linkClass = isActive ? "nav-link active" : "nav-link";

            <li class="nav-item">
                <a asp-controller="@item.Controller" asp-action="@item.Action" class="@linkClass">
                    <i class="nav-icon @item.Icon"></i>
                    <p>@item.Name</p>
                    @if (isActive)
                    {
                        <span class="right badge badge-primary">当前</span>
                    }
                </a>
            </li>
        }

        @* 如果用户没有任何模块权限，显示提示信息 *@
        @if (Model.VisibleNavigationItems.Count <= 1) // 只有仪表盘
        {
            <li class="nav-item">
                <div class="nav-link text-muted">
                    <i class="nav-icon fas fa-info-circle"></i>
                    <p>
                        <small>请联系管理员<br>分配模块权限</small>
                    </p>
                </div>
            </li>
        }
    </ul>
</nav>

@* 权限调试信息（仅在开发环境显示） *@
@if (ViewContext.HttpContext.RequestServices.GetService<IWebHostEnvironment>()?.IsDevelopment() == true)
{
    <div class="mt-3 px-3">
        <div class="card card-outline card-info collapsed-card">
            <div class="card-header">
                <h3 class="card-title">
                    <small><i class="fas fa-bug"></i> 权限调试</small>
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool btn-sm" data-card-widget="collapse">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body" style="display: none;">
                <div class="text-xs">
                    <strong>可见模块 (@Model.VisibleNavigationItems.Count):</strong>
                    <ul class="list-unstyled ml-2">
                        @foreach (var item in Model.VisibleNavigationItems)
                        {
                            <li>
                                <i class="@item.Icon"></i> @item.Name
                                @if (!item.IsAlwaysVisible)
                                {
                                    <small class="text-muted">(@item.ModuleType)</small>
                                }
                            </li>
                        }
                    </ul>

                    <strong>用户权限:</strong>
                    <div class="row">
                        @foreach (var permission in Model.UserPermissions.Where(p => p.Value))
                        {
                            <div class="col-12">
                                <small class="badge badge-success badge-sm">@permission.Key</small>
                            </div>
                        }
                    </div>

                    @if (!Model.UserPermissions.Any(p => p.Value))
                    {
                        <small class="text-warning">
                            <i class="fas fa-exclamation-triangle"></i> 未检测到任何权限
                        </small>
                    }
                </div>
            </div>
        </div>
    </div>
}

@* 添加一些样式优化 *@
<style>
    .nav-sidebar .nav-item .nav-link.active {
        background-color: #007bff;
        color: white;
    }

    .nav-sidebar .nav-item .nav-link.active .nav-icon {
        color: white;
    }

    .nav-sidebar .nav-item .nav-link:hover {
        background-color: rgba(255,255,255,.1);
    }

    .nav-sidebar .nav-item .nav-link.text-muted {
        cursor: default;
    }

    .nav-sidebar .nav-item .nav-link.text-muted:hover {
        background-color: transparent;
    }
</style>
