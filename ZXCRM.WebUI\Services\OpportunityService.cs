using ZXCRM.Service.DTOs;
using ZXCRM.WebUI.Models;

namespace ZXCRM.WebUI.Services
{
    public interface IOpportunityService
    {
        Task<ApiResponse<List<OpportunityDTO>>> GetOpportunitiesAsync();
        Task<ApiResponse<List<OpportunityDTO>>> GetAllOpportunitiesAsync();
        Task<ApiResponse<OpportunityDTO>> GetOpportunityByIdAsync(int id);
        Task<ApiResponse<OpportunityDTO>> CreateOpportunityAsync(CreateOpportunityDTO request);
        Task<ApiResponse<OpportunityDTO>> UpdateOpportunityAsync(int id, UpdateOpportunityDTO request);
        Task<ApiResponse<object>> DeleteOpportunityAsync(int id);
        Task<ApiResponse<List<OpportunityDTO>>> GetOpportunitiesByUserIdAsync(int userId);
    }

    public class OpportunityService : IOpportunityService
    {
        private readonly IApiService _apiService;
        private readonly ILogger<OpportunityService> _logger;

        public OpportunityService(IApiService apiService, ILogger<OpportunityService> logger)
        {
            _apiService = apiService;
            _logger = logger;
        }

        public async Task<ApiResponse<List<OpportunityDTO>>> GetOpportunitiesAsync()
        {
            try
            {
                _logger.LogInformation("Getting opportunities from API");
                var response = await _apiService.GetAsync<List<OpportunityDTO>>("api/opportunity");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting opportunities");
                return new ApiResponse<List<OpportunityDTO>>
                {
                    Success = false,
                    Message = "获取商机列表失败",
                    Data = new List<OpportunityDTO>()
                };
            }
        }

        public async Task<ApiResponse<List<OpportunityDTO>>> GetAllOpportunitiesAsync()
        {
            try
            {
                _logger.LogInformation("Getting all opportunities from API");
                var response = await _apiService.GetAsync<List<OpportunityDTO>>("api/opportunity/all");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all opportunities");
                return new ApiResponse<List<OpportunityDTO>>
                {
                    Success = false,
                    Message = "获取所有商机列表失败",
                    Data = new List<OpportunityDTO>()
                };
            }
        }

        public async Task<ApiResponse<OpportunityDTO>> GetOpportunityByIdAsync(int id)
        {
            try
            {
                _logger.LogInformation("Getting opportunity by ID: {OpportunityId}", id);
                var response = await _apiService.GetAsync<OpportunityDTO>($"api/opportunity/{id}");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting opportunity by ID: {OpportunityId}", id);
                return new ApiResponse<OpportunityDTO>
                {
                    Success = false,
                    Message = "获取商机详情失败",
                    Data = null
                };
            }
        }

        public async Task<ApiResponse<OpportunityDTO>> CreateOpportunityAsync(CreateOpportunityDTO request)
        {
            try
            {
                _logger.LogInformation("Creating opportunity: {OpportunityName}", request.Name);
                var response = await _apiService.PostAsync<OpportunityDTO>("api/opportunity", request);
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating opportunity: {OpportunityName}", request.Name);
                return new ApiResponse<OpportunityDTO>
                {
                    Success = false,
                    Message = "创建商机失败",
                    Data = null
                };
            }
        }

        public async Task<ApiResponse<OpportunityDTO>> UpdateOpportunityAsync(int id, UpdateOpportunityDTO request)
        {
            try
            {
                _logger.LogInformation("Updating opportunity: {OpportunityId}", id);
                var response = await _apiService.PutAsync<OpportunityDTO>($"api/opportunity/{id}", request);
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating opportunity: {OpportunityId}", id);
                return new ApiResponse<OpportunityDTO>
                {
                    Success = false,
                    Message = "更新商机失败",
                    Data = null
                };
            }
        }

        public async Task<ApiResponse<object>> DeleteOpportunityAsync(int id)
        {
            try
            {
                _logger.LogInformation("Deleting opportunity: {OpportunityId}", id);
                var response = await _apiService.DeleteAsync<object>($"api/opportunity/{id}");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting opportunity: {OpportunityId}", id);
                return new ApiResponse<object>
                {
                    Success = false,
                    Message = "删除商机失败",
                    Data = null
                };
            }
        }

        public async Task<ApiResponse<List<OpportunityDTO>>> GetOpportunitiesByUserIdAsync(int userId)
        {
            try
            {
                _logger.LogInformation("Getting opportunities by user: {UserId}", userId);
                var response = await _apiService.GetAsync<List<OpportunityDTO>>($"api/opportunity/user/{userId}");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting opportunities by user: {UserId}", userId);
                return new ApiResponse<List<OpportunityDTO>>
                {
                    Success = false,
                    Message = "获取用户商机失败",
                    Data = new List<OpportunityDTO>()
                };
            }
        }
    }
}
