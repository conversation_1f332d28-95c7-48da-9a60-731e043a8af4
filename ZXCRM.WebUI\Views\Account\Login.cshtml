@model LoginViewModel
@{
    Layout = null;
    ViewData["Title"] = "登录";
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - ZXCRM企业运营数据管理系统</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- AdminLTE CSS -->
    <link href="https://cdn.jsdelivr.net/npm/admin-lte@3.2.0/dist/css/adminlte.min.css" rel="stylesheet">

    <style>
        .login-page {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .login-box {
            width: 400px;
        }
        .login-logo a {
            color: #fff;
            font-size: 2.1rem;
            font-weight: 300;
            text-decoration: none;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        .input-group.focus {
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            border-radius: 0.25rem;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: none;
        }
        .btn-loading {
            font-size: 0.9rem;
        }
        .login-card-body {
            padding: 2rem;
        }
    </style>
</head>
<body class="hold-transition login-page">
    <div class="login-box">
        <!-- Logo -->
        <div class="login-logo">
            <a href="#"><b>ZXCRM</b>企业运营数据管理系统</a>
        </div>

        <!-- Login Card -->
        <div class="card">
            <div class="card-body login-card-body">
                <p class="login-box-msg">请登录以访问系统</p>

                <!-- Alert Messages -->
                @if (!ViewData.ModelState.IsValid)
                {
                    <div class="alert alert-danger">
                        @foreach (var error in ViewData.ModelState.Values.SelectMany(v => v.Errors))
                        {
                            <div>@error.ErrorMessage</div>
                        }
                    </div>
                }

                <form asp-action="Login" method="post">
                    <input asp-for="ReturnUrl" type="hidden" />

                    <div class="input-group mb-3">
                        <input asp-for="Username" class="form-control" placeholder="用户名" autocomplete="username">
                        <div class="input-group-append">
                            <div class="input-group-text">
                                <span class="fas fa-user"></span>
                            </div>
                        </div>
                    </div>
                    <span asp-validation-for="Username" class="text-danger"></span>

                    <div class="input-group mb-3">
                        <input asp-for="Password" class="form-control" placeholder="密码" autocomplete="current-password">
                        <div class="input-group-append">
                            <div class="input-group-text">
                                <span class="fas fa-lock"></span>
                            </div>
                        </div>
                    </div>
                    <span asp-validation-for="Password" class="text-danger"></span>

                    <div class="row">
                        <div class="col-8">
                            <div class="icheck-primary">
                                <input asp-for="RememberMe" type="checkbox" id="remember">
                                <label for="remember">
                                    记住我
                                </label>
                            </div>
                        </div>
                        <div class="col-4">
                            <button type="submit" class="btn btn-primary btn-block" id="loginBtn">
                                <span class="btn-text">登录</span>
                                <span class="btn-loading d-none">
                                    <i class="fas fa-spinner fa-spin"></i> 登录中...
                                </span>
                            </button>
                        </div>
                    </div>
                </form>

                <div class="mt-3 text-center">
                    <small class="text-muted">
                        默认用户名: admin, 密码: admin123<br>
                        勾选"记住我"可在下次访问时自动填充登录信息
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="https://cdn.jsdelivr.net/npm/admin-lte@3.2.0/dist/js/adminlte.min.js"></script>

    <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery-validation-unobtrusive@3.2.12/dist/jquery.validate.unobtrusive.min.js"></script>

    <script>
        $(document).ready(function() {
            // 登录表单提交处理
            $('form').on('submit', function() {
                var $btn = $('#loginBtn');
                var $btnText = $btn.find('.btn-text');
                var $btnLoading = $btn.find('.btn-loading');

                // 显示加载状态
                $btnText.addClass('d-none');
                $btnLoading.removeClass('d-none');
                $btn.prop('disabled', true);

                // 如果表单验证失败，恢复按钮状态
                setTimeout(function() {
                    if (!$('form').valid()) {
                        $btnText.removeClass('d-none');
                        $btnLoading.addClass('d-none');
                        $btn.prop('disabled', false);
                    }
                }, 100);
            });

            // 输入框焦点效果
            $('.form-control').on('focus', function() {
                $(this).parent().addClass('focus');
            }).on('blur', function() {
                $(this).parent().removeClass('focus');
            });

            // 处理记住的登录信息自动填充
            var rememberedUsername = '@Html.Raw(Model.Username)';
            var rememberedPassword = '@Html.Raw(Model.Password)';
            var hasRememberedInfo = rememberedUsername && rememberedUsername.length > 0;

            if (hasRememberedInfo) {
                // 模拟用户手动输入用户名
                setTimeout(function() {
                    typeText('#Username', rememberedUsername, function() {
                        // 用户名输入完成后，模拟输入密码
                        if (rememberedPassword) {
                            setTimeout(function() {
                                typePassword('#Password', rememberedPassword);
                            }, 300);
                        }
                    });
                }, 500);
            } else {
                // 如果没有记住的信息，自动聚焦到用户名输入框
                $('#Username').focus();
            }

            // 回车键快捷登录
            $('.form-control').on('keypress', function(e) {
                if (e.which === 13) { // Enter键
                    $('form').submit();
                }
            });

            // 模拟打字效果的函数
            function typeText(selector, text, callback) {
                var $input = $(selector);
                $input.val('').focus();
                var i = 0;

                function typeChar() {
                    if (i < text.length) {
                        $input.val($input.val() + text.charAt(i));
                        i++;
                        setTimeout(typeChar, 100); // 每个字符间隔100ms
                    } else if (callback) {
                        callback();
                    }
                }

                typeChar();
            }

            // 模拟密码输入效果的函数
            function typePassword(selector, password) {
                var $input = $(selector);
                $input.val('').focus();
                var i = 0;

                function typeChar() {
                    if (i < password.length) {
                        $input.val($input.val() + password.charAt(i));
                        i++;
                        setTimeout(typeChar, 80); // 密码输入稍快一些
                    }
                }

                typeChar();
            }
        });
    </script>
</body>
</html>
