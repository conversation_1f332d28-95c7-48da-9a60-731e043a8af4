using System.ComponentModel.DataAnnotations;
using ZXCRM.Service.DTOs;

namespace ZXCRM.WebUI.Models.ViewModels
{
    public class ReportIndexViewModel
    {
        public DashboardStatsDTO? DashboardStats { get; set; }
        public ReportQueryViewModel Query { get; set; } = new();
        public List<DepartmentSelectItem> Departments { get; set; } = new();
        public List<UserSelectItem> Users { get; set; } = new();
    }

    public class ReportQueryViewModel
    {
        [Display(Name = "开始日期")]
        public DateTime? StartDate { get; set; }

        [Display(Name = "结束日期")]
        public DateTime? EndDate { get; set; }

        [Display(Name = "部门")]
        public int? DepartmentId { get; set; }

        [Display(Name = "用户")]
        public int? UserId { get; set; }

        [Display(Name = "币种")]
        public string? Currency { get; set; }

        [Display(Name = "状态")]
        public string? Status { get; set; }

        public ReportQueryDTO ToDTO()
        {
            return new ReportQueryDTO
            {
                StartDate = StartDate,
                EndDate = EndDate,
                DepartmentId = DepartmentId,
                UserId = UserId,
                Currency = Currency,
                Status = Status
            };
        }
    }

    public class OpportunityReportViewModel
    {
        public OpportunityReportDTO? Report { get; set; }
        public ReportQueryViewModel Query { get; set; } = new();
        public List<DepartmentSelectItem> Departments { get; set; } = new();
        public List<UserSelectItem> Users { get; set; } = new();
    }

    public class OrderReportViewModel
    {
        public OrderReportDTO? Report { get; set; }
        public ReportQueryViewModel Query { get; set; } = new();
        public List<DepartmentSelectItem> Departments { get; set; } = new();
        public List<UserSelectItem> Users { get; set; } = new();
        public List<string> Currencies { get; set; } = new() { "RMB", "USD", "JPY", "EUR" };
    }

    public class PaymentReportViewModel
    {
        public PaymentReportDTO? Report { get; set; }
        public ReportQueryViewModel Query { get; set; } = new();
        public List<string> Currencies { get; set; } = new() { "RMB", "USD", "JPY", "EUR" };
    }

    public class InvoiceReportViewModel
    {
        public InvoiceReportDTO? Report { get; set; }
        public ReportQueryViewModel Query { get; set; } = new();
    }

    public class ReportDashboardViewModel
    {
        public DashboardStatsDTO? Stats { get; set; }
        public object? Summary { get; set; }
        public object? Trends { get; set; }
        public ReportQueryViewModel Query { get; set; } = new();
    }

    // 报表类型枚举
    public static class ReportTypes
    {
        public const string Dashboard = "dashboard";
        public const string Opportunity = "opportunity";
        public const string Order = "order";
        public const string Payment = "payment";
        public const string Invoice = "invoice";

        public static List<ReportTypeItem> GetAllTypes()
        {
            return new List<ReportTypeItem>
            {
                new() { Value = Dashboard, Name = "仪表盘", Icon = "fas fa-tachometer-alt", Color = "primary" },
                new() { Value = Opportunity, Name = "商机报表", Icon = "fas fa-seedling", Color = "success" },
                new() { Value = Order, Name = "订单报表", Icon = "fas fa-shopping-cart", Color = "info" },
                new() { Value = Payment, Name = "款项报表", Icon = "fas fa-money-bill", Color = "warning" },
                new() { Value = Invoice, Name = "发票报表", Icon = "fas fa-file-invoice", Color = "danger" }
            };
        }
    }

    public class ReportTypeItem
    {
        public string Value { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
    }

    // 商机状态选项
    public static class OpportunityStatusOptions
    {
        public static List<string> GetAllStatuses()
        {
            return new List<string>
            {
                "潜在", "合格", "方案", "谈判", "成交", "失败", "取消"
            };
        }
    }

    // 订单状态选项
    public static class OrderStatusOptions
    {
        public static List<string> GetAllStatuses()
        {
            return new List<string>
            {
                "草稿", "已确认", "执行中", "已完成", "已取消"
            };
        }
    }

    // 款项类型选项
    public static class PaymentTypeOptions
    {
        public static List<string> GetAllTypes()
        {
            return new List<string>
            {
                "首付款", "二期款", "三期款", "四期款", "五期款", "尾款", "全款"
            };
        }
    }

    // 发票类型选项
    public static class InvoiceTypeOptions
    {
        public static List<string> GetAllTypes()
        {
            return new List<string>
            {
                "增值税专用发票", "增值税普通发票", "收据"
            };
        }
    }

    // 发票公司选项
    public static class InvoiceCompanyOptions
    {
        public static List<string> GetAllCompanies()
        {
            return new List<string>
            {
                "A公司", "B公司", "C公司"
            };
        }
    }

    // 税率选项
    public static class TaxRateOptions
    {
        public static List<decimal> GetAllRates()
        {
            return new List<decimal>
            {
                0, 6, 13, 16
            };
        }
    }
}
