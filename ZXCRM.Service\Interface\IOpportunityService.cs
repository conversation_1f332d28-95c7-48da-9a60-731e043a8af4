using ZXCRM.Service.DTOs;

namespace ZXCRM.Service.Interface
{
    public interface IOpportunityService
    {
        Task<IEnumerable<OpportunityDTO>> GetAllOpportunitiesAsync();
        Task<IEnumerable<OpportunityDTO>> GetOpportunitiesByUserIdAsync(int userId);
        Task<OpportunityDTO?> GetOpportunityByIdAsync(int id);
        Task<OpportunityDTO> CreateOpportunityAsync(CreateOpportunityDTO createOpportunityDto);
        Task<OpportunityDTO?> UpdateOpportunityAsync(UpdateOpportunityDTO updateOpportunityDto);
        Task<bool> DeleteOpportunityAsync(int id);
    }
}
