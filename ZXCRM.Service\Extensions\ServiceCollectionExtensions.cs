using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using ZXCRM.Data.Context;
using ZXCRM.Data.UnitOfWork;
using ZXCRM.Service.Interface;
using ZXCRM.Service.Mappings;
using ZXCRM.Service.Service;

namespace ZXCRM.Service.Extensions
{
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddZXCRMServices(this IServiceCollection services, IConfiguration configuration)
        {
            // 添加数据库上下文
            services.AddDbContext<ZXCRMDbContext>(options =>
                options.UseSqlServer(configuration.GetConnectionString("DefaultConnection")));

            // 添加工作单元
            services.AddScoped<IUnitOfWork, UnitOfWork>();

            // 添加AutoMapper
            services.AddAutoMapper(typeof(AutoMapperProfile), typeof(MappingProfile));

            // 添加服务
            services.AddScoped<IAuthService, AuthService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IDepartmentService, DepartmentService>();
            services.AddScoped<IOpportunityService, OpportunityService>();
            services.AddScoped<IOrderService, OrderService>();
            services.AddScoped<IPaymentService, PaymentService>();
            services.AddScoped<IInvoiceService, InvoiceService>();
            services.AddScoped<IReportService, ReportService>();
            services.AddScoped<IPermissionService, PermissionService>();
            services.AddScoped<IUserPermissionService, UserPermissionService>();
            services.AddScoped<IPermissionCheckService, ZXCRM.Service.Services.PermissionCheckService>();

            return services;
        }
    }
}
