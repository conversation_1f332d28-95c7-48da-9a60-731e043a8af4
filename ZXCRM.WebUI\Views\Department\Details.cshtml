@model ZXCRM.WebUI.Models.ViewModels.DepartmentDetailViewModel
@using ZXCRM.WebUI.Helpers
@{
    ViewData["Title"] = "部门详情";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">部门详情</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a asp-controller="Dashboard" asp-action="Index">首页</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Department" asp-action="Index">部门管理</a></li>
                    <li class="breadcrumb-item active">部门详情</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <!-- 部门基本信息 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-building"></i> 基本信息
                        </h3>
                        <div class="card-tools">
                            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning btn-sm">
                                <i class="fas fa-edit"></i> 编辑
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">部门名称:</dt>
                                    <dd class="col-sm-8">
                                        <strong>@Model.Name</strong>
                                    </dd>

                                    <dt class="col-sm-4">状态:</dt>
                                    <dd class="col-sm-8">
                                        @if (Model.IsActive)
                                        {
                                            <span class="badge bg-success text-white">
                                                <i class="fas fa-check"></i> 启用
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-danger text-white">
                                                <i class="fas fa-times"></i> 禁用
                                            </span>
                                        }
                                    </dd>
                                </dl>
                            </div>
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">创建时间:</dt>
                                    <dd class="col-sm-8">
                                        <i class="fas fa-calendar-plus text-success"></i>
                                        @Model.CreatedAt.ToString("yyyy年MM月dd日 HH:mm:ss")
                                    </dd>

                                    <dt class="col-sm-4">更新时间:</dt>
                                    <dd class="col-sm-8">
                                        <i class="fas fa-calendar-edit text-warning"></i>
                                        @Model.UpdatedAt.ToString("yyyy年MM月dd日 HH:mm:ss")
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 部门人员列表 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-users"></i> 部门人员 (@Model.Users.Count 人)
                        </h3>
                        <div class="card-tools">
                            <a asp-controller="User" asp-action="Create" class="btn btn-primary btn-sm">
                                <i class="fas fa-user-plus"></i> 添加人员
                            </a>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        @if (Model.Users.Any())
                        {
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>用户名</th>
                                            <th>姓名</th>
                                            <th>邮箱</th>
                                            <th>角色</th>
                                            <th>状态</th>
                                            <th>加入时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var user in Model.Users)
                                        {
                                            <tr>
                                                <td>@user.Username</td>
                                                <td>@user.Name</td>
                                                <td>
                                                    @if (!string.IsNullOrEmpty(user.Email))
                                                    {
                                                        <a href="mailto:@user.Email">@user.Email</a>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">未设置</span>
                                                    }
                                                </td>
                                                <td>
                                                    <span class="badge bg-info text-white">@user.Role</span>
                                                </td>
                                                <td>
                                                    <span class="badge <EMAIL>(user.Status) text-white">
                                                        @EnumHelper.GetUserStatusText(user.Status)
                                                    </span>
                                                </td>
                                                <td>
                                                    <small class="text-muted">
                                                        @user.CreatedAt.ToString("yyyy-MM-dd")
                                                    </small>
                                                </td>
                                                <td>
                                                    <a asp-controller="User" asp-action="Details" asp-route-id="@user.Id" class="btn btn-info btn-xs">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a asp-controller="User" asp-action="Edit" asp-route-id="@user.Id" class="btn btn-warning btn-xs">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-4">
                                <i class="fas fa-users fa-2x text-muted mb-2"></i>
                                <p class="text-muted">该部门暂无人员</p>
                                <a asp-controller="User" asp-action="Create" class="btn btn-primary btn-sm">
                                    <i class="fas fa-user-plus"></i> 添加第一个人员
                                </a>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <!-- 操作面板 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-cogs"></i> 操作
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning btn-block">
                                <i class="fas fa-edit"></i> 编辑部门
                            </a>

                            <button type="button" class="btn btn-danger btn-block" onclick="confirmDelete(@Model.Id, '@Model.Name')">
                                <i class="fas fa-trash"></i> 删除部门
                            </button>

                            <hr>

                            <a asp-action="Index" class="btn btn-secondary btn-block">
                                <i class="fas fa-arrow-left"></i> 返回列表
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-bar"></i> 统计信息
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="info-box">
                            <span class="info-box-icon bg-info">
                                <i class="fas fa-users"></i>
                            </span>
                            <div class="info-box-content">
                                <span class="info-box-text">总人数</span>
                                <span class="info-box-number">@Model.TotalUsers</span>
                            </div>
                        </div>

                        <div class="info-box">
                            <span class="info-box-icon bg-success">
                                <i class="fas fa-user-check"></i>
                            </span>
                            <div class="info-box-content">
                                <span class="info-box-text">活跃人数</span>
                                <span class="info-box-number">@Model.ActiveUsers</span>
                            </div>
                        </div>

                        <div class="info-box">
                            <span class="info-box-icon bg-warning">
                                <i class="fas fa-shopping-cart"></i>
                            </span>
                            <div class="info-box-content">
                                <span class="info-box-text">订单数量</span>
                                <span class="info-box-number">@Model.TotalOrders</span>
                            </div>
                        </div>

                        <div class="info-box">
                            <span class="info-box-icon bg-danger">
                                <i class="fas fa-dollar-sign"></i>
                            </span>
                            <div class="info-box-content">
                                <span class="info-box-text">总营收</span>
                                <span class="info-box-number">¥@Model.TotalRevenue.ToString("N2")</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>您确定要删除部门 <strong id="deleteDepartmentName"></strong> 吗？</p>
                <p class="text-danger"><small>删除部门前请确保该部门下没有用户！</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(departmentId, departmentName) {
            $('#deleteDepartmentName').text(departmentName);
            $('#deleteForm').attr('action', '@Url.Action("Delete", "Department")/' + departmentId);
            $('#deleteModal').modal('show');
        }
    </script>
}
