@{
    ViewData["Title"] = "权限不足";
    var returnUrl = Context.Request.Query["returnUrl"].ToString();
}

<div class="access-denied-container">
    <div class="access-denied-content">
        <div class="access-denied-icon">
            <i class="fas fa-shield-alt"></i>
        </div>
        
        <div class="access-denied-message">
            <h1>权限不足</h1>
            <p class="lead">抱歉，您没有访问此功能的权限</p>
        </div>

        <div class="access-denied-details">
            <div class="alert alert-warning">
                <h5><i class="fas fa-exclamation-triangle"></i> 访问被拒绝</h5>
                <p>您尝试访问的功能需要特定的权限。请联系系统管理员为您分配相应的权限。</p>
                
                @if (!string.IsNullOrEmpty(returnUrl))
                {
                    <hr>
                    <p class="mb-0">
                        <strong>请求的页面：</strong> 
                        <code>@returnUrl</code>
                    </p>
                }
            </div>
        </div>

        <div class="access-denied-actions">
            <div class="btn-group" role="group">
                <a href="/" class="btn btn-primary">
                    <i class="fas fa-home"></i> 返回首页
                </a>
                <button type="button" class="btn btn-secondary" onclick="history.back()">
                    <i class="fas fa-arrow-left"></i> 返回上页
                </button>
                <a href="/Account/Logout" class="btn btn-outline-secondary">
                    <i class="fas fa-sign-out-alt"></i> 退出登录
                </a>
            </div>
        </div>

        <div class="access-denied-help">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-question-circle"></i> 需要帮助？
                    </h6>
                </div>
                <div class="card-body">
                    <p class="card-text">如果您认为这是一个错误，或者您需要访问此功能，请：</p>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-user-shield text-primary"></i> 联系系统管理员申请权限</li>
                        <li><i class="fas fa-envelope text-info"></i> 发送邮件说明您的业务需求</li>
                        <li><i class="fas fa-phone text-success"></i> 拨打技术支持电话</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="access-denied-info">
            <small class="text-muted">
                <i class="fas fa-info-circle"></i>
                错误代码: 403 | 时间: @DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            </small>
        </div>
    </div>
</div>

<style>
    .access-denied-container {
        min-height: 80vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
    }

    .access-denied-content {
        max-width: 600px;
        text-align: center;
    }

    .access-denied-icon {
        font-size: 5rem;
        color: #dc3545;
        margin-bottom: 30px;
    }

    .access-denied-message h1 {
        color: #dc3545;
        font-size: 2.5rem;
        margin-bottom: 15px;
    }

    .access-denied-message .lead {
        font-size: 1.25rem;
        color: #6c757d;
        margin-bottom: 30px;
    }

    .access-denied-details {
        margin-bottom: 30px;
        text-align: left;
    }

    .access-denied-actions {
        margin-bottom: 30px;
    }

    .access-denied-actions .btn {
        margin: 0 5px 10px 5px;
    }

    .access-denied-help {
        margin-bottom: 30px;
        text-align: left;
    }

    .access-denied-help .card {
        border: 1px solid #dee2e6;
    }

    .access-denied-help .card-header {
        background-color: #f8f9fa;
    }

    .access-denied-help ul li {
        padding: 5px 0;
    }

    .access-denied-help ul li i {
        margin-right: 10px;
        width: 20px;
    }

    .access-denied-info {
        border-top: 1px solid #dee2e6;
        padding-top: 20px;
    }

    @@media (max-width: 768px) {
        .access-denied-icon {
            font-size: 3rem;
        }

        .access-denied-message h1 {
            font-size: 2rem;
        }

        .access-denied-actions .btn {
            display: block;
            width: 100%;
            margin: 5px 0;
        }

        .btn-group {
            flex-direction: column;
        }
    }
</style>
