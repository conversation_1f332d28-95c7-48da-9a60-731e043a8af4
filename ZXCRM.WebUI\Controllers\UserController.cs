using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ZXCRM.Service.DTOs;
using ZXCRM.WebUI.Models.ViewModels;
using ZXCRM.WebUI.Services;

namespace ZXCRM.WebUI.Controllers
{
    [Authorize]
    public class UserController : BaseController
    {
        private readonly IUserService _userService;
        private readonly ILogger<UserController> _logger;

        public UserController(IUserService userService, ILogger<UserController> logger)
        {
            _userService = userService;
            _logger = logger;
        }

        // GET: User
        public async Task<IActionResult> Index(string searchTerm = "", int pageIndex = 1, int pageSize = 25)
        {
            try
            {
                // 验证分页参数
                pageIndex = Math.Max(1, pageIndex);
                pageSize = Math.Max(1, Math.Min(100, pageSize)); // 限制最大100条每页

                _logger.LogInformation("Getting users list. SearchTerm: {SearchTerm}, PageIndex: {PageIndex}, PageSize: {PageSize}",
                    searchTerm, pageIndex, pageSize);

                var response = await _userService.GetUsersAsync();

                if (!response.Success || response.Data == null)
                {
                    _logger.LogWarning("Failed to get users: {Message}", response.Message);
                    TempData["ErrorMessage"] = response.Message ?? "获取用户列表失败";
                    return View(new UserListViewModel());
                }

                // 应用搜索过滤
                var filteredUsers = response.Data.AsQueryable();
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    filteredUsers = filteredUsers.Where(u =>
                        u.Username.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        u.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        u.DepartmentName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase));
                }

                // 应用分页
                var totalCount = filteredUsers.Count();
                var users = filteredUsers
                    .Skip((pageIndex - 1) * pageSize)
                    .Take(pageSize)
                    .Select(u => new UserItemViewModel
                    {
                        Id = u.Id,
                        Username = u.Username,
                        Name = u.Name,
                        Email = u.Email ?? string.Empty,
                        DepartmentName = u.DepartmentName,
                        Role = u.Role,
                        Status = u.Status,
                        CreatedAt = u.CreatedAt
                    })
                    .ToList();

                var viewModel = new UserListViewModel
                {
                    Users = users,
                    TotalCount = totalCount,
                    PageIndex = pageIndex,
                    PageSize = pageSize,
                    SearchTerm = searchTerm
                };

                _logger.LogInformation("Successfully retrieved {Count} users", users.Count);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting users list");
                TempData["ErrorMessage"] = "获取用户列表时发生错误";
                return View(new UserListViewModel());
            }
        }

        // GET: User/Profile - 查看当前用户信息
        public async Task<IActionResult> Profile()
        {
            try
            {
                // 从Claims中获取当前用户ID
                var userIdClaim = User.FindFirst("UserId")?.Value;
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                {
                    _logger.LogWarning("Current user ID not found in claims");
                    TempData["ErrorMessage"] = "无法获取当前用户信息";
                    return RedirectToAction("Index", "Dashboard");
                }

                _logger.LogInformation("Getting current user profile for ID: {UserId}", userId);

                var response = await _userService.GetUserByIdAsync(userId);

                if (!response.Success || response.Data == null)
                {
                    _logger.LogWarning("Current user not found: {UserId}", userId);
                    TempData["ErrorMessage"] = "用户信息不存在";
                    return RedirectToAction("Index", "Dashboard");
                }

                var viewModel = new UserProfileViewModel
                {
                    Id = response.Data.Id,
                    Username = response.Data.Username,
                    Name = response.Data.Name,
                    Email = response.Data.Email ?? string.Empty,
                    Phone = response.Data.Phone ?? string.Empty,
                    Gender = response.Data.Gender ?? string.Empty,
                    DepartmentId = response.Data.DepartmentId,
                    DepartmentName = response.Data.DepartmentName,
                    Role = "用户", // 暂时硬编码
                    IsActive = true, // 暂时硬编码
                    CreatedAt = response.Data.CreatedAt,
                    UpdatedAt = response.Data.UpdatedAt
                };

                _logger.LogInformation("Successfully retrieved current user profile for ID: {UserId}", userId);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting current user profile");
                TempData["ErrorMessage"] = "获取用户信息时发生错误";
                return RedirectToAction("Index", "Dashboard");
            }
        }

        // GET: User/Details/5
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                _logger.LogInformation("Getting user details for ID: {UserId}", id);

                var response = await _userService.GetUserByIdAsync(id);

                if (!response.Success || response.Data == null)
                {
                    _logger.LogWarning("User not found: {UserId}", id);
                    TempData["ErrorMessage"] = "用户不存在";
                    return RedirectToAction(nameof(Index));
                }

                var viewModel = new UserDetailViewModel
                {
                    Id = response.Data.Id,
                    Username = response.Data.Username,
                    Name = response.Data.Name,
                    Email = response.Data.Email ?? string.Empty,
                    Phone = response.Data.Phone ?? string.Empty,
                    Gender = response.Data.Gender ?? string.Empty,
                    DepartmentId = response.Data.DepartmentId,
                    DepartmentName = response.Data.DepartmentName,
                    Role = response.Data.Role,
                    Status = response.Data.Status,
                    CreatedAt = response.Data.CreatedAt,
                    UpdatedAt = response.Data.UpdatedAt
                };

                _logger.LogInformation("Successfully retrieved user details for ID: {UserId}", id);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user details for ID: {UserId}", id);
                TempData["ErrorMessage"] = "获取用户详情时发生错误";
                return RedirectToAction(nameof(Index));
            }
        }

        // GET: User/Create
        public async Task<IActionResult> Create()
        {
            try
            {
                var viewModel = new CreateUserViewModel();
                await LoadDepartmentsAndRoles(viewModel);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading create user page");
                TempData["ErrorMessage"] = "加载创建用户页面时发生错误";
                return RedirectToAction(nameof(Index));
            }
        }

        // POST: User/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CreateUserViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    await LoadDepartmentsAndRoles(model);
                    return View(model);
                }

                _logger.LogInformation("Creating new user: {Username}", model.Username);

                var createUserDto = new CreateUserDTO
                {
                    Username = model.Username,
                    Password = model.Password,
                    Name = model.Name,
                    Email = string.IsNullOrWhiteSpace(model.Email) ? null : model.Email,
                    Phone = string.IsNullOrWhiteSpace(model.Phone) ? null : model.Phone,
                    Gender = string.IsNullOrWhiteSpace(model.Gender) ? null : model.Gender,
                    DepartmentId = model.DepartmentId
                };

                var response = await _userService.CreateUserAsync(createUserDto);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully created user: {Username}", model.Username);
                    TempData["SuccessMessage"] = "用户创建成功";
                    return RedirectToAction(nameof(Index));
                }
                else
                {
                    _logger.LogWarning("Failed to create user: {Username}. Message: {Message}", model.Username, response.Message);
                    ModelState.AddModelError(string.Empty, response.Message ?? "创建用户失败");
                    await LoadDepartmentsAndRoles(model);
                    return View(model);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user: {Username}", model.Username);
                ModelState.AddModelError(string.Empty, "创建用户时发生错误");
                await LoadDepartmentsAndRoles(model);
                return View(model);
            }
        }

        // GET: User/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            try
            {
                _logger.LogInformation("Loading edit user page for ID: {UserId}", id);

                var response = await _userService.GetUserByIdAsync(id);

                if (!response.Success || response.Data == null)
                {
                    _logger.LogWarning("User not found for edit: {UserId}", id);
                    TempData["ErrorMessage"] = "用户不存在";
                    return RedirectToAction(nameof(Index));
                }

                var viewModel = new EditUserViewModel
                {
                    Id = response.Data.Id,
                    Username = response.Data.Username,
                    Name = response.Data.Name,
                    Email = response.Data.Email ?? string.Empty,
                    Phone = response.Data.Phone ?? string.Empty,
                    Gender = response.Data.Gender ?? string.Empty,
                    DepartmentId = response.Data.DepartmentId,
                    Role = response.Data.Role,
                    IsActive = response.Data.Status == "Active" // 从Status转换为IsActive
                };

                await LoadDepartmentsAndRoles(viewModel);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading edit user page for ID: {UserId}", id);
                TempData["ErrorMessage"] = "加载编辑用户页面时发生错误";
                return RedirectToAction(nameof(Index));
            }
        }

        // POST: User/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, EditUserViewModel model)
        {
            try
            {
                if (id != model.Id)
                {
                    TempData["ErrorMessage"] = "用户ID不匹配";
                    return RedirectToAction(nameof(Index));
                }

                if (!ModelState.IsValid)
                {
                    await LoadDepartmentsAndRoles(model);
                    return View(model);
                }

                _logger.LogInformation("Updating user: {UserId}", id);

                var updateUserDto = new UpdateUserDTO
                {
                    Id = model.Id,
                    Name = model.Name,
                    Email = string.IsNullOrWhiteSpace(model.Email) ? null : model.Email,
                    Phone = string.IsNullOrWhiteSpace(model.Phone) ? null : model.Phone,
                    Gender = string.IsNullOrWhiteSpace(model.Gender) ? null : model.Gender,
                    DepartmentId = model.DepartmentId,
                    Password = !string.IsNullOrEmpty(model.NewPassword) ? model.NewPassword : null
                };

                var response = await _userService.UpdateUserAsync(id, updateUserDto);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully updated user: {UserId}", id);
                    TempData["SuccessMessage"] = "用户更新成功";
                    return RedirectToAction(nameof(Index));
                }
                else
                {
                    _logger.LogWarning("Failed to update user: {UserId}. Message: {Message}", id, response.Message);
                    ModelState.AddModelError(string.Empty, response.Message ?? "更新用户失败");
                    await LoadDepartmentsAndRoles(model);
                    return View(model);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user: {UserId}", id);
                ModelState.AddModelError(string.Empty, "更新用户时发生错误");
                await LoadDepartmentsAndRoles(model);
                return View(model);
            }
        }

        // POST: User/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                _logger.LogInformation("Deleting user: {UserId}", id);

                var response = await _userService.DeleteUserAsync(id);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully deleted user: {UserId}", id);
                    TempData["SuccessMessage"] = "用户删除成功";
                }
                else
                {
                    _logger.LogWarning("Failed to delete user: {UserId}. Message: {Message}", id, response.Message);
                    TempData["ErrorMessage"] = response.Message ?? "删除用户失败";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user: {UserId}", id);
                TempData["ErrorMessage"] = "删除用户时发生错误";
            }

            return RedirectToAction(nameof(Index));
        }

        private async Task LoadDepartmentsAndRoles<T>(T viewModel) where T : class
        {
            try
            {
                // 加载部门列表
                var departmentsResponse = await _userService.GetDepartmentsAsync();
                var departments = new List<DepartmentSelectItem>();

                if (departmentsResponse.Success && departmentsResponse.Data != null)
                {
                    departments = departmentsResponse.Data.Select(d => new DepartmentSelectItem
                    {
                        Id = d.Id,
                        Name = d.Name
                    }).ToList();
                }

                // 加载角色列表（暂时硬编码）
                var roles = new List<RoleSelectItem>
                {
                    new RoleSelectItem { Value = "用户", Text = "用户" },
                    new RoleSelectItem { Value = "管理员", Text = "管理员" }
                };

                // 使用反射设置属性
                var departmentsProperty = typeof(T).GetProperty("Departments");
                var rolesProperty = typeof(T).GetProperty("Roles");

                departmentsProperty?.SetValue(viewModel, departments);
                rolesProperty?.SetValue(viewModel, roles);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading departments and roles");
            }
        }
    }
}
