using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ZXCRM.Service.Interface;
using ZXCRM.Service.DTOs;

namespace ZXCRM.WebAPI.Controllers
{
    [Authorize]
    public class PermissionController : BaseController
    {
        private readonly IPermissionService _permissionService;
        private readonly ILogger<PermissionController> _logger;

        public PermissionController(IPermissionService permissionService, ILogger<PermissionController> logger)
        {
            _permissionService = permissionService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> GetAllPermissions()
        {
            try
            {
                _logger.LogInformation("Getting all permissions");
                var permissions = await _permissionService.GetAllAsync();
                return Success(permissions, "获取权限列表成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting permissions");
                return Failure("获取权限列表失败");
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetPermissionById(int id)
        {
            try
            {
                _logger.LogInformation("Getting permission by ID: {PermissionId}", id);
                var permission = await _permissionService.GetByIdAsync(id);
                if (permission == null)
                {
                    return Failure("权限不存在");
                }
                return Success(permission, "获取权限详情成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting permission by ID: {PermissionId}", id);
                return Failure("获取权限详情失败");
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreatePermission([FromBody] PermissionDto permissionDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Failure("请求数据无效", ModelState);
                }

                _logger.LogInformation("Creating permission: {PermissionName}", permissionDto.Name);
                var result = await _permissionService.CreateAsync(permissionDto);
                return Success(result, "创建权限成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating permission: {PermissionName}", permissionDto.Name);
                return Failure("创建权限失败");
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdatePermission(int id, [FromBody] PermissionDto permissionDto)
        {
            try
            {
                if (id != permissionDto.Id)
                {
                    return Failure("权限ID不匹配");
                }

                if (!ModelState.IsValid)
                {
                    return Failure("请求数据无效", ModelState);
                }

                _logger.LogInformation("Updating permission: {PermissionId}", id);
                var result = await _permissionService.UpdateAsync(permissionDto);
                return Success(result, "更新权限成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating permission: {PermissionId}", id);
                return Failure("更新权限失败");
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeletePermission(int id)
        {
            try
            {
                _logger.LogInformation("Deleting permission: {PermissionId}", id);
                await _permissionService.DeleteAsync(id);
                return Success(null, "删除权限成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting permission: {PermissionId}", id);
                return Failure("删除权限失败，可能存在关联的用户权限");
            }
        }
    }
}
