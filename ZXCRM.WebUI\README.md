# ZXCRM.WebUI - ASP.NET Core MVC Web界面

## 项目概述

ZXCRM.WebUI是ZXCRM企业运营数据管理系统的Web用户界面，基于ASP.NET Core MVC框架开发，提供现代化的企业级管理后台界面。

## 技术栈

- **框架**: ASP.NET Core 8.0 MVC
- **前端**: Bootstrap 5 + AdminLTE 3 + Font Awesome
- **图表**: Chart.js
- **认证**: Cookie Authentication
- **HTTP客户端**: HttpClientFactory
- **日志**: ASP.NET Core Logging

## 项目结构

```
ZXCRM.WebUI/
├── Controllers/           # 控制器
│   ├── BaseController.cs     # 基础控制器
│   ├── AccountController.cs  # 认证控制器
│   └── DashboardController.cs # 仪表盘控制器
├── Models/               # 模型
│   └── ViewModels/          # 视图模型
├── Services/             # 服务层
│   ├── IApiService.cs       # API服务接口
│   ├── ApiService.cs        # API服务实现
│   ├── IAuthService.cs      # 认证服务接口
│   ├── AuthService.cs       # 认证服务实现
│   └── ...                  # 其他业务服务
├── Views/                # 视图
│   ├── Shared/              # 共享视图
│   ├── Account/             # 认证相关视图
│   └── Dashboard/           # 仪表盘视图
├── wwwroot/              # 静态资源
│   ├── css/                 # 样式文件
│   ├── js/                  # JavaScript文件
│   └── img/                 # 图片文件
├── Program.cs            # 应用程序入口点
└── appsettings.json      # 配置文件
```

## 主要功能

### 1. 用户认证
- 基于Cookie的身份认证
- 登录/登出功能
- 记住我功能
- 访问控制

### 2. 仪表盘
- 数据概览卡片
- 图表展示
- 最近活动列表
- 快速操作按钮

### 3. 用户管理
- 用户列表
- 新增/编辑用户
- 用户详情查看
- 用户状态管理

### 4. 系统管理
- 部门管理
- 角色权限管理
- 系统设置

## 配置说明

### appsettings.json

```json
{
  "ApiSettings": {
    "BaseUrl": "http://localhost:5000/",
    "Timeout": 30
  },
  "Authentication": {
    "CookieName": "ZXCRM.Auth",
    "ExpireTimeSpan": "08:00:00"
  }
}
```

### 环境变量

- `ASPNETCORE_ENVIRONMENT`: 运行环境 (Development/Production)
- `ASPNETCORE_URLS`: 监听地址

## 运行说明

### 前置条件

1. .NET 8.0 SDK
2. ZXCRM.WebAPI项目正在运行 (http://localhost:5000)

### 启动步骤

1. 克隆项目到本地
2. 打开命令行，导航到项目目录
3. 运行以下命令：

```bash
# 还原NuGet包
dotnet restore

# 编译项目
dotnet build

# 运行项目
dotnet run
```

4. 打开浏览器访问 `https://localhost:5001` 或 `http://localhost:5000`

### 默认登录信息

- 用户名: admin
- 密码: admin123

## 开发说明

### 添加新功能

1. **创建控制器**: 继承自`BaseController`
2. **创建服务**: 实现相应的业务逻辑
3. **创建视图模型**: 定义数据传输对象
4. **创建视图**: 使用Razor语法创建UI
5. **注册服务**: 在`Program.cs`中注册依赖注入

### 样式指南

- 使用Bootstrap 5的CSS类
- 遵循AdminLTE的设计规范
- 保持响应式设计
- 使用Font Awesome图标

### API调用

所有API调用都通过`IApiService`进行，支持：
- GET/POST/PUT/DELETE请求
- 自动错误处理
- 认证token管理
- 请求日志记录

## 部署说明

### IIS部署

1. 发布项目：`dotnet publish -c Release`
2. 将发布文件复制到IIS目录
3. 配置IIS应用程序池（.NET Core）
4. 设置环境变量和配置文件

### Docker部署

```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["ZXCRM.WebUI/ZXCRM.WebUI.csproj", "ZXCRM.WebUI/"]
RUN dotnet restore "ZXCRM.WebUI/ZXCRM.WebUI.csproj"
COPY . .
WORKDIR "/src/ZXCRM.WebUI"
RUN dotnet build "ZXCRM.WebUI.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "ZXCRM.WebUI.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "ZXCRM.WebUI.dll"]
```

## 故障排除

### 常见问题

1. **无法连接到API**
   - 检查API服务是否运行
   - 验证`appsettings.json`中的API地址
   - 检查防火墙设置

2. **登录失败**
   - 验证用户名和密码
   - 检查API认证端点
   - 查看浏览器开发者工具的网络请求

3. **页面样式异常**
   - 检查CDN资源是否可访问
   - 验证静态文件服务配置
   - 清除浏览器缓存

### 日志查看

开发环境下，日志会输出到控制台。生产环境建议配置文件日志或结构化日志。

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

本项目采用MIT许可证。
