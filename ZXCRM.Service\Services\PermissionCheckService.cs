using Microsoft.Extensions.Logging;
using ZXCRM.Data.UnitOfWork;
using ZXCRM.Service.DTOs;
using ZXCRM.Service.Interfaces;

namespace ZXCRM.Service.Services
{
    public class PermissionCheckService : IPermissionCheckService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<PermissionCheckService> _logger;

        // 定义模块类型
        private readonly string[] _businessModules = {
            "商机管理", "订单管理", "款项管理", "发票管理", "报表统计"
        };

        private readonly string[] _systemModules = {
            "用户管理", "部门管理", "权限管理"
        };

        public PermissionCheckService(IUnitOfWork unitOfWork, ILogger<PermissionCheckService> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        /// <summary>
        /// 检查用户是否有特定权限
        /// </summary>
        public async Task<bool> HasPermissionAsync(int userId, string moduleType, string permissionCode)
        {
            try
            {
                // 检查是否是超级管理员
                if (await IsSuperAdminAsync(userId))
                {
                    _logger.LogInformation("User {UserId} is SuperAdmin, granting permission {ModuleType}.{PermissionCode}", 
                        userId, moduleType, permissionCode);
                    return true;
                }

                // 获取权限
                var permissions = await _unitOfWork.Permissions.FindAsync(p => p.Code == permissionCode);
                var permission = permissions.FirstOrDefault();
                if (permission == null)
                {
                    _logger.LogWarning("Permission code {PermissionCode} not found", permissionCode);
                    return false;
                }

                // 检查用户权限
                var userPermissions = await _unitOfWork.UserPermissions.FindAsync(
                    up => up.UserId == userId && up.PermissionId == permission.Id && up.ModuleType == moduleType);

                var hasPermission = userPermissions.Any();

                _logger.LogDebug("User {UserId} permission check: {ModuleType}.{PermissionCode} = {HasPermission}", 
                    userId, moduleType, permissionCode, hasPermission);

                return hasPermission;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking permission for user {UserId}: {ModuleType}.{PermissionCode}", 
                    userId, moduleType, permissionCode);
                return false;
            }
        }

        /// <summary>
        /// 检查用户是否有模块访问权限（至少有一个权限）
        /// </summary>
        public async Task<bool> HasModuleAccessAsync(int userId, string moduleType)
        {
            try
            {
                // 检查是否是超级管理员
                if (await IsSuperAdminAsync(userId))
                {
                    return true;
                }

                // 获取用户在该模块的所有权限
                var userPermissions = await _unitOfWork.UserPermissions.FindAsync(
                    up => up.UserId == userId && up.ModuleType == moduleType);

                var hasAccess = userPermissions.Any();

                _logger.LogDebug("User {UserId} module access check: {ModuleType} = {HasAccess}", 
                    userId, moduleType, hasAccess);

                return hasAccess;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking module access for user {UserId}: {ModuleType}", 
                    userId, moduleType);
                return false;
            }
        }

        /// <summary>
        /// 获取用户有权限的模块列表
        /// </summary>
        public async Task<List<string>> GetUserModulesAsync(int userId)
        {
            try
            {
                _logger.LogInformation("Getting user modules for user {UserId}", userId);

                // 检查是否是超级管理员
                var isSuperAdmin = await IsSuperAdminAsync(userId);
                if (isSuperAdmin)
                {
                    var allModules = _businessModules.Concat(_systemModules).ToList();
                    _logger.LogInformation("User {UserId} is SuperAdmin, returning all modules: {Modules}",
                        userId, string.Join(", ", allModules));
                    return allModules;
                }

                // 获取用户权限
                _logger.LogInformation("User {UserId} is not SuperAdmin, checking database permissions", userId);
                var userPermissions = await _unitOfWork.UserPermissions.FindAsync(up => up.UserId == userId);

                _logger.LogInformation("Found {Count} user permissions for user {UserId}",
                    userPermissions.Count(), userId);

                if (userPermissions.Any())
                {
                    foreach (var up in userPermissions)
                    {
                        _logger.LogInformation("User {UserId} has permission: PermissionId={PermissionId}, ModuleType={ModuleType}",
                            userId, up.PermissionId, up.ModuleType);
                    }
                }

                var userModules = userPermissions
                    .Select(up => up.ModuleType)
                    .Distinct()
                    .ToList();

                _logger.LogInformation("User {UserId} has access to modules: {Modules}",
                    userId, string.Join(", ", userModules));

                return userModules;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user modules for user {UserId}", userId);
                return new List<string>();
            }
        }

        /// <summary>
        /// 获取用户的完整权限映射
        /// </summary>
        public async Task<Dictionary<string, List<string>>> GetUserPermissionsAsync(int userId)
        {
            try
            {
                var result = new Dictionary<string, List<string>>();

                // 检查是否是超级管理员
                if (await IsSuperAdminAsync(userId))
                {
                    var allPermissions = new List<string> { "Query", "Create", "Edit", "Delete" };
                    foreach (var module in _businessModules.Concat(_systemModules))
                    {
                        result[module] = allPermissions;
                    }
                    return result;
                }

                // 获取用户权限
                var userPermissions = await _unitOfWork.UserPermissions.FindAsync(up => up.UserId == userId);
                var allPermissionTypes = await _unitOfWork.Permissions.GetAllAsync();

                foreach (var up in userPermissions)
                {
                    var permission = allPermissionTypes.FirstOrDefault(p => p.Id == up.PermissionId);
                    if (permission != null)
                    {
                        if (!result.ContainsKey(up.ModuleType))
                        {
                            result[up.ModuleType] = new List<string>();
                        }
                        result[up.ModuleType].Add(permission.Code);
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user permissions for user {UserId}", userId);
                return new Dictionary<string, List<string>>();
            }
        }

        /// <summary>
        /// 判断是否是系统模块
        /// </summary>
        public bool IsSystemModule(string moduleType)
        {
            return _systemModules.Contains(moduleType);
        }

        /// <summary>
        /// 判断是否是业务模块
        /// </summary>
        public bool IsBusinessModule(string moduleType)
        {
            return _businessModules.Contains(moduleType);
        }

        /// <summary>
        /// 获取用户权限概览
        /// </summary>
        public async Task<UserPermissionOverviewDto> GetUserPermissionOverviewAsync(int userId)
        {
            try
            {
                var user = await _unitOfWork.Users.GetByIdAsync(userId);
                if (user == null)
                {
                    throw new ArgumentException($"User with ID {userId} not found");
                }

                var isSuperAdmin = await IsSuperAdminAsync(userId);
                var userModules = await GetUserModulesAsync(userId);
                var userPermissions = await GetUserPermissionsAsync(userId);

                var businessModules = userModules.Where(m => IsBusinessModule(m)).ToList();
                var systemModules = userModules.Where(m => IsSystemModule(m)).ToList();

                return new UserPermissionOverviewDto
                {
                    UserId = userId,
                    Username = user.Username,
                    Name = user.Name,
                    Role = user.Role,
                    IsSuperAdmin = isSuperAdmin,
                    BusinessModules = businessModules,
                    SystemModules = systemModules,
                    ModulePermissions = userPermissions,
                    HasBusinessAccess = businessModules.Any(),
                    HasSystemAccess = systemModules.Any()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user permission overview for user {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// 检查是否是超级管理员
        /// </summary>
        private async Task<bool> IsSuperAdminAsync(int userId)
        {
            try
            {
                _logger.LogInformation("Checking if user {UserId} is SuperAdmin", userId);
                var user = await _unitOfWork.Users.GetByIdAsync(userId);

                if (user == null)
                {
                    _logger.LogWarning("User {UserId} not found when checking SuperAdmin status", userId);
                    return false;
                }

                _logger.LogInformation("User {UserId} found: Username={Username}, Role={Role}",
                    userId, user.Username, user.Role);

                var isSuperAdmin = user.Role.Equals("SuperAdmin", StringComparison.OrdinalIgnoreCase);
                _logger.LogInformation("User {UserId} SuperAdmin check result: {IsSuperAdmin}", userId, isSuperAdmin);

                return isSuperAdmin;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if user {UserId} is SuperAdmin", userId);
                return false;
            }
        }
    }
}
