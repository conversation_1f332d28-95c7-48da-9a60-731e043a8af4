namespace ZXCRM.Data.Entities
{
    public class Payment : BaseEntity
    {
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public int OrderId { get; set; }
        public string PaymentType { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public decimal SettlementAmount { get; set; }
        public DateTime? ExpectedPaymentDate { get; set; }
        public DateTime? ActualPaymentDate { get; set; }
        public string PaymentStatus { get; set; } = "Pending";
        public string InvoiceStatus { get; set; } = string.Empty;
        public DateTime? InvoiceDate { get; set; }

        // 导航属性
        public Order Order { get; set; } = null!;
        public ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
    }
}
