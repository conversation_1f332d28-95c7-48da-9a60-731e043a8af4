{"ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=ZXCRM;User Id=sa;Password=*********;TrustServerCertificate=True;Connection Timeout=30;Command Timeout=60;Max Pool Size=100;Min Pool Size=5;Pooling=true;MultipleActiveResultSets=true;"}, "Jwt": {"Key": "ZXCRMSecretKeyForDevelopment12345678901234567890", "Issuer": "ZXCRM", "Audience": "ZXCRMClient"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Warning"}}, "Cache": {"DefaultExpiration": "00:30:00", "SlidingExpiration": "00:10:00"}, "Performance": {"EnableResponseCompression": true, "EnableResponseCaching": true, "MaxRequestBodySize": 10485760}, "AllowedHosts": "*"}