using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using ZXCRM.WebUI.Services;
using ZXCRM.Service.DTOs;
using ZXCRM.WebUI.Models.ViewModels;
using System.Text.Json;

namespace ZXCRM.WebUI.Controllers
{
    public class DebugController : Controller
    {
        private readonly IOrderService _orderService;
        private readonly IPaymentService _paymentService;
        private readonly IInvoiceService _invoiceService;
        private readonly ILogger<DebugController> _logger;

        public DebugController(
            IOrderService orderService,
            IPaymentService paymentService,
            IInvoiceService invoiceService,
            ILogger<DebugController> logger)
        {
            _orderService = orderService;
            _paymentService = paymentService;
            _invoiceService = invoiceService;
            _logger = logger;
        }

        /// <summary>
        /// 调试数据显示问题 - 检查API返回的原始数据
        /// </summary>
        public async Task<IActionResult> CheckData()
        {
            var result = new Dictionary<string, object>
            {
                ["Timestamp"] = DateTime.Now,
                ["Orders"] = new object(),
                ["Payments"] = new object(),
                ["Invoices"] = new object()
            };

            try
            {
                // 检查订单数据
                var ordersResponse = await _orderService.GetOrdersAsync();
                if (ordersResponse.Success && ordersResponse.Data != null)
                {
                    var firstOrder = ordersResponse.Data.FirstOrDefault();
                    result["Orders"] = new
                    {
                        Success = ordersResponse.Success,
                        Count = ordersResponse.Data.Count,
                        FirstOrder = firstOrder != null ? new
                        {
                            firstOrder.Id,
                            firstOrder.Name,
                            firstOrder.Code,
                            firstOrder.Amount,
                            firstOrder.Currency,
                            firstOrder.Status,
                            firstOrder.CustomerName,
                            AmountIsZero = firstOrder.Amount == 0,
                            CurrencyIsEmpty = string.IsNullOrEmpty(firstOrder.Currency),
                            StatusIsEmpty = string.IsNullOrEmpty(firstOrder.Status)
                        } : null
                    };
                }
                else
                {
                    result["Orders"] = new
                    {
                        Success = ordersResponse.Success,
                        Message = ordersResponse.Message,
                        Error = "Failed to get orders"
                    };
                }

                // 检查款项数据
                var paymentsResponse = await _paymentService.GetPaymentsAsync();
                if (paymentsResponse.Success && paymentsResponse.Data != null)
                {
                    var firstPayment = paymentsResponse.Data.FirstOrDefault();
                    result["Payments"] = new
                    {
                        Success = paymentsResponse.Success,
                        Count = paymentsResponse.Data.Count,
                        FirstPayment = firstPayment != null ? new
                        {
                            firstPayment.Id,
                            firstPayment.Name,
                            firstPayment.Code,
                            firstPayment.PaymentType,
                            firstPayment.Amount,
                            firstPayment.Currency,
                            firstPayment.PaymentStatus,
                            firstPayment.InvoiceStatus,
                            PaymentTypeIsEmpty = string.IsNullOrEmpty(firstPayment.PaymentType),
                            AmountIsZero = firstPayment.Amount == 0,
                            CurrencyIsEmpty = string.IsNullOrEmpty(firstPayment.Currency),
                            PaymentStatusIsEmpty = string.IsNullOrEmpty(firstPayment.PaymentStatus),
                            InvoiceStatusIsEmpty = string.IsNullOrEmpty(firstPayment.InvoiceStatus)
                        } : null
                    };
                }
                else
                {
                    result["Payments"] = new
                    {
                        Success = paymentsResponse.Success,
                        Message = paymentsResponse.Message,
                        Error = "Failed to get payments"
                    };
                }

                // 检查发票数据
                var invoicesResponse = await _invoiceService.GetInvoicesAsync();
                if (invoicesResponse.Success && invoicesResponse.Data != null)
                {
                    var firstInvoice = invoicesResponse.Data.FirstOrDefault();
                    result["Invoices"] = new
                    {
                        Success = invoicesResponse.Success,
                        Count = invoicesResponse.Data.Count,
                        FirstInvoice = firstInvoice != null ? new
                        {
                            firstInvoice.Id,
                            firstInvoice.Company,
                            firstInvoice.Type,
                            firstInvoice.TaxRate,
                            firstInvoice.Amount,
                            firstInvoice.Status,
                            CompanyIsEmpty = string.IsNullOrEmpty(firstInvoice.Company),
                            TypeIsEmpty = string.IsNullOrEmpty(firstInvoice.Type),
                            TaxRateIsZero = firstInvoice.TaxRate == 0,
                            AmountIsZero = firstInvoice.Amount == 0,
                            StatusIsEmpty = string.IsNullOrEmpty(firstInvoice.Status)
                        } : null
                    };
                }
                else
                {
                    result["Invoices"] = new
                    {
                        Success = invoicesResponse.Success,
                        Message = invoicesResponse.Message,
                        Error = "Failed to get invoices"
                    };
                }

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in debug data check");
                result["Error"] = ex.Message;
                result["StackTrace"] = ex.StackTrace;
            }

            // 返回JSON格式的调试信息
            return Json(result, new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });
        }

        /// <summary>
        /// 检查单个订单的详细数据
        /// </summary>
        public async Task<IActionResult> CheckOrder(int id = 1)
        {
            try
            {
                var response = await _orderService.GetOrderByIdAsync(id);

                var result = new
                {
                    Timestamp = DateTime.Now,
                    OrderId = id,
                    Success = response.Success,
                    Message = response.Message,
                    Data = response.Data,
                    DataAnalysis = response.Data != null ? new
                    {
                        HasAmount = response.Data.Amount > 0,
                        HasCurrency = !string.IsNullOrEmpty(response.Data.Currency),
                        HasStatus = !string.IsNullOrEmpty(response.Data.Status),
                        HasCustomerName = !string.IsNullOrEmpty(response.Data.CustomerName),
                        AmountValue = response.Data.Amount,
                        CurrencyValue = response.Data.Currency,
                        StatusValue = response.Data.Status,
                        AllProperties = new
                        {
                            response.Data.Id,
                            response.Data.Name,
                            response.Data.Code,
                            response.Data.CustomerName,
                            response.Data.ContactName,
                            response.Data.ContactPhone,
                            response.Data.OpportunityId,
                            response.Data.OpportunityName,
                            response.Data.Amount,
                            response.Data.Currency,
                            response.Data.SettlementAmount,
                            response.Data.AccountManagerId,
                            response.Data.AccountManagerName,
                            response.Data.DepartmentId,
                            response.Data.DepartmentName,
                            response.Data.SignDate,
                            response.Data.Status,
                            response.Data.ProjectManagerId,
                            response.Data.ProjectManagerName,
                            response.Data.PerformanceDepartmentId,
                            response.Data.PerformanceDepartmentName,
                            response.Data.CreatedById,
                            response.Data.CreatedByName,
                            response.Data.CreatedAt,
                            response.Data.UpdatedAt
                        }
                    } : null
                };

                return Json(result, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking order {OrderId}", id);
                return Json(new
                {
                    Timestamp = DateTime.Now,
                    OrderId = id,
                    Error = ex.Message,
                    StackTrace = ex.StackTrace
                });
            }
        }

        /// <summary>
        /// 直接调用API检查原始数据
        /// </summary>
        public async Task<IActionResult> CheckApiRaw()
        {
            try
            {
                // 直接调用API服务检查原始响应
                var apiService = HttpContext.RequestServices.GetRequiredService<IApiService>();
                var response = await apiService.GetAsync<List<OrderDTO>>("api/order");

                var result = new
                {
                    Timestamp = DateTime.Now,
                    ApiResponse = new
                    {
                        response.Success,
                        response.Message,
                        DataCount = response.Data?.Count ?? 0,
                        FirstOrderRaw = response.Data?.FirstOrDefault()
                    }
                };

                return Json(result, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking API raw data");
                return Json(new
                {
                    Timestamp = DateTime.Now,
                    Error = ex.Message,
                    StackTrace = ex.StackTrace
                });
            }
        }

        /// <summary>
        /// 检查数据库原始数据（绕过AutoMapper）
        /// </summary>
        public async Task<IActionResult> CheckDatabaseRaw()
        {
            try
            {
                // 直接访问数据库上下文
                var dbContext = HttpContext.RequestServices.GetRequiredService<ZXCRM.Data.Context.ZXCRMDbContext>();

                // 查询第一个订单的原始数据
                var firstOrder = await dbContext.Orders
                    .Include(o => o.AccountManager)
                    .Include(o => o.Department)
                    .Include(o => o.CreatedBy)
                    .Include(o => o.ProjectManager)
                    .Include(o => o.PerformanceDepartment)
                    .Include(o => o.Opportunity)
                    .FirstOrDefaultAsync();

                var result = new
                {
                    Timestamp = DateTime.Now,
                    DatabaseData = firstOrder != null ? new
                    {
                        // 基本属性
                        firstOrder.Id,
                        firstOrder.Name,
                        firstOrder.Code,
                        firstOrder.CustomerName,
                        firstOrder.ContactName,
                        firstOrder.ContactPhone,
                        firstOrder.OpportunityId,
                        firstOrder.Amount,
                        firstOrder.Currency,
                        firstOrder.SettlementAmount,
                        firstOrder.AccountManagerId,
                        firstOrder.DepartmentId,
                        firstOrder.SignDate,
                        firstOrder.Status,
                        firstOrder.ProjectManagerId,
                        firstOrder.PerformanceDepartmentId,
                        firstOrder.CreatedById,
                        firstOrder.CreatedAt,
                        firstOrder.UpdatedAt,

                        // 导航属性
                        AccountManager = firstOrder.AccountManager != null ? new
                        {
                            firstOrder.AccountManager.Id,
                            firstOrder.AccountManager.Name,
                            firstOrder.AccountManager.Username
                        } : null,

                        Department = firstOrder.Department != null ? new
                        {
                            firstOrder.Department.Id,
                            firstOrder.Department.Name,
                            firstOrder.Department.Code
                        } : null,

                        CreatedBy = firstOrder.CreatedBy != null ? new
                        {
                            firstOrder.CreatedBy.Id,
                            firstOrder.CreatedBy.Name,
                            firstOrder.CreatedBy.Username
                        } : null,

                        ProjectManager = firstOrder.ProjectManager != null ? new
                        {
                            firstOrder.ProjectManager.Id,
                            firstOrder.ProjectManager.Name,
                            firstOrder.ProjectManager.Username
                        } : null,

                        PerformanceDepartment = firstOrder.PerformanceDepartment != null ? new
                        {
                            firstOrder.PerformanceDepartment.Id,
                            firstOrder.PerformanceDepartment.Name,
                            firstOrder.PerformanceDepartment.Code
                        } : null,

                        Opportunity = firstOrder.Opportunity != null ? new
                        {
                            firstOrder.Opportunity.Id,
                            firstOrder.Opportunity.Name,
                            firstOrder.Opportunity.CustomerName
                        } : null
                    } : null,

                    TotalOrdersCount = await dbContext.Orders.CountAsync()
                };

                return Json(result, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking database raw data");
                return Json(new
                {
                    Timestamp = DateTime.Now,
                    Error = ex.Message,
                    StackTrace = ex.StackTrace
                });
            }
        }

        /// <summary>
        /// 测试AutoMapper配置
        /// </summary>
        public async Task<IActionResult> TestAutoMapper()
        {
            try
            {
                // 获取AutoMapper
                var mapper = HttpContext.RequestServices.GetRequiredService<AutoMapper.IMapper>();

                // 获取数据库上下文
                var dbContext = HttpContext.RequestServices.GetRequiredService<ZXCRM.Data.Context.ZXCRMDbContext>();

                // 查询第一个订单
                var firstOrder = await dbContext.Orders
                    .Include(o => o.AccountManager)
                    .Include(o => o.Department)
                    .Include(o => o.CreatedBy)
                    .Include(o => o.ProjectManager)
                    .Include(o => o.PerformanceDepartment)
                    .Include(o => o.Opportunity)
                    .FirstOrDefaultAsync();

                if (firstOrder == null)
                {
                    return Json(new
                    {
                        Timestamp = DateTime.Now,
                        Error = "No orders found in database"
                    });
                }

                // 测试AutoMapper映射
                var mappedOrder = mapper.Map<OrderDTO>(firstOrder);

                var result = new
                {
                    Timestamp = DateTime.Now,
                    OriginalOrder = new
                    {
                        firstOrder.Id,
                        firstOrder.Name,
                        firstOrder.Code,
                        firstOrder.Amount,
                        firstOrder.Currency,
                        firstOrder.Status,
                        firstOrder.CustomerName,
                        AccountManagerName = firstOrder.AccountManager?.Name,
                        DepartmentName = firstOrder.Department?.Name,
                        CreatedByName = firstOrder.CreatedBy?.Name
                    },
                    MappedOrder = new
                    {
                        mappedOrder.Id,
                        mappedOrder.Name,
                        mappedOrder.Code,
                        mappedOrder.Amount,
                        mappedOrder.Currency,
                        mappedOrder.Status,
                        mappedOrder.CustomerName,
                        mappedOrder.AccountManagerName,
                        mappedOrder.DepartmentName,
                        mappedOrder.CreatedByName
                    },
                    MappingSuccess = mappedOrder != null,
                    PropertiesMatch = new
                    {
                        IdMatch = firstOrder.Id == mappedOrder.Id,
                        NameMatch = firstOrder.Name == mappedOrder.Name,
                        AmountMatch = firstOrder.Amount == mappedOrder.Amount,
                        CurrencyMatch = firstOrder.Currency == mappedOrder.Currency,
                        StatusMatch = firstOrder.Status == mappedOrder.Status,
                        AccountManagerNameMatch = firstOrder.AccountManager?.Name == mappedOrder.AccountManagerName,
                        DepartmentNameMatch = firstOrder.Department?.Name == mappedOrder.DepartmentName
                    }
                };

                return Json(result, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing AutoMapper");
                return Json(new
                {
                    Timestamp = DateTime.Now,
                    Error = ex.Message,
                    StackTrace = ex.StackTrace
                });
            }
        }

        /// <summary>
        /// 检查订单列表视图模型数据
        /// </summary>
        public async Task<IActionResult> CheckOrderListViewModel()
        {
            try
            {
                // 模拟OrderController的Index方法
                var orderService = HttpContext.RequestServices.GetRequiredService<IOrderService>();
                var response = await orderService.GetOrdersAsync();

                var result = new
                {
                    Timestamp = DateTime.Now,
                    ApiResponse = new
                    {
                        response.Success,
                        response.Message,
                        DataCount = response.Data?.Count ?? 0
                    },
                    FirstOrderData = response.Data?.FirstOrDefault(),
                    ViewModelMapping = response.Data?.FirstOrDefault() != null ? new
                    {
                        OriginalData = response.Data.First(),
                        ViewModelData = new
                        {
                            Id = response.Data.First().Id,
                            Name = response.Data.First().Name,
                            Code = response.Data.First().Code,
                            CustomerName = response.Data.First().CustomerName,
                            Amount = response.Data.First().Amount,
                            Currency = response.Data.First().Currency,
                            Status = response.Data.First().Status,
                            AccountManagerName = response.Data.First().AccountManagerName,
                            DepartmentName = response.Data.First().DepartmentName,
                            SignDate = response.Data.First().SignDate,
                            CreatedAt = response.Data.First().CreatedAt
                        }
                    } : null
                };

                return Json(result, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking order list view model");
                return Json(new
                {
                    Timestamp = DateTime.Now,
                    Error = ex.Message,
                    StackTrace = ex.StackTrace
                });
            }
        }

        /// <summary>
        /// 简单的数据显示测试页面
        /// </summary>
        public async Task<IActionResult> SimpleDataTest()
        {
            try
            {
                var orderService = HttpContext.RequestServices.GetRequiredService<IOrderService>();
                var response = await orderService.GetOrdersAsync();

                ViewBag.ApiResponse = response;
                ViewBag.FirstOrder = response.Data?.FirstOrDefault();

                return View();
            }
            catch (Exception ex)
            {
                ViewBag.Error = ex.Message;
                return View();
            }
        }

        /// <summary>
        /// 调试订单显示问题
        /// </summary>
        public async Task<IActionResult> OrderDisplayDebug()
        {
            try
            {
                _logger.LogInformation("=== Debug: 开始调试订单显示问题 ===");

                // 获取订单数据
                var orderService = HttpContext.RequestServices.GetRequiredService<IOrderService>();
                var response = await orderService.GetOrdersAsync();

                _logger.LogInformation("Debug: API响应 Success={Success}, DataCount={Count}",
                    response.Success, response.Data?.Count ?? 0);

                if (response.Success && response.Data != null && response.Data.Any())
                {
                    // 记录所有订单的关键数据
                    for (int i = 0; i < response.Data.Count; i++)
                    {
                        var order = response.Data[i];
                        _logger.LogInformation("Debug: 订单{Index} - ID={Id}, Amount={Amount}, Currency={Currency}, Status={Status}",
                            i + 1, order.Id, order.Amount, order.Currency ?? "NULL", order.Status ?? "NULL");
                    }
                }

                // 创建ViewModel
                var orders = response.Data ?? new List<OrderDTO>();
                var orderViewModels = orders.Select(o => new OrderItemViewModel
                {
                    Id = o.Id,
                    Name = o.Name,
                    Code = o.Code,
                    CustomerName = o.CustomerName,
                    Amount = o.Amount,
                    Currency = o.Currency,
                    Status = o.Status,
                    AccountManagerName = o.AccountManagerName,
                    DepartmentName = o.DepartmentName,
                    SignDate = o.SignDate,
                    CreatedAt = o.CreatedAt
                }).ToList();

                // 记录ViewModel数据
                if (orderViewModels.Any())
                {
                    for (int i = 0; i < orderViewModels.Count; i++)
                    {
                        var vm = orderViewModels[i];
                        _logger.LogInformation("Debug: ViewModel{Index} - ID={Id}, Amount={Amount}, Currency={Currency}, Status={Status}",
                            i + 1, vm.Id, vm.Amount, vm.Currency ?? "NULL", vm.Status ?? "NULL");
                    }
                }

                var viewModel = new OrderListViewModel
                {
                    Orders = orderViewModels,
                    TotalCount = orderViewModels.Count,
                    PageIndex = 1,
                    PageSize = 25,
                    SearchTerm = "",
                    Status = ""
                };

                _logger.LogInformation("=== Debug: 调试完成，返回视图 ===");
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Debug: 调试过程中发生错误");
                ViewBag.Error = ex.Message;
                return View(new OrderListViewModel());
            }
        }
    }
}
