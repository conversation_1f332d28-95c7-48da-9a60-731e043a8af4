using Microsoft.AspNetCore.Mvc;
using ZXCRM.WebUI.Services;
using System.Text.Json;

namespace ZXCRM.WebUI.Controllers
{
    public class DebugController : Controller
    {
        private readonly IOrderService _orderService;
        private readonly IPaymentService _paymentService;
        private readonly IInvoiceService _invoiceService;
        private readonly ILogger<DebugController> _logger;

        public DebugController(
            IOrderService orderService,
            IPaymentService paymentService,
            IInvoiceService invoiceService,
            ILogger<DebugController> logger)
        {
            _orderService = orderService;
            _paymentService = paymentService;
            _invoiceService = invoiceService;
            _logger = logger;
        }

        /// <summary>
        /// 调试数据显示问题 - 检查API返回的原始数据
        /// </summary>
        public async Task<IActionResult> CheckData()
        {
            var result = new Dictionary<string, object>
            {
                ["Timestamp"] = DateTime.Now,
                ["Orders"] = new object(),
                ["Payments"] = new object(),
                ["Invoices"] = new object()
            };

            try
            {
                // 检查订单数据
                var ordersResponse = await _orderService.GetOrdersAsync();
                if (ordersResponse.Success && ordersResponse.Data != null)
                {
                    var firstOrder = ordersResponse.Data.FirstOrDefault();
                    result["Orders"] = new
                    {
                        Success = ordersResponse.Success,
                        Count = ordersResponse.Data.Count,
                        FirstOrder = firstOrder != null ? new
                        {
                            firstOrder.Id,
                            firstOrder.Name,
                            firstOrder.Code,
                            firstOrder.Amount,
                            firstOrder.Currency,
                            firstOrder.Status,
                            firstOrder.CustomerName,
                            AmountIsZero = firstOrder.Amount == 0,
                            CurrencyIsEmpty = string.IsNullOrEmpty(firstOrder.Currency),
                            StatusIsEmpty = string.IsNullOrEmpty(firstOrder.Status)
                        } : null
                    };
                }
                else
                {
                    result["Orders"] = new
                    {
                        Success = ordersResponse.Success,
                        Message = ordersResponse.Message,
                        Error = "Failed to get orders"
                    };
                }

                // 检查款项数据
                var paymentsResponse = await _paymentService.GetPaymentsAsync();
                if (paymentsResponse.Success && paymentsResponse.Data != null)
                {
                    var firstPayment = paymentsResponse.Data.FirstOrDefault();
                    result["Payments"] = new
                    {
                        Success = paymentsResponse.Success,
                        Count = paymentsResponse.Data.Count,
                        FirstPayment = firstPayment != null ? new
                        {
                            firstPayment.Id,
                            firstPayment.Name,
                            firstPayment.Code,
                            firstPayment.PaymentType,
                            firstPayment.Amount,
                            firstPayment.Currency,
                            firstPayment.PaymentStatus,
                            firstPayment.InvoiceStatus,
                            PaymentTypeIsEmpty = string.IsNullOrEmpty(firstPayment.PaymentType),
                            AmountIsZero = firstPayment.Amount == 0,
                            CurrencyIsEmpty = string.IsNullOrEmpty(firstPayment.Currency),
                            PaymentStatusIsEmpty = string.IsNullOrEmpty(firstPayment.PaymentStatus),
                            InvoiceStatusIsEmpty = string.IsNullOrEmpty(firstPayment.InvoiceStatus)
                        } : null
                    };
                }
                else
                {
                    result["Payments"] = new
                    {
                        Success = paymentsResponse.Success,
                        Message = paymentsResponse.Message,
                        Error = "Failed to get payments"
                    };
                }

                // 检查发票数据
                var invoicesResponse = await _invoiceService.GetInvoicesAsync();
                if (invoicesResponse.Success && invoicesResponse.Data != null)
                {
                    var firstInvoice = invoicesResponse.Data.FirstOrDefault();
                    result["Invoices"] = new
                    {
                        Success = invoicesResponse.Success,
                        Count = invoicesResponse.Data.Count,
                        FirstInvoice = firstInvoice != null ? new
                        {
                            firstInvoice.Id,
                            firstInvoice.Company,
                            firstInvoice.Type,
                            firstInvoice.TaxRate,
                            firstInvoice.Amount,
                            firstInvoice.Status,
                            CompanyIsEmpty = string.IsNullOrEmpty(firstInvoice.Company),
                            TypeIsEmpty = string.IsNullOrEmpty(firstInvoice.Type),
                            TaxRateIsZero = firstInvoice.TaxRate == 0,
                            AmountIsZero = firstInvoice.Amount == 0,
                            StatusIsEmpty = string.IsNullOrEmpty(firstInvoice.Status)
                        } : null
                    };
                }
                else
                {
                    result["Invoices"] = new
                    {
                        Success = invoicesResponse.Success,
                        Message = invoicesResponse.Message,
                        Error = "Failed to get invoices"
                    };
                }

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in debug data check");
                result["Error"] = ex.Message;
                result["StackTrace"] = ex.StackTrace;
            }

            // 返回JSON格式的调试信息
            return Json(result, new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });
        }

        /// <summary>
        /// 检查单个订单的详细数据
        /// </summary>
        public async Task<IActionResult> CheckOrder(int id = 1)
        {
            try
            {
                var response = await _orderService.GetOrderByIdAsync(id);

                var result = new
                {
                    Timestamp = DateTime.Now,
                    OrderId = id,
                    Success = response.Success,
                    Message = response.Message,
                    Data = response.Data,
                    DataAnalysis = response.Data != null ? new
                    {
                        HasAmount = response.Data.Amount > 0,
                        HasCurrency = !string.IsNullOrEmpty(response.Data.Currency),
                        HasStatus = !string.IsNullOrEmpty(response.Data.Status),
                        HasCustomerName = !string.IsNullOrEmpty(response.Data.CustomerName),
                        AmountValue = response.Data.Amount,
                        CurrencyValue = response.Data.Currency,
                        StatusValue = response.Data.Status
                    } : null
                };

                return Json(result, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking order {OrderId}", id);
                return Json(new
                {
                    Timestamp = DateTime.Now,
                    OrderId = id,
                    Error = ex.Message,
                    StackTrace = ex.StackTrace
                });
            }
        }
    }
}
