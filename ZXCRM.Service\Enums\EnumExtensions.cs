using System;
using System.Collections.Generic;
using System.Linq;
using System.Globalization;

namespace ZXCRM.Service.Enums
{
    /// <summary>
    /// 枚举扩展方法
    /// </summary>
    public static class EnumExtensions
    {
        /// <summary>
        /// 获取枚举的显示文本
        /// </summary>
        /// <param name="value">枚举值</param>
        /// <returns>显示文本</returns>
        public static string GetDisplayText(this CurrencyType value)
        {
            switch (value)
            {
                case CurrencyType.CNY:
                    return "人民币";
                case CurrencyType.USD:
                    return "美元";
                case CurrencyType.JPY:
                    return "日元";
                case CurrencyType.EUR:
                    return "欧元";
                default:
                    return value.ToString();
            }
        }

        /// <summary>
        /// 获取枚举的显示文本
        /// </summary>
        /// <param name="value">枚举值</param>
        /// <returns>显示文本</returns>
        public static string GetDisplayText(this OrderStatus value)
        {
            switch (value)
            {
                case OrderStatus.New:
                    return "新建";
                case OrderStatus.InProgress:
                    return "进行中";
                case OrderStatus.Completed:
                    return "已完成";
                case OrderStatus.Cancelled:
                    return "已取消";
                default:
                    return value.ToString();
            }
        }

        /// <summary>
        /// 获取枚举的显示文本
        /// </summary>
        /// <param name="value">枚举值</param>
        /// <returns>显示文本</returns>
        public static string GetDisplayText(this OpportunityStatus value)
        {
            switch (value)
            {
                case OpportunityStatus.New:
                    return "新建";
                case OpportunityStatus.InProgress:
                    return "跟进中";
                case OpportunityStatus.Quoted:
                    return "已报价";
                case OpportunityStatus.Won:
                    return "已赢单";
                case OpportunityStatus.Lost:
                    return "已输单";
                case OpportunityStatus.Cancelled:
                    return "已取消";
                default:
                    return value.ToString();
            }
        }

        /// <summary>
        /// 获取枚举的显示文本
        /// </summary>
        /// <param name="value">枚举值</param>
        /// <returns>显示文本</returns>
        public static string GetDisplayText(this PaymentType value)
        {
            switch (value)
            {
                case PaymentType.FirstPayment:
                    return "首付";
                case PaymentType.SecondPayment:
                    return "2付";
                case PaymentType.ThirdPayment:
                    return "3付";
                case PaymentType.FourthPayment:
                    return "4付";
                case PaymentType.FifthPayment:
                    return "5付";
                case PaymentType.FinalPayment:
                    return "尾款";
                case PaymentType.FullPayment:
                    return "全款";
                default:
                    return value.ToString();
            }
        }

        /// <summary>
        /// 获取枚举的显示文本
        /// </summary>
        /// <param name="value">枚举值</param>
        /// <returns>显示文本</returns>
        public static string GetDisplayText(this InvoiceStatus value)
        {
            switch (value)
            {
                case InvoiceStatus.Pending:
                    return "待开票";
                case InvoiceStatus.Issued:
                    return "已开票";
                case InvoiceStatus.Cancelled:
                    return "已作废";
                default:
                    return value.ToString();
            }
        }

        /// <summary>
        /// 获取枚举的显示文本
        /// </summary>
        /// <param name="value">枚举值</param>
        /// <returns>显示文本</returns>
        public static string GetDisplayText(this InvoiceType value)
        {
            switch (value)
            {
                case InvoiceType.Regular:
                    return "增值税普通发票";
                case InvoiceType.Special:
                    return "增值税专用发票";
                case InvoiceType.Electronic:
                    return "电子发票";
                default:
                    return value.ToString();
            }
        }

        /// <summary>
        /// 获取枚举的显示文本
        /// </summary>
        /// <param name="value">枚举值</param>
        /// <returns>显示文本</returns>
        public static string GetDisplayText(this CompanyType value)
        {
            switch (value)
            {
                case CompanyType.LLC:
                    return "有限责任公司";
                case CompanyType.Corporation:
                    return "股份有限公司";
                case CompanyType.SoleProprietorship:
                    return "个人独资企业";
                case CompanyType.Partnership:
                    return "合伙企业";
                case CompanyType.ForeignInvestment:
                    return "外商投资企业";
                default:
                    return value.ToString();
            }
        }

        /// <summary>
        /// 获取枚举的显示文本
        /// </summary>
        /// <param name="value">枚举值</param>
        /// <returns>显示文本</returns>
        public static string GetDisplayText(this GenderType value)
        {
            switch (value)
            {
                case GenderType.Male:
                    return "男";
                case GenderType.Female:
                    return "女";
                default:
                    return value.ToString();
            }
        }

        /// <summary>
        /// 获取枚举的显示文本
        /// </summary>
        /// <param name="value">枚举值</param>
        /// <returns>显示文本</returns>
        public static string GetDisplayText(this TaxRateType value)
        {
            return ((int)value).ToString(CultureInfo.InvariantCulture) + "%";
        }

        /// <summary>
        /// 获取枚举的显示文本
        /// </summary>
        /// <param name="value">枚举值</param>
        /// <returns>显示文本</returns>
        public static string GetDisplayText(this UserStatus value)
        {
            switch (value)
            {
                case UserStatus.Active:
                    return "正常";
                case UserStatus.Inactive:
                    return "禁用";
                case UserStatus.Locked:
                    return "锁定";
                default:
                    return value.ToString();
            }
        }

        /// <summary>
        /// 获取枚举的显示文本
        /// </summary>
        /// <param name="value">枚举值</param>
        /// <returns>显示文本</returns>
        public static string GetDisplayText(this DepartmentStatus value)
        {
            switch (value)
            {
                case DepartmentStatus.Active:
                    return "正常";
                case DepartmentStatus.Inactive:
                    return "禁用";
                default:
                    return value.ToString();
            }
        }

        /// <summary>
        /// 获取枚举的显示文本
        /// </summary>
        /// <param name="value">枚举值</param>
        /// <returns>显示文本</returns>
        public static string GetDisplayText(this PaymentStatus value)
        {
            switch (value)
            {
                case PaymentStatus.Pending:
                    return "待回款";
                case PaymentStatus.Partial:
                    return "部分回款";
                case PaymentStatus.Completed:
                    return "已回款";
                case PaymentStatus.Overdue:
                    return "逾期";
                default:
                    return value.ToString();
            }
        }

        /// <summary>
        /// 获取枚举值列表
        /// </summary>
        /// <typeparam name="T">枚举类型</typeparam>
        /// <returns>枚举值列表</returns>
        public static List<T> GetEnumValues<T>() where T : Enum
        {
            return Enum.GetValues(typeof(T)).Cast<T>().ToList();
        }

        /// <summary>
        /// 获取枚举显示文本列表
        /// </summary>
        /// <typeparam name="T">枚举类型</typeparam>
        /// <returns>枚举显示文本列表</returns>
        public static List<string> GetEnumDisplayTexts<T>() where T : Enum
        {
            var values = GetEnumValues<T>();
            var result = new List<string>();

            foreach (var value in values)
            {
                if (value is CurrencyType currencyType)
                    result.Add(currencyType.GetDisplayText());
                else if (value is OrderStatus orderStatus)
                    result.Add(orderStatus.GetDisplayText());
                else if (value is OpportunityStatus opportunityStatus)
                    result.Add(opportunityStatus.GetDisplayText());
                else if (value is PaymentType paymentType)
                    result.Add(paymentType.GetDisplayText());
                else if (value is InvoiceStatus invoiceStatus)
                    result.Add(invoiceStatus.GetDisplayText());
                else if (value is InvoiceType invoiceType)
                    result.Add(invoiceType.GetDisplayText());
                else if (value is CompanyType companyType)
                    result.Add(companyType.GetDisplayText());
                else if (value is GenderType genderType)
                    result.Add(genderType.GetDisplayText());
                else if (value is TaxRateType taxRateType)
                    result.Add(taxRateType.GetDisplayText());
                else if (value is UserStatus userStatus)
                    result.Add(userStatus.GetDisplayText());
                else if (value is DepartmentStatus departmentStatus)
                    result.Add(departmentStatus.GetDisplayText());
                else if (value is PaymentStatus paymentStatus)
                    result.Add(paymentStatus.GetDisplayText());
                else
                    result.Add(value.ToString());
            }

            return result;
        }

        /// <summary>
        /// 从显示文本获取枚举值
        /// </summary>
        /// <typeparam name="T">枚举类型</typeparam>
        /// <param name="displayText">显示文本</param>
        /// <returns>枚举值</returns>
        public static T GetEnumFromDisplayText<T>(string displayText) where T : Enum
        {
            var values = GetEnumValues<T>();

            foreach (var value in values)
            {
                string valueDisplayText = string.Empty;

                if (value is CurrencyType currencyType)
                    valueDisplayText = currencyType.GetDisplayText();
                else if (value is OrderStatus orderStatus)
                    valueDisplayText = orderStatus.GetDisplayText();
                else if (value is OpportunityStatus opportunityStatus)
                    valueDisplayText = opportunityStatus.GetDisplayText();
                else if (value is PaymentType paymentType)
                    valueDisplayText = paymentType.GetDisplayText();
                else if (value is InvoiceStatus invoiceStatus)
                    valueDisplayText = invoiceStatus.GetDisplayText();
                else if (value is InvoiceType invoiceType)
                    valueDisplayText = invoiceType.GetDisplayText();
                else if (value is CompanyType companyType)
                    valueDisplayText = companyType.GetDisplayText();
                else if (value is GenderType genderType)
                    valueDisplayText = genderType.GetDisplayText();
                else if (value is TaxRateType taxRateType)
                    valueDisplayText = taxRateType.GetDisplayText();
                else if (value is UserStatus userStatus)
                    valueDisplayText = userStatus.GetDisplayText();
                else if (value is DepartmentStatus departmentStatus)
                    valueDisplayText = departmentStatus.GetDisplayText();
                else if (value is PaymentStatus paymentStatus)
                    valueDisplayText = paymentStatus.GetDisplayText();
                else
                    valueDisplayText = value.ToString();

                if (valueDisplayText == displayText)
                    return value;
            }

            return default;
        }
    }
}
