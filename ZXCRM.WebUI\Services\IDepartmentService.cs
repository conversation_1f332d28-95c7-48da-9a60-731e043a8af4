using ZXCRM.Service.DTOs;
using ZXCRM.WebUI.Models;

namespace ZXCRM.WebUI.Services
{
    public interface IDepartmentService
    {
        Task<ApiResponse<List<DepartmentDTO>>> GetDepartmentsAsync();
        Task<ApiResponse<List<DepartmentDTO>>> GetDepartmentTreeAsync();
        Task<ApiResponse<DepartmentDTO>> GetDepartmentByIdAsync(int id);
        Task<ApiResponse<DepartmentDTO>> CreateDepartmentAsync(CreateDepartmentDTO request);
        Task<ApiResponse<DepartmentDTO>> UpdateDepartmentAsync(int id, UpdateDepartmentDTO request);
        Task<ApiResponse<object>> DeleteDepartmentAsync(int id);
        Task<ApiResponse<List<UserDTO>>> GetDepartmentUsersAsync(int departmentId);
    }
}
