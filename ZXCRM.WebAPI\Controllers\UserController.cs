using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ZXCRM.Service.DTOs;
using ZXCRM.Service.Interface;

namespace ZXCRM.WebAPI.Controllers
{
    [Authorize]
    public class UserController : BaseController
    {
        private readonly IUserService _userService;

        public UserController(IUserService userService)
        {
            _userService = userService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAllUsers()
        {
            try
            {
                var users = await _userService.GetAllUsersAsync();
                return Success(users);
            }
            catch (Exception ex)
            {
                return Failure(ex.Message);
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetUserById(int id)
        {
            try
            {
                var user = await _userService.GetUserByIdAsync(id);
                if (user == null)
                {
                    return Failure("用户不存在");
                }
                return Success(user);
            }
            catch (Exception ex)
            {
                return Failure(ex.Message);
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreateUser([FromBody] CreateUserDTO createUserDto)
        {
            try
            {
                var user = await _userService.CreateUserAsync(createUserDto);
                return Success(user, "创建用户成功");
            }
            catch (Exception ex)
            {
                return Failure(ex.Message);
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateUser(int id, [FromBody] UpdateUserDTO updateUserDto)
        {
            try
            {
                var user = await _userService.UpdateUserAsync(updateUserDto);
                if (user == null)
                {
                    return Failure("用户不存在");
                }
                return Success(user, "更新用户成功");
            }
            catch (Exception ex)
            {
                return Failure(ex.Message);
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteUser(int id)
        {
            try
            {
                var result = await _userService.DeleteUserAsync(id);
                if (!result)
                {
                    return Failure("删除用户失败");
                }
                return Success(null, "删除用户成功");
            }
            catch (Exception ex)
            {
                return Failure(ex.Message);
            }
        }

        [HttpGet("check-permission")]
        public async Task<IActionResult> CheckPermission([FromQuery] string moduleType, [FromQuery] string permissionCode)
        {
            try
            {
                int userId = GetCurrentUserId();
                if (userId == 0)
                {
                    return Failure("未获取到用户ID");
                }

                var hasPermission = await _userService.CheckUserPermissionAsync(userId, moduleType, permissionCode);
                return Success(hasPermission);
            }
            catch (Exception ex)
            {
                return Failure(ex.Message);
            }
        }
    }
}
