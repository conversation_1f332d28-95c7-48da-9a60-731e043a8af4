using ZXCRM.Service.DTOs;

namespace ZXCRM.Service.Interface
{
    public interface IInvoiceService
    {
        Task<IEnumerable<InvoiceDTO>> GetAllInvoicesAsync();
        Task<IEnumerable<InvoiceDTO>> GetInvoicesByPaymentIdAsync(int paymentId);
        Task<InvoiceDTO?> GetInvoiceByIdAsync(int id);
        Task<InvoiceDTO> CreateInvoiceAsync(CreateInvoiceDTO createInvoiceDto);
        Task<InvoiceDTO?> UpdateInvoiceAsync(UpdateInvoiceDTO updateInvoiceDto);
        Task<bool> DeleteInvoiceAsync(int id);
        Task<decimal> GetValidInvoiceAmountByPaymentIdAsync(int paymentId);
    }
}
