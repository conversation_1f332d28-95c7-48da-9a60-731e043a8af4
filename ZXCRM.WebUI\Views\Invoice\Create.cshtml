@model ZXCRM.WebUI.Models.ViewModels.CreateInvoiceViewModel
@{
    ViewData["Title"] = "新增发票";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">新增发票</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a asp-controller="Dashboard" asp-action="Index">首页</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Invoice" asp-action="Index">发票管理</a></li>
                    <li class="breadcrumb-item active">新增发票</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-plus"></i> 发票信息
                        </h3>
                    </div>
                    <form asp-action="Create" method="post">
                        <div class="card-body">
                            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <!-- 关联款项 -->
                                    <div class="form-group">
                                        <label asp-for="PaymentId" class="form-label required"></label>
                                        <select asp-for="PaymentId" class="form-control">
                                            <option value="">请选择关联款项</option>
                                            @foreach (var payment in Model.Payments)
                                            {
                                                <option value="@payment.Id">
                                                    @payment.Name (@payment.Code) - @payment.OrderName - @payment.CustomerName - @payment.Amount.ToString("N2") @payment.Currency
                                                </option>
                                            }
                                        </select>
                                        <span asp-validation-for="PaymentId" class="text-danger"></span>
                                    </div>

                                    <!-- 发票公司 -->
                                    <div class="form-group">
                                        <label asp-for="Company" class="form-label required"></label>
                                        <select asp-for="Company" class="form-control">
                                            <option value="">请选择发票公司</option>
                                            <option value="A公司">A公司</option>
                                            <option value="B公司">B公司</option>
                                            <option value="C公司">C公司</option>
                                        </select>
                                        <span asp-validation-for="Company" class="text-danger"></span>
                                    </div>

                                    <!-- 发票类型 -->
                                    <div class="form-group">
                                        <label asp-for="Type" class="form-label required"></label>
                                        <select asp-for="Type" class="form-control">
                                            <option value="">请选择发票类型</option>
                                            <option value="增值税专用发票">增值税专用发票</option>
                                            <option value="增值税普通发票">增值税普通发票</option>
                                            <option value="收据">收据</option>
                                        </select>
                                        <span asp-validation-for="Type" class="text-danger"></span>
                                    </div>

                                    <!-- 发票税率 -->
                                    <div class="form-group">
                                        <label asp-for="TaxRate" class="form-label required"></label>
                                        <select asp-for="TaxRate" class="form-control">
                                            <option value="">请选择发票税率</option>
                                            <option value="16">16%</option>
                                            <option value="13">13%</option>
                                            <option value="6">6%</option>
                                            <option value="0">免税</option>
                                        </select>
                                        <span asp-validation-for="TaxRate" class="text-danger"></span>
                                    </div>

                                    <!-- 发票金额 -->
                                    <div class="form-group">
                                        <label asp-for="Amount" class="form-label required"></label>
                                        <input asp-for="Amount" class="form-control" placeholder="0.00" step="0.01" />
                                        <span asp-validation-for="Amount" class="text-danger"></span>
                                        <small class="form-text text-muted">发票金额不能超过款项金额</small>
                                    </div>

                                    <!-- 发票状态 -->
                                    <div class="form-group">
                                        <label asp-for="Status" class="form-label"></label>
                                        <select asp-for="Status" class="form-control">
                                            <option value="Normal">正常</option>
                                            <option value="Cancelled">已作废</option>
                                        </select>
                                        <span asp-validation-for="Status" class="text-danger"></span>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <!-- 发票内容 -->
                                    <div class="form-group">
                                        <label asp-for="Content" class="form-label"></label>
                                        <textarea asp-for="Content" class="form-control" rows="3" placeholder="请输入发票内容"></textarea>
                                        <span asp-validation-for="Content" class="text-danger"></span>
                                    </div>

                                    <!-- 发票代号 -->
                                    <div class="form-group">
                                        <label asp-for="Code" class="form-label"></label>
                                        <input asp-for="Code" class="form-control" placeholder="请输入发票代号" />
                                        <span asp-validation-for="Code" class="text-danger"></span>
                                    </div>

                                    <!-- 收件人 -->
                                    <div class="form-group">
                                        <label asp-for="ReceiverName" class="form-label"></label>
                                        <input asp-for="ReceiverName" class="form-control" placeholder="请输入收件人姓名" />
                                        <span asp-validation-for="ReceiverName" class="text-danger"></span>
                                    </div>

                                    <!-- 收件人电话 -->
                                    <div class="form-group">
                                        <label asp-for="ReceiverPhone" class="form-label"></label>
                                        <input asp-for="ReceiverPhone" class="form-control" placeholder="请输入收件人电话" />
                                        <span asp-validation-for="ReceiverPhone" class="text-danger"></span>
                                    </div>

                                    <!-- 邮寄地址 -->
                                    <div class="form-group">
                                        <label asp-for="MailingAddress" class="form-label"></label>
                                        <textarea asp-for="MailingAddress" class="form-control" rows="2" placeholder="请输入发票邮寄地址"></textarea>
                                        <span asp-validation-for="MailingAddress" class="text-danger"></span>
                                    </div>

                                    <!-- 申请人电话 -->
                                    <div class="form-group">
                                        <label asp-for="ApplicantPhone" class="form-label"></label>
                                        <input asp-for="ApplicantPhone" class="form-control" placeholder="请输入申请人电话" />
                                        <span asp-validation-for="ApplicantPhone" class="text-danger"></span>
                                    </div>

                                    <!-- 客户电子邮件 -->
                                    <div class="form-group">
                                        <label asp-for="CustomerEmail" class="form-label"></label>
                                        <input asp-for="CustomerEmail" class="form-control" type="email" placeholder="请输入客户电子邮件" />
                                        <span asp-validation-for="CustomerEmail" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card-footer">
                            <div class="row">
                                <div class="col-md-6">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> 保存
                                    </button>
                                    <a asp-action="Index" class="btn btn-secondary ml-2">
                                        <i class="fas fa-times"></i> 取消
                                    </a>
                                </div>
                                <div class="col-md-6 text-right">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i> 
                                        标有 <span class="text-danger">*</span> 的字段为必填项
                                    </small>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="col-md-4">
                <!-- 帮助信息 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-question-circle"></i> 填写说明
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb"></i> 提示</h6>
                            <ul class="mb-0">
                                <li>必须选择一个已存在的款项进行关联</li>
                                <li>发票金额不能超过款项金额</li>
                                <li>发票公司、类型、税率根据业务需求选择</li>
                                <li>收件人信息用于发票邮寄</li>
                                <li>发票代号建议填写便于查找</li>
                            </ul>
                        </div>

                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> 注意</h6>
                            <p class="mb-0">
                                创建发票后，系统会自动更新相关款项的开票状态。
                                发票金额总和不能超过款项金额。
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 发票类型说明 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-tags"></i> 发票类型说明
                        </h3>
                    </div>
                    <div class="card-body">
                        <dl>
                            <dt>增值税专用发票</dt>
                            <dd>可以抵扣进项税额的发票</dd>
                            
                            <dt>增值税普通发票</dt>
                            <dd>不能抵扣进项税额的发票</dd>
                            
                            <dt>收据</dt>
                            <dd>非正式发票，用于收款凭证</dd>
                        </dl>
                    </div>
                </div>

                <!-- 税率说明 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-calculator"></i> 税率说明
                        </h3>
                    </div>
                    <div class="card-body">
                        <dl>
                            <dt>16%</dt>
                            <dd>一般纳税人标准税率</dd>
                            
                            <dt>13%</dt>
                            <dd>基本生活必需品税率</dd>
                            
                            <dt>6%</dt>
                            <dd>现代服务业税率</dd>
                            
                            <dt>免税</dt>
                            <dd>免征增值税项目</dd>
                        </dl>
                    </div>
                </div>

                <!-- 快捷操作 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-bolt"></i> 快捷操作
                        </h3>
                    </div>
                    <div class="card-body">
                        <button type="button" class="btn btn-outline-primary btn-sm btn-block" onclick="copyPaymentAmount()">
                            <i class="fas fa-copy"></i> 复制款项金额到发票金额
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm btn-block" onclick="setDefaultTaxRate()">
                            <i class="fas fa-percentage"></i> 设置默认税率（13%）
                        </button>
                        <button type="button" class="btn btn-outline-info btn-sm btn-block" onclick="fillReceiverInfo()">
                            <i class="fas fa-user"></i> 填充默认收件人信息
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        function copyPaymentAmount() {
            const paymentSelect = document.getElementById('PaymentId');
            const selectedOption = paymentSelect.options[paymentSelect.selectedIndex];
            if (selectedOption.value) {
                // 从选项文本中提取金额（格式：名称 (编号) - 订单名称 - 客户名称 - 金额 币种）
                const text = selectedOption.text;
                const amountMatch = text.match(/- ([\d,]+\.?\d*) \w+$/);
                if (amountMatch) {
                    const amount = amountMatch[1].replace(/,/g, '');
                    document.getElementById('Amount').value = amount;
                    toastr.info('已复制款项金额到发票金额');
                }
            } else {
                toastr.warning('请先选择关联款项');
            }
        }

        function setDefaultTaxRate() {
            document.getElementById('TaxRate').value = '13';
            toastr.info('已设置默认税率为13%');
        }

        function fillReceiverInfo() {
            // 这里可以根据选择的款项自动填充收件人信息
            toastr.info('功能开发中...');
        }

        // 表单验证增强
        $(document).ready(function() {
            // 金额输入验证
            $('#Amount').on('input', function() {
                const value = parseFloat($(this).val());
                if (value <= 0) {
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });

            // 款项选择变更处理
            $('#PaymentId').on('change', function() {
                const selectedOption = $(this).find('option:selected');
                if (selectedOption.val()) {
                    // 自动填充发票内容
                    const text = selectedOption.text();
                    const orderName = text.split(' - ')[1];
                    if (orderName && !$('#Content').val()) {
                        $('#Content').val(orderName + ' 相关服务费');
                    }
                }
            });

            // 发票类型变更处理
            $('#Type').on('change', function() {
                const type = $(this).val();
                if (type === '收据') {
                    $('#TaxRate').val('0');
                    toastr.info('收据类型已自动设置为免税');
                } else if (!$('#TaxRate').val()) {
                    $('#TaxRate').val('13');
                    toastr.info('已自动设置默认税率为13%');
                }
            });

            // 税率变更计算
            $('#TaxRate, #Amount').on('change', function() {
                calculateTaxInfo();
            });

            function calculateTaxInfo() {
                const amount = parseFloat($('#Amount').val()) || 0;
                const taxRate = parseFloat($('#TaxRate').val()) || 0;
                
                if (amount > 0 && taxRate > 0) {
                    const taxAmount = amount * taxRate / 100;
                    const netAmount = amount - taxAmount;
                    
                    // 显示税额信息（可以添加到页面上）
                    console.log('税额:', taxAmount.toFixed(2));
                    console.log('不含税金额:', netAmount.toFixed(2));
                }
            }
        });
    </script>
}

@section Styles {
    <style>
        .required::after {
            content: " *";
            color: red;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .is-invalid {
            border-color: #dc3545;
        }
    </style>
}
