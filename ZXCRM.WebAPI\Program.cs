using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using System.Text;
using ZXCRM.Service.Extensions;
using ZXCRM.WebAPI.Middleware;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers().AddJsonOptions(options =>
{
    options.JsonSerializerOptions.PropertyNamingPolicy = null;
    options.JsonSerializerOptions.WriteIndented = true;
});

// 添加API控制器探索
builder.Services.AddEndpointsApiExplorer();

// 添加内存缓存
builder.Services.AddMemoryCache();

// 添加响应缓存
builder.Services.AddResponseCaching();

// 添加响应压缩
builder.Services.AddResponseCompression(options =>
{
    options.EnableForHttps = true;
});

// 配置请求体大小限制
builder.Services.Configure<IISServerOptions>(options =>
{
    options.MaxRequestBodySize = 10485760; // 10MB
});

// Add ZXCRM services (from existing service layer)
builder.Services.AddZXCRMServices(builder.Configuration);

// Configure CORS
builder.Services.AddCors(options =>
{
    // 添加一个允许所有来源的策略，用于开发环境
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin() // 允许任何来源
              .AllowAnyHeader()
              .AllowAnyMethod();
    });

    // 添加一个特定来源的策略，允许凭证
    options.AddPolicy("AllowSpecificOrigins", policy =>
    {
        policy.WithOrigins("http://localhost:5173", "http://127.0.0.1:5173")
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials(); // 允许凭证
    });
});

// Configure JWT Authentication
var jwtSettings = builder.Configuration.GetSection("Jwt");
var key = Encoding.ASCII.GetBytes(jwtSettings["Key"] ?? throw new InvalidOperationException("JWT Key is not configured"));

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.RequireHttpsMetadata = false;
    options.SaveToken = true;
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidIssuer = jwtSettings["Issuer"],
        ValidAudience = jwtSettings["Audience"]
    };
});

// Add OpenAPI/Swagger
builder.Services.AddSwaggerGen();

var app = builder.Build();

// Configure the HTTP request pipeline.
// 全局异常处理（必须在最前面）
app.UseGlobalExceptionHandling();

// 请求日志记录
app.UseRequestLogging();

// 启用响应压缩
app.UseResponseCompression();

// 启用响应缓存
app.UseResponseCaching();

// 始终启用Swagger，不仅限于开发环境
app.UseSwagger();
app.UseSwaggerUI();

// 启用CORS - 使用AllowAll策略，允许所有来源
app.UseCors("AllowAll");

// 启用HTTPS重定向
app.UseHttpsRedirection();

// 启用认证和授权
app.UseAuthentication();
app.UseAuthorization();

// 映射控制器
app.MapControllers();

app.Run();
