using ZXCRM.WebUI.Models;
using ZXCRM.WebUI.Services;
using System.Security.Claims;

namespace ZXCRM.WebUI.Services
{
    public interface INavigationPermissionService
    {
        Task<UserNavigationViewModel> GetUserNavigationAsync(ClaimsPrincipal user, string currentController, string currentAction);
        Task<Dictionary<string, bool>> GetUserPermissionsAsync(int userId);
        Task<bool> CheckUserHasModuleAccessAsync(int userId, string moduleType);
    }

    public class NavigationPermissionService : INavigationPermissionService
    {
        private readonly IUserService _userService;
        private readonly ILogger<NavigationPermissionService> _logger;

        public NavigationPermissionService(IUserService userService, ILogger<NavigationPermissionService> logger)
        {
            _userService = userService;
            _logger = logger;
        }

        public async Task<UserNavigationViewModel> GetUserNavigationAsync(ClaimsPrincipal user, string currentController, string currentAction)
        {
            try
            {
                var viewModel = new UserNavigationViewModel
                {
                    CurrentController = currentController,
                    CurrentAction = currentAction
                };

                // 获取当前用户ID
                var userIdClaim = user.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
                {
                    _logger.LogWarning("Unable to get user ID from claims");
                    // 如果无法获取用户ID，只显示仪表盘
                    viewModel.VisibleNavigationItems = NavigationPermissionManager.GetAllNavigationItems()
                        .Where(item => item.IsAlwaysVisible)
                        .ToList();
                    return viewModel;
                }

                // 获取用户权限
                var userPermissions = await GetUserPermissionsAsync(userId);
                viewModel.UserPermissions = userPermissions;

                // 获取所有导航项
                var allNavigationItems = NavigationPermissionManager.GetAllNavigationItems();

                // 根据权限过滤导航项
                viewModel.VisibleNavigationItems = NavigationPermissionManager.FilterNavigationByPermissions(allNavigationItems, userPermissions);

                _logger.LogInformation("Generated navigation for user {UserId} with {ItemCount} visible items", userId, viewModel.VisibleNavigationItems.Count);

                return viewModel;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating user navigation");

                // 发生错误时，返回最小权限（只有仪表盘）
                return new UserNavigationViewModel
                {
                    CurrentController = currentController,
                    CurrentAction = currentAction,
                    VisibleNavigationItems = NavigationPermissionManager.GetAllNavigationItems()
                        .Where(item => item.IsAlwaysVisible)
                        .ToList()
                };
            }
        }

        public async Task<Dictionary<string, bool>> GetUserPermissionsAsync(int userId)
        {
            var permissions = new Dictionary<string, bool>();

            try
            {
                // 获取所有可能的模块类型
                var moduleTypes = new[] { "用户", "部门", "商机", "订单", "款项", "发票", "报表" };
                var permissionCodes = new[] { "Query", "Create", "Update", "Delete" };

                foreach (var moduleType in moduleTypes)
                {
                    foreach (var permissionCode in permissionCodes)
                    {
                        try
                        {
                            var hasPermission = await _userService.CheckUserPermissionAsync(userId, moduleType, permissionCode);
                            var permissionKey = NavigationPermissionManager.GeneratePermissionKey(moduleType, permissionCode);
                            permissions[permissionKey] = hasPermission;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Error checking permission for user {UserId}, module {ModuleType}, permission {PermissionCode}",
                                userId, moduleType, permissionCode);

                            // 如果检查权限失败，默认为无权限
                            var permissionKey = NavigationPermissionManager.GeneratePermissionKey(moduleType, permissionCode);
                            permissions[permissionKey] = false;
                        }
                    }
                }

                _logger.LogInformation("Retrieved {PermissionCount} permissions for user {UserId}", permissions.Count, userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user permissions for user {UserId}", userId);
            }

            return permissions;
        }

        public async Task<bool> CheckUserHasModuleAccessAsync(int userId, string moduleType)
        {
            try
            {
                // 检查用户是否对该模块有任何权限（查询权限作为基础权限）
                return await _userService.CheckUserPermissionAsync(userId, moduleType, "Query");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking module access for user {UserId}, module {ModuleType}", userId, moduleType);
                return false;
            }
        }
    }
}
