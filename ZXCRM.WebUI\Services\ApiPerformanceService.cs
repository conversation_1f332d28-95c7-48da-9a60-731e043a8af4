using System.Diagnostics;
using Microsoft.Extensions.Caching.Memory;

namespace ZXCRM.WebUI.Services
{
    public interface IApiPerformanceService
    {
        Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, string operationName, int maxRetries = 3);
        void RecordApiCall(string endpoint, TimeSpan duration, bool success);
        ApiPerformanceStats GetPerformanceStats();
    }

    public class ApiPerformanceService : IApiPerformanceService
    {
        private readonly ILogger<ApiPerformanceService> _logger;
        private readonly IMemoryCache _cache;
        private readonly List<ApiCallRecord> _recentCalls = new();
        private readonly object _lock = new();

        public ApiPerformanceService(ILogger<ApiPerformanceService> logger, IMemoryCache cache)
        {
            _logger = logger;
            _cache = cache;
        }

        public async Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, string operationName, int maxRetries = 3)
        {
            var stopwatch = Stopwatch.StartNew();
            Exception? lastException = null;

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    _logger.LogDebug("Executing {OperationName}, attempt {Attempt}/{MaxRetries}", 
                        operationName, attempt, maxRetries);

                    var result = await operation();
                    
                    stopwatch.Stop();
                    RecordApiCall(operationName, stopwatch.Elapsed, true);
                    
                    if (attempt > 1)
                    {
                        _logger.LogInformation("Operation {OperationName} succeeded on attempt {Attempt} after {ElapsedMs}ms",
                            operationName, attempt, stopwatch.ElapsedMilliseconds);
                    }

                    return result;
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    
                    if (attempt == maxRetries)
                    {
                        stopwatch.Stop();
                        RecordApiCall(operationName, stopwatch.Elapsed, false);
                        
                        _logger.LogError(ex, "Operation {OperationName} failed after {MaxRetries} attempts in {ElapsedMs}ms",
                            operationName, maxRetries, stopwatch.ElapsedMilliseconds);
                        break;
                    }

                    var delay = TimeSpan.FromMilliseconds(Math.Pow(2, attempt - 1) * 1000); // 指数退避
                    _logger.LogWarning(ex, "Operation {OperationName} failed on attempt {Attempt}, retrying in {DelayMs}ms",
                        operationName, attempt, delay.TotalMilliseconds);

                    await Task.Delay(delay);
                }
            }

            throw lastException ?? new InvalidOperationException($"Operation {operationName} failed");
        }

        public void RecordApiCall(string endpoint, TimeSpan duration, bool success)
        {
            var record = new ApiCallRecord
            {
                Endpoint = endpoint,
                Duration = duration,
                Success = success,
                Timestamp = DateTime.UtcNow
            };

            lock (_lock)
            {
                _recentCalls.Add(record);
                
                // 只保留最近1000条记录
                if (_recentCalls.Count > 1000)
                {
                    _recentCalls.RemoveAt(0);
                }
            }

            // 记录慢请求
            if (duration.TotalMilliseconds > 5000)
            {
                _logger.LogWarning("Slow API call detected: {Endpoint} took {DurationMs}ms",
                    endpoint, duration.TotalMilliseconds);
            }

            // 记录失败请求
            if (!success)
            {
                _logger.LogWarning("API call failed: {Endpoint} after {DurationMs}ms",
                    endpoint, duration.TotalMilliseconds);
            }
        }

        public ApiPerformanceStats GetPerformanceStats()
        {
            lock (_lock)
            {
                if (!_recentCalls.Any())
                {
                    return new ApiPerformanceStats();
                }

                var now = DateTime.UtcNow;
                var last24Hours = _recentCalls.Where(c => now - c.Timestamp <= TimeSpan.FromHours(24)).ToList();
                var lastHour = _recentCalls.Where(c => now - c.Timestamp <= TimeSpan.FromHours(1)).ToList();

                return new ApiPerformanceStats
                {
                    TotalCalls24h = last24Hours.Count,
                    SuccessfulCalls24h = last24Hours.Count(c => c.Success),
                    FailedCalls24h = last24Hours.Count(c => !c.Success),
                    AverageResponseTime24h = last24Hours.Any() ? last24Hours.Average(c => c.Duration.TotalMilliseconds) : 0,
                    
                    TotalCallsLastHour = lastHour.Count,
                    SuccessfulCallsLastHour = lastHour.Count(c => c.Success),
                    FailedCallsLastHour = lastHour.Count(c => !c.Success),
                    AverageResponseTimeLastHour = lastHour.Any() ? lastHour.Average(c => c.Duration.TotalMilliseconds) : 0,
                    
                    SlowestEndpoints = _recentCalls
                        .GroupBy(c => c.Endpoint)
                        .Select(g => new EndpointStats
                        {
                            Endpoint = g.Key,
                            AverageResponseTime = g.Average(c => c.Duration.TotalMilliseconds),
                            CallCount = g.Count(),
                            SuccessRate = g.Count(c => c.Success) * 100.0 / g.Count()
                        })
                        .OrderByDescending(e => e.AverageResponseTime)
                        .Take(10)
                        .ToList()
                };
            }
        }
    }

    public class ApiCallRecord
    {
        public string Endpoint { get; set; } = string.Empty;
        public TimeSpan Duration { get; set; }
        public bool Success { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public class ApiPerformanceStats
    {
        public int TotalCalls24h { get; set; }
        public int SuccessfulCalls24h { get; set; }
        public int FailedCalls24h { get; set; }
        public double AverageResponseTime24h { get; set; }
        
        public int TotalCallsLastHour { get; set; }
        public int SuccessfulCallsLastHour { get; set; }
        public int FailedCallsLastHour { get; set; }
        public double AverageResponseTimeLastHour { get; set; }
        
        public List<EndpointStats> SlowestEndpoints { get; set; } = new();
        
        public double SuccessRate24h => TotalCalls24h > 0 ? SuccessfulCalls24h * 100.0 / TotalCalls24h : 100;
        public double SuccessRateLastHour => TotalCallsLastHour > 0 ? SuccessfulCallsLastHour * 100.0 / TotalCallsLastHour : 100;
    }

    public class EndpointStats
    {
        public string Endpoint { get; set; } = string.Empty;
        public double AverageResponseTime { get; set; }
        public int CallCount { get; set; }
        public double SuccessRate { get; set; }
    }
}
