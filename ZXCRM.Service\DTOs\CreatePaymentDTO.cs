using System;
using System.ComponentModel.DataAnnotations;

namespace ZXCRM.Service.DTOs
{
    public class CreatePaymentDTO
    {
        [Required(ErrorMessage = "款项名称不能为空")]
        [StringLength(100, ErrorMessage = "款项名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "款项编号不能为空")]
        [StringLength(50, ErrorMessage = "款项编号长度不能超过50个字符")]
        public string Code { get; set; } = string.Empty;

        [Required(ErrorMessage = "订单ID不能为空")]
        public int OrderId { get; set; }

        [Required(ErrorMessage = "款项类型不能为空")]
        [StringLength(20, ErrorMessage = "款项类型长度不能超过20个字符")]
        public string PaymentType { get; set; } = string.Empty;

        [Required(ErrorMessage = "款项金额不能为空")]
        [Range(0.01, double.MaxValue, ErrorMessage = "款项金额必须大于0")]
        public decimal Amount { get; set; }

        [Required(ErrorMessage = "款项币种不能为空")]
        [StringLength(20, ErrorMessage = "款项币种长度不能超过20个字符")]
        public string Currency { get; set; } = "人民币";

        [Required(ErrorMessage = "结算金额不能为空")]
        [Range(0.01, double.MaxValue, ErrorMessage = "结算金额必须大于0")]
        public decimal SettlementAmount { get; set; }

        public DateTime? ExpectedPaymentDate { get; set; }

        public DateTime? ActualPaymentDate { get; set; }

        [Required(ErrorMessage = "开票状态不能为空")]
        [StringLength(20, ErrorMessage = "开票状态长度不能超过20个字符")]
        public string InvoiceStatus { get; set; } = "未开票";

        public DateTime? InvoiceDate { get; set; }
    }
}
