@model ZXCRM.WebUI.Models.ViewModels.InvoiceReportViewModel
@{
    ViewData["Title"] = "发票统计报表";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">发票统计报表</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a asp-controller="Dashboard" asp-action="Index">首页</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Report" asp-action="Index">报表统计</a></li>
                    <li class="breadcrumb-item active">发票统计</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <!-- 查询条件 -->
        <div class="card collapsed-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-filter"></i> 查询条件
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body" style="display: none;">
                <form method="get" asp-action="Invoice">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label asp-for="Query.StartDate">开始日期</label>
                                <input asp-for="Query.StartDate" type="date" class="form-control" />
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label asp-for="Query.EndDate">结束日期</label>
                                <input asp-for="Query.EndDate" type="date" class="form-control" />
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary btn-block">
                                        <i class="fas fa-search"></i> 查询
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        @if (Model.Report != null)
        {
            <!-- 总体统计 -->
            <div class="row">
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3>@Model.Report.TotalCount</h3>
                            <p>总发票数</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3>@Model.Report.TotalAmount.ToString("N0")</h3>
                            <p>发票总金额</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3>@Model.Report.TotalTaxAmount.ToString("N0")</h3>
                            <p>税额总计</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-percentage"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-danger">
                        <div class="inner">
                            <h3>@(Model.Report.TotalCount > 0 ? (Model.Report.TotalAmount / Model.Report.TotalCount).ToString("N0") : "0")</h3>
                            <p>平均发票金额</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-calculator"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 发票类型和公司分布 -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-list"></i> 发票类型分布
                            </h3>
                        </div>
                        <div class="card-body">
                            @if (Model.Report.TypeStats.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>发票类型</th>
                                                <th>数量</th>
                                                <th>金额</th>
                                                <th>占比</th>
                                                <th>分布</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var stat in Model.Report.TypeStats)
                                            {
                                                var badgeColor = stat.Type switch
                                                {
                                                    "增值税专用发票" => "success",
                                                    "增值税普通发票" => "info",
                                                    "收据" => "warning",
                                                    _ => "secondary"
                                                };
                                                <tr>
                                                    <td>
                                                        <span class="badge badge-@badgeColor">
                                                            @stat.Type
                                                        </span>
                                                    </td>
                                                    <td><strong>@stat.Count</strong></td>
                                                    <td>@stat.Amount.ToString("N0")</td>
                                                    <td>@stat.Percentage.ToString("F1")%</td>
                                                    <td>
                                                        <div class="progress progress-xs">
                                                            <div class="progress-bar bg-@badgeColor" 
                                                                 style="width: @stat.Percentage%"></div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <p class="text-muted">暂无发票类型统计数据</p>
                            }
                        </div>
                    </div>
                </div>

                <!-- 开票公司分布 -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-building"></i> 开票公司分布
                            </h3>
                        </div>
                        <div class="card-body">
                            @if (Model.Report.CompanyStats.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>开票公司</th>
                                                <th>发票数</th>
                                                <th>金额</th>
                                                <th>占比</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var stat in Model.Report.CompanyStats)
                                            {
                                                <tr>
                                                    <td>
                                                        <span class="badge badge-@(stat.Company == "A公司" ? "primary" : stat.Company == "B公司" ? "success" : "info")">
                                                            @stat.Company
                                                        </span>
                                                    </td>
                                                    <td><strong>@stat.Count</strong></td>
                                                    <td>@stat.Amount.ToString("N0")</td>
                                                    <td>
                                                        <span class="badge badge-@(stat.Percentage >= 40 ? "success" : stat.Percentage >= 20 ? "warning" : "secondary")">
                                                            @stat.Percentage.ToString("F1")%
                                                        </span>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <p class="text-muted">暂无开票公司统计数据</p>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- 税率分析和月度趋势 -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-percentage"></i> 税率分析统计
                            </h3>
                        </div>
                        <div class="card-body">
                            @if (Model.Report.TaxRateStats.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>税率</th>
                                                <th>发票数</th>
                                                <th>发票金额</th>
                                                <th>税额</th>
                                                <th>占比</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var stat in Model.Report.TaxRateStats)
                                            {
                                                <tr>
                                                    <td>
                                                        <span class="badge badge-@(stat.TaxRate == 0 ? "secondary" : stat.TaxRate <= 6 ? "info" : stat.TaxRate <= 13 ? "warning" : "danger")">
                                                            @(stat.TaxRate == 0 ? "免税" : stat.TaxRate.ToString("F0") + "%")
                                                        </span>
                                                    </td>
                                                    <td><strong>@stat.Count</strong></td>
                                                    <td>@stat.Amount.ToString("N0")</td>
                                                    <td>@stat.TaxAmount.ToString("N0")</td>
                                                    <td>
                                                        <div class="progress progress-xs">
                                                            <div class="progress-bar bg-@(stat.TaxRate == 0 ? "secondary" : stat.TaxRate <= 6 ? "info" : stat.TaxRate <= 13 ? "warning" : "danger")" 
                                                                 style="width: @stat.Percentage%"></div>
                                                        </div>
                                                        <span class="badge badge-light">@stat.Percentage.ToString("F1")%</span>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 税额汇总 -->
                                <div class="mt-3">
                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-info-circle"></i> 税额汇总</h6>
                                        <div class="row">
                                            <div class="col-6">
                                                <strong>含税总额：</strong>
                                                <span class="badge badge-primary badge-lg">@Model.Report.TotalAmount.ToString("N0")</span>
                                            </div>
                                            <div class="col-6">
                                                <strong>税额总计：</strong>
                                                <span class="badge badge-warning badge-lg">@Model.Report.TotalTaxAmount.ToString("N0")</span>
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-6">
                                                <strong>不含税金额：</strong>
                                                <span class="badge badge-success badge-lg">@((Model.Report.TotalAmount - Model.Report.TotalTaxAmount).ToString("N0"))</span>
                                            </div>
                                            <div class="col-6">
                                                <strong>平均税率：</strong>
                                                <span class="badge badge-info badge-lg">
                                                    @(Model.Report.TotalAmount > 0 ? (Model.Report.TotalTaxAmount / Model.Report.TotalAmount * 100).ToString("F1") : "0")%
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                            else
                            {
                                <p class="text-muted">暂无税率统计数据</p>
                            }
                        </div>
                    </div>
                </div>

                <!-- 月度趋势 -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-chart-line"></i> 月度开票趋势
                            </h3>
                        </div>
                        <div class="card-body">
                            @if (Model.Report.MonthlyStats.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>月份</th>
                                                <th>发票数</th>
                                                <th>开票金额</th>
                                                <th>税额</th>
                                                <th>趋势</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var stat in Model.Report.MonthlyStats.OrderBy(s => s.Month))
                                            {
                                                <tr>
                                                    <td><strong>@stat.Month</strong></td>
                                                    <td>@stat.Count</td>
                                                    <td>@stat.Amount.ToString("N0")</td>
                                                    <td>@stat.TaxAmount.ToString("N0")</td>
                                                    <td>
                                                        <div class="progress progress-xs">
                                                            <div class="progress-bar bg-primary" style="width: @(stat.Count * 5)%"></div>
                                                        </div>
                                                        <span class="badge badge-info">@stat.Count 张</span>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 开票趋势分析 -->
                                <div class="mt-3">
                                    <div class="alert alert-success">
                                        <h6><i class="fas fa-chart-line"></i> 趋势分析</h6>
                                        @{
                                            var maxMonth = Model.Report.MonthlyStats.OrderByDescending(s => s.Amount).FirstOrDefault();
                                            var minMonth = Model.Report.MonthlyStats.OrderBy(s => s.Amount).FirstOrDefault();
                                        }
                                        <div class="row">
                                            <div class="col-6">
                                                <strong>开票最多：</strong>
                                                @if (maxMonth != null)
                                                {
                                                    <span class="badge badge-success">@maxMonth.Month (@maxMonth.Amount.ToString("N0"))</span>
                                                }
                                            </div>
                                            <div class="col-6">
                                                <strong>开票最少：</strong>
                                                @if (minMonth != null)
                                                {
                                                    <span class="badge badge-warning">@minMonth.Month (@minMonth.Amount.ToString("N0"))</span>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                            else
                            {
                                <p class="text-muted">暂无月度统计数据</p>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
        else
        {
            <div class="alert alert-warning">
                <h4><i class="icon fas fa-exclamation-triangle"></i> 提示</h4>
                暂无发票统计数据，请检查查询条件或联系管理员。
            </div>
        }

        <!-- 快捷操作 -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-bolt"></i> 快捷操作
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="btn-group" role="group">
                            <a asp-controller="Invoice" asp-action="Index" class="btn btn-success">
                                <i class="fas fa-file-invoice"></i> 发票管理
                            </a>
                            <a asp-controller="Invoice" asp-action="Create" class="btn btn-primary">
                                <i class="fas fa-plus"></i> 新增发票
                            </a>
                            <a asp-action="Dashboard" class="btn btn-info">
                                <i class="fas fa-tachometer-alt"></i> 综合仪表盘
                            </a>
                            <a asp-controller="Report" asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> 返回报表首页
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        $(document).ready(function() {
            // 显示提示消息
            @if (TempData["SuccessMessage"] != null)
            {
                <text>
                toastr.success('@TempData["SuccessMessage"]');
                </text>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <text>
                toastr.error('@TempData["ErrorMessage"]');
                </text>
            }
        });
    </script>
}
