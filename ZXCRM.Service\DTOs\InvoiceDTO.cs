using System;

namespace ZXCRM.Service.DTOs
{
    public class InvoiceDTO
    {
        public int Id { get; set; }
        public int PaymentId { get; set; }
        public string PaymentName { get; set; } = string.Empty;
        public string PaymentCode { get; set; } = string.Empty;
        public string OrderName { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string Company { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public decimal TaxRate { get; set; }
        public decimal Amount { get; set; } // 新增发票金额字段
        public string Status { get; set; } = "Normal"; // 新增发票状态字段，默认为正常
        public string? Content { get; set; }
        public string? Code { get; set; }
        public string? ReceiverName { get; set; }
        public string? ReceiverPhone { get; set; }
        public string? MailingAddress { get; set; }
        public string? ApplicantPhone { get; set; }
        public string? CustomerEmail { get; set; }
        public int CreatedById { get; set; }
        public string CreatedByName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }
}
