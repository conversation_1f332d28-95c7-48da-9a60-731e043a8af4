using ZXCRM.Service.DTOs;
using ZXCRM.WebUI.Models;

namespace ZXCRM.WebUI.Services
{
    public interface IReportService
    {
        Task<ApiResponse<DashboardStatsDTO>> GetDashboardStatsAsync(ReportQueryDTO? query = null);
        Task<ApiResponse<OpportunityReportDTO>> GetOpportunityReportAsync(ReportQueryDTO? query = null);
        Task<ApiResponse<OrderReportDTO>> GetOrderReportAsync(ReportQueryDTO? query = null);
        Task<ApiResponse<PaymentReportDTO>> GetPaymentReportAsync(ReportQueryDTO? query = null);
        Task<ApiResponse<InvoiceReportDTO>> GetInvoiceReportAsync(ReportQueryDTO? query = null);
        Task<ApiResponse<object>> GetReportSummaryAsync(ReportQueryDTO? query = null);
        Task<ApiResponse<object>> GetTrendsAsync(ReportQueryDTO? query = null);
    }

    public class ReportService : IReportService
    {
        private readonly IApiService _apiService;
        private readonly ILogger<ReportService> _logger;

        public ReportService(IApiService apiService, ILogger<ReportService> logger)
        {
            _apiService = apiService;
            _logger = logger;
        }

        public async Task<ApiResponse<DashboardStatsDTO>> GetDashboardStatsAsync(ReportQueryDTO? query = null)
        {
            try
            {
                _logger.LogInformation("Getting dashboard stats from API");
                var queryString = BuildQueryString(query);
                var response = await _apiService.GetAsync<DashboardStatsDTO>($"api/report/dashboard{queryString}");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dashboard stats");
                return new ApiResponse<DashboardStatsDTO>
                {
                    Success = false,
                    Message = "获取仪表盘统计失败",
                    Data = null
                };
            }
        }

        public async Task<ApiResponse<OpportunityReportDTO>> GetOpportunityReportAsync(ReportQueryDTO? query = null)
        {
            try
            {
                _logger.LogInformation("Getting opportunity report from API");
                var queryString = BuildQueryString(query);
                var response = await _apiService.GetAsync<OpportunityReportDTO>($"api/report/opportunity{queryString}");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting opportunity report");
                return new ApiResponse<OpportunityReportDTO>
                {
                    Success = false,
                    Message = "获取商机报表失败",
                    Data = null
                };
            }
        }

        public async Task<ApiResponse<OrderReportDTO>> GetOrderReportAsync(ReportQueryDTO? query = null)
        {
            try
            {
                _logger.LogInformation("Getting order report from API");
                var queryString = BuildQueryString(query);
                var response = await _apiService.GetAsync<OrderReportDTO>($"api/report/order{queryString}");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting order report");
                return new ApiResponse<OrderReportDTO>
                {
                    Success = false,
                    Message = "获取订单报表失败",
                    Data = null
                };
            }
        }

        public async Task<ApiResponse<PaymentReportDTO>> GetPaymentReportAsync(ReportQueryDTO? query = null)
        {
            try
            {
                _logger.LogInformation("Getting payment report from API");
                var queryString = BuildQueryString(query);
                var response = await _apiService.GetAsync<PaymentReportDTO>($"api/report/payment{queryString}");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payment report");
                return new ApiResponse<PaymentReportDTO>
                {
                    Success = false,
                    Message = "获取款项报表失败",
                    Data = null
                };
            }
        }

        public async Task<ApiResponse<InvoiceReportDTO>> GetInvoiceReportAsync(ReportQueryDTO? query = null)
        {
            try
            {
                _logger.LogInformation("Getting invoice report from API");
                var queryString = BuildQueryString(query);
                var response = await _apiService.GetAsync<InvoiceReportDTO>($"api/report/invoice{queryString}");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoice report");
                return new ApiResponse<InvoiceReportDTO>
                {
                    Success = false,
                    Message = "获取发票报表失败",
                    Data = null
                };
            }
        }

        public async Task<ApiResponse<object>> GetReportSummaryAsync(ReportQueryDTO? query = null)
        {
            try
            {
                _logger.LogInformation("Getting report summary from API");
                var queryString = BuildQueryString(query);
                var response = await _apiService.GetAsync<object>($"api/report/summary{queryString}");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting report summary");
                return new ApiResponse<object>
                {
                    Success = false,
                    Message = "获取报表摘要失败",
                    Data = null
                };
            }
        }

        public async Task<ApiResponse<object>> GetTrendsAsync(ReportQueryDTO? query = null)
        {
            try
            {
                _logger.LogInformation("Getting trends data from API");
                var queryString = BuildQueryString(query);
                var response = await _apiService.GetAsync<object>($"api/report/trends{queryString}");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting trends data");
                return new ApiResponse<object>
                {
                    Success = false,
                    Message = "获取趋势数据失败",
                    Data = null
                };
            }
        }

        private string BuildQueryString(ReportQueryDTO? query)
        {
            if (query == null) return "";

            var queryParams = new List<string>();

            if (query.StartDate.HasValue)
                queryParams.Add($"StartDate={query.StartDate.Value:yyyy-MM-dd}");

            if (query.EndDate.HasValue)
                queryParams.Add($"EndDate={query.EndDate.Value:yyyy-MM-dd}");

            if (query.DepartmentId.HasValue)
                queryParams.Add($"DepartmentId={query.DepartmentId.Value}");

            if (query.UserId.HasValue)
                queryParams.Add($"UserId={query.UserId.Value}");

            if (!string.IsNullOrEmpty(query.Currency))
                queryParams.Add($"Currency={Uri.EscapeDataString(query.Currency)}");

            if (!string.IsNullOrEmpty(query.Status))
                queryParams.Add($"Status={Uri.EscapeDataString(query.Status)}");

            return queryParams.Count > 0 ? "?" + string.Join("&", queryParams) : "";
        }
    }
}
