namespace ZXCRM.Data.Entities
{
    public class Opportunity : BaseEntity
    {
        public string Name { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string? ContactName { get; set; }
        public string? ContactPhone { get; set; }
        public string? Content { get; set; }
        public string Status { get; set; } = string.Empty;
        public int CreatedById { get; set; }
        
        // 导航属性
        public User CreatedBy { get; set; } = null!;
        public ICollection<Order> Orders { get; set; } = new List<Order>();
    }
}
