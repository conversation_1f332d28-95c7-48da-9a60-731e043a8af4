@model DashboardViewModel
@{
    ViewData["Title"] = "仪表盘";
}

<!-- Small boxes (Stat box) -->
<div class="row">
    <div class="col-lg-3 col-6">
        <!-- small box -->
        <div class="small-box bg-info">
            <div class="inner">
                <h3>@Model.Summary.TotalOrders</h3>
                <p>订单总数</p>
            </div>
            <div class="icon">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <a asp-controller="Order" asp-action="Index" class="small-box-footer">
                查看详情 <i class="fas fa-arrow-circle-right"></i>
            </a>
        </div>
    </div>

    <div class="col-lg-3 col-6">
        <!-- small box -->
        <div class="small-box bg-success">
            <div class="inner">
                <h3>¥@Model.Summary.TotalRevenue.ToString("N2")</h3>
                <p>总收入</p>
            </div>
            <div class="icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <a asp-controller="Payment" asp-action="Index" class="small-box-footer">
                查看详情 <i class="fas fa-arrow-circle-right"></i>
            </a>
        </div>
    </div>

    <div class="col-lg-3 col-6">
        <!-- small box -->
        <div class="small-box bg-warning">
            <div class="inner">
                <h3>@Model.Summary.TotalCustomers</h3>
                <p>客户总数</p>
            </div>
            <div class="icon">
                <i class="fas fa-users"></i>
            </div>
            <a href="#" class="small-box-footer">
                查看详情 <i class="fas fa-arrow-circle-right"></i>
            </a>
        </div>
    </div>

    <div class="col-lg-3 col-6">
        <!-- small box -->
        <div class="small-box bg-danger">
            <div class="inner">
                <h3>@Model.Summary.PendingOrders</h3>
                <p>待处理订单</p>
            </div>
            <div class="icon">
                <i class="fas fa-clock"></i>
            </div>
            <a asp-controller="Order" asp-action="Index" asp-route-status="pending" class="small-box-footer">
                查看详情 <i class="fas fa-arrow-circle-right"></i>
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- Left col -->
    <section class="col-lg-7 connectedSortable">
        <!-- Chart -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-chart-pie mr-1"></i>
                    @Model.ChartData.Title
                </h3>
                <div class="card-tools">
                    <ul class="nav nav-pills ml-auto">
                        <li class="nav-item">
                            <a class="nav-link active" href="#revenue-chart" data-toggle="tab">收入</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#sales-chart" data-toggle="tab">销售</a>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <div class="tab-content p-0">
                    <div class="chart tab-pane active" id="revenue-chart" style="position: relative; height: 300px;">
                        <canvas id="revenue-chart-canvas" height="300" style="height: 300px;"></canvas>
                    </div>
                    <div class="chart tab-pane" id="sales-chart" style="position: relative; height: 300px;">
                        <canvas id="sales-chart-canvas" height="300" style="height: 300px;"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Right col -->
    <section class="col-lg-5 connectedSortable">
        <!-- Recent Activity -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-history mr-1"></i>
                    最近活动
                </h3>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    @foreach (var activity in Model.RecentActivities.Take(10))
                    {
                        <li class="media mb-3">
                            <div class="media-object <EMAIL> rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                <i class="@activity.Icon text-white"></i>
                            </div>
                            <div class="media-body ml-3">
                                <h6 class="mt-0 mb-1">@activity.Description</h6>
                                <small class="text-muted">
                                    @activity.UserName • @activity.CreatedAt.ToString("yyyy-MM-dd HH:mm")
                                </small>
                            </div>
                        </li>
                    }
                </ul>
            </div>
            <div class="card-footer text-center">
                <a href="#" class="btn btn-sm btn-primary">查看所有活动</a>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-bolt mr-1"></i>
                    快速操作
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6 mb-2">
                        <a asp-controller="Opportunity" asp-action="Create" class="btn btn-success btn-block">
                            <i class="fas fa-seedling"></i> 新增商机
                        </a>
                    </div>
                    <div class="col-6 mb-2">
                        <a asp-controller="Order" asp-action="Create" class="btn btn-primary btn-block">
                            <i class="fas fa-shopping-cart"></i> 新增订单
                        </a>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6 mb-2">
                        <a asp-controller="Payment" asp-action="Create" class="btn btn-warning btn-block">
                            <i class="fas fa-money-bill"></i> 记录付款
                        </a>
                    </div>
                    <div class="col-6 mb-2">
                        <a asp-controller="Invoice" asp-action="Create" class="btn btn-info btn-block">
                            <i class="fas fa-file-invoice"></i> 创建发票
                        </a>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6 mb-2">
                        <a asp-controller="User" asp-action="Create" class="btn btn-secondary btn-block">
                            <i class="fas fa-user-plus"></i> 新增用户
                        </a>
                    </div>
                    <div class="col-6 mb-2">
                        <a asp-controller="Report" asp-action="Index" class="btn btn-dark btn-block">
                            <i class="fas fa-chart-bar"></i> 查看报表
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Additional Stats Row -->
<div class="row">
    <div class="col-md-3 col-sm-6 col-12">
        <div class="info-box">
            <span class="info-box-icon bg-info"><i class="fas fa-file-invoice"></i></span>
            <div class="info-box-content">
                <span class="info-box-text">发票总数</span>
                <span class="info-box-number">@Model.Summary.TotalInvoices</span>
            </div>
        </div>
    </div>

    <div class="col-md-3 col-sm-6 col-12">
        <div class="info-box">
            <span class="info-box-icon bg-success"><i class="fas fa-money-bill-wave"></i></span>
            <div class="info-box-content">
                <span class="info-box-text">总付款</span>
                <span class="info-box-number">¥@Model.Summary.TotalPayments.ToString("N2")</span>
            </div>
        </div>
    </div>

    <div class="col-md-3 col-sm-6 col-12">
        <div class="info-box">
            <span class="info-box-icon bg-warning"><i class="fas fa-users"></i></span>
            <div class="info-box-content">
                <span class="info-box-text">用户总数</span>
                <span class="info-box-number">@Model.Summary.TotalUsers</span>
            </div>
        </div>
    </div>

    <div class="col-md-3 col-sm-6 col-12">
        <div class="info-box">
            <span class="info-box-icon bg-danger"><i class="fas fa-check-circle"></i></span>
            <div class="info-box-content">
                <span class="info-box-text">已完成订单</span>
                <span class="info-box-number">@Model.Summary.CompletedOrders</span>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Revenue Chart
            var revenueCtx = document.getElementById('revenue-chart-canvas').getContext('2d');
            var revenueChart = new Chart(revenueCtx, {
                type: 'line',
                data: {
                    labels: @Html.Raw(Json.Serialize(Model.ChartData.Labels)),
                    datasets: [{
                        label: '收入',
                        data: @Html.Raw(Json.Serialize(Model.ChartData.Data)),
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '¥' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });

            // Sales Chart (placeholder)
            var salesCtx = document.getElementById('sales-chart-canvas').getContext('2d');
            var salesChart = new Chart(salesCtx, {
                type: 'bar',
                data: {
                    labels: @Html.Raw(Json.Serialize(Model.ChartData.Labels)),
                    datasets: [{
                        label: '销售量',
                        data: [12, 19, 3, 5, 2, 3],
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        });
    </script>
}
