using System.ComponentModel.DataAnnotations;

namespace ZXCRM.WebUI.Models.ViewModels
{
    public class InvoiceListViewModel
    {
        public List<InvoiceItemViewModel> Invoices { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageIndex { get; set; }
        public int PageSize { get; set; }
        public string SearchTerm { get; set; } = string.Empty;
        public string Company { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public int? PaymentId { get; set; }
    }

    public class InvoiceItemViewModel
    {
        public int Id { get; set; }
        public int PaymentId { get; set; }
        public string PaymentName { get; set; } = string.Empty;
        public string PaymentCode { get; set; } = string.Empty;
        public string OrderName { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string Company { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public decimal TaxRate { get; set; }
        public decimal Amount { get; set; }
        public string Status { get; set; } = string.Empty;
        public string? Content { get; set; }
        public string? Code { get; set; }
        public string? ReceiverName { get; set; }
        public string? ReceiverPhone { get; set; }
        public string? MailingAddress { get; set; }
        public string? ApplicantPhone { get; set; }
        public string? CustomerEmail { get; set; }
        public string CreatedByName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
    }

    public class CreateInvoiceViewModel
    {
        [Required(ErrorMessage = "请选择关联款项")]
        [Display(Name = "关联款项")]
        public int PaymentId { get; set; }

        [Required(ErrorMessage = "请选择发票公司")]
        [Display(Name = "发票公司")]
        public string Company { get; set; } = string.Empty;

        [Required(ErrorMessage = "请选择发票类型")]
        [Display(Name = "发票类型")]
        public string Type { get; set; } = string.Empty;

        [Required(ErrorMessage = "请选择发票税率")]
        [Display(Name = "发票税率")]
        public decimal TaxRate { get; set; }

        [Required(ErrorMessage = "发票金额不能为空")]
        [Range(0.01, double.MaxValue, ErrorMessage = "发票金额必须大于0")]
        [Display(Name = "发票金额")]
        public decimal Amount { get; set; }

        [Display(Name = "发票状态")]
        public string Status { get; set; } = "Normal";

        [StringLength(200, ErrorMessage = "发票内容长度不能超过200个字符")]
        [Display(Name = "发票内容")]
        public string? Content { get; set; }

        [StringLength(50, ErrorMessage = "发票代号长度不能超过50个字符")]
        [Display(Name = "发票代号")]
        public string? Code { get; set; }

        [StringLength(50, ErrorMessage = "收件人长度不能超过50个字符")]
        [Display(Name = "收件人")]
        public string? ReceiverName { get; set; }

        [StringLength(20, ErrorMessage = "收件人电话长度不能超过20个字符")]
        [Display(Name = "收件人电话")]
        public string? ReceiverPhone { get; set; }

        [StringLength(200, ErrorMessage = "邮寄地址长度不能超过200个字符")]
        [Display(Name = "发票邮寄地址")]
        public string? MailingAddress { get; set; }

        [StringLength(20, ErrorMessage = "申请人电话长度不能超过20个字符")]
        [Display(Name = "申请人电话")]
        public string? ApplicantPhone { get; set; }

        [StringLength(100, ErrorMessage = "客户电子邮件长度不能超过100个字符")]
        [EmailAddress(ErrorMessage = "客户电子邮件格式不正确")]
        [Display(Name = "客户电子邮件")]
        public string? CustomerEmail { get; set; }

        // 用于下拉列表
        public List<PaymentSelectItem> Payments { get; set; } = new();
    }

    public class EditInvoiceViewModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "请选择关联款项")]
        [Display(Name = "关联款项")]
        public int PaymentId { get; set; }

        [Required(ErrorMessage = "请选择发票公司")]
        [Display(Name = "发票公司")]
        public string Company { get; set; } = string.Empty;

        [Required(ErrorMessage = "请选择发票类型")]
        [Display(Name = "发票类型")]
        public string Type { get; set; } = string.Empty;

        [Required(ErrorMessage = "请选择发票税率")]
        [Display(Name = "发票税率")]
        public decimal TaxRate { get; set; }

        [Required(ErrorMessage = "发票金额不能为空")]
        [Range(0.01, double.MaxValue, ErrorMessage = "发票金额必须大于0")]
        [Display(Name = "发票金额")]
        public decimal Amount { get; set; }

        [Display(Name = "发票状态")]
        public string Status { get; set; } = string.Empty;

        [StringLength(200, ErrorMessage = "发票内容长度不能超过200个字符")]
        [Display(Name = "发票内容")]
        public string? Content { get; set; }

        [StringLength(50, ErrorMessage = "发票代号长度不能超过50个字符")]
        [Display(Name = "发票代号")]
        public string? Code { get; set; }

        [StringLength(50, ErrorMessage = "收件人长度不能超过50个字符")]
        [Display(Name = "收件人")]
        public string? ReceiverName { get; set; }

        [StringLength(20, ErrorMessage = "收件人电话长度不能超过20个字符")]
        [Display(Name = "收件人电话")]
        public string? ReceiverPhone { get; set; }

        [StringLength(200, ErrorMessage = "邮寄地址长度不能超过200个字符")]
        [Display(Name = "发票邮寄地址")]
        public string? MailingAddress { get; set; }

        [StringLength(20, ErrorMessage = "申请人电话长度不能超过20个字符")]
        [Display(Name = "申请人电话")]
        public string? ApplicantPhone { get; set; }

        [StringLength(100, ErrorMessage = "客户电子邮件长度不能超过100个字符")]
        [EmailAddress(ErrorMessage = "客户电子邮件格式不正确")]
        [Display(Name = "客户电子邮件")]
        public string? CustomerEmail { get; set; }

        // 用于下拉列表
        public List<PaymentSelectItem> Payments { get; set; } = new();
    }

    public class InvoiceDetailViewModel
    {
        public int Id { get; set; }
        public int PaymentId { get; set; }
        public string PaymentName { get; set; } = string.Empty;
        public string PaymentCode { get; set; } = string.Empty;
        public string OrderName { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string Company { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public decimal TaxRate { get; set; }
        public decimal Amount { get; set; }
        public string Status { get; set; } = string.Empty;
        public string? Content { get; set; }
        public string? Code { get; set; }
        public string? ReceiverName { get; set; }
        public string? ReceiverPhone { get; set; }
        public string? MailingAddress { get; set; }
        public string? ApplicantPhone { get; set; }
        public string? CustomerEmail { get; set; }
        public string CreatedByName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class PaymentSelectItem
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string OrderName { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public string PaymentType { get; set; } = string.Empty;
    }
}
