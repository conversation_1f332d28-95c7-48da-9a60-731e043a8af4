using ZXCRM.WebUI.Services;

namespace ZXCRM.WebUI.Middleware
{
    public class TokenMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<TokenMiddleware> _logger;

        public TokenMiddleware(RequestDelegate next, ILogger<TokenMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, IApiService apiService)
        {
            try
            {
                // 检查用户是否已认证
                if (context.User.Identity?.IsAuthenticated == true)
                {
                    // 从Claims中获取AccessToken
                    var accessToken = context.User.Claims
                        .FirstOrDefault(c => c.Type == "AccessToken")?.Value;

                    if (!string.IsNullOrEmpty(accessToken))
                    {
                        // 设置API服务的认证Token
                        apiService.SetAuthToken(accessToken);
                        _logger.LogDebug("Set API auth token for user: {Username}", context.User.Identity.Name);
                    }
                    else
                    {
                        _logger.LogWarning("No access token found in claims for user: {Username}", context.User.Identity.Name);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting API auth token");
            }

            await _next(context);
        }
    }

    public static class TokenMiddlewareExtensions
    {
        public static IApplicationBuilder UseTokenMiddleware(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<TokenMiddleware>();
        }
    }
}
