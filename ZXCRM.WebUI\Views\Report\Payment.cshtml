@model ZXCRM.WebUI.Models.ViewModels.PaymentReportViewModel
@{
    ViewData["Title"] = "款项统计报表";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">款项统计报表</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a asp-controller="Dashboard" asp-action="Index">首页</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Report" asp-action="Index">报表统计</a></li>
                    <li class="breadcrumb-item active">款项统计</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <!-- 查询条件 -->
        <div class="card collapsed-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-filter"></i> 查询条件
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body" style="display: none;">
                <form method="get" asp-action="Payment">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label asp-for="Query.StartDate">开始日期</label>
                                <input asp-for="Query.StartDate" type="date" class="form-control" />
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label asp-for="Query.EndDate">结束日期</label>
                                <input asp-for="Query.EndDate" type="date" class="form-control" />
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label asp-for="Query.Currency">币种</label>
                                <select asp-for="Query.Currency" class="form-control">
                                    <option value="">全部币种</option>
                                    @foreach (var currency in Model.Currencies)
                                    {
                                        <option value="@currency">@currency</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary btn-block">
                                        <i class="fas fa-search"></i> 查询
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        @if (Model.Report != null)
        {
            <!-- 总体统计 -->
            <div class="row">
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3>@Model.Report.TotalCount</h3>
                            <p>总款项数</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-money-bill"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3>@Model.Report.TotalAmount.ToString("N0")</h3>
                            <p>回款总金额</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3>@Model.Report.CollectionRate.ToString("F1")%</h3>
                            <p>回款率</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-danger">
                        <div class="inner">
                            <h3>@(Model.Report.TotalCount > 0 ? (Model.Report.TotalAmount / Model.Report.TotalCount).ToString("N0") : "0")</h3>
                            <p>平均款项金额</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-calculator"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 款项类型分布和币种统计 -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-list"></i> 款项类型分布
                            </h3>
                        </div>
                        <div class="card-body">
                            @if (Model.Report.TypeStats.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>款项类型</th>
                                                <th>数量</th>
                                                <th>金额</th>
                                                <th>占比</th>
                                                <th>分布</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var stat in Model.Report.TypeStats)
                                            {
                                                var badgeColor = stat.Type switch
                                                {
                                                    "首付款" => "primary",
                                                    "尾款" => "success",
                                                    "全款" => "warning",
                                                    _ => "info"
                                                };
                                                <tr>
                                                    <td>
                                                        <span class="badge badge-@badgeColor">
                                                            @stat.Type
                                                        </span>
                                                    </td>
                                                    <td><strong>@stat.Count</strong></td>
                                                    <td>@stat.Amount.ToString("N0")</td>
                                                    <td>@stat.Percentage.ToString("F1")%</td>
                                                    <td>
                                                        <div class="progress progress-xs">
                                                            <div class="progress-bar bg-@badgeColor" 
                                                                 style="width: @stat.Percentage%"></div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <p class="text-muted">暂无款项类型统计数据</p>
                            }
                        </div>
                    </div>
                </div>

                <!-- 币种统计 -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-coins"></i> 币种分布统计
                            </h3>
                        </div>
                        <div class="card-body">
                            @if (Model.Report.CurrencyStats.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>币种</th>
                                                <th>款项数</th>
                                                <th>金额</th>
                                                <th>占比</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var stat in Model.Report.CurrencyStats)
                                            {
                                                <tr>
                                                    <td>
                                                        <span class="badge badge-@(stat.Currency == "RMB" ? "success" : stat.Currency == "USD" ? "primary" : "info")">
                                                            @stat.Currency
                                                        </span>
                                                    </td>
                                                    <td><strong>@stat.Count</strong></td>
                                                    <td>@stat.Amount.ToString("N0")</td>
                                                    <td>
                                                        <span class="badge badge-@(stat.Percentage >= 50 ? "success" : stat.Percentage >= 25 ? "warning" : "secondary")">
                                                            @stat.Percentage.ToString("F1")%
                                                        </span>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <p class="text-muted">暂无币种统计数据</p>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- 月度趋势分析 -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-chart-line"></i> 月度回款趋势分析
                            </h3>
                        </div>
                        <div class="card-body">
                            @if (Model.Report.MonthlyStats.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>月份</th>
                                                <th>款项数量</th>
                                                <th>回款金额</th>
                                                <th>回款率</th>
                                                <th>趋势图</th>
                                                <th>状态</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var stat in Model.Report.MonthlyStats.OrderBy(s => s.Month))
                                            {
                                                <tr>
                                                    <td><strong>@stat.Month</strong></td>
                                                    <td>@stat.Count</td>
                                                    <td>@stat.Amount.ToString("N0")</td>
                                                    <td>
                                                        <span class="badge badge-@(stat.CollectionRate >= 80 ? "success" : stat.CollectionRate >= 60 ? "warning" : "danger")">
                                                            @stat.CollectionRate.ToString("F1")%
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="progress progress-xs">
                                                            <div class="progress-bar bg-@(stat.CollectionRate >= 80 ? "success" : stat.CollectionRate >= 60 ? "warning" : "danger")" 
                                                                 style="width: @stat.CollectionRate%"></div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        @if (stat.CollectionRate >= 80)
                                                        {
                                                            <span class="badge badge-success">
                                                                <i class="fas fa-check"></i> 良好
                                                            </span>
                                                        }
                                                        else if (stat.CollectionRate >= 60)
                                                        {
                                                            <span class="badge badge-warning">
                                                                <i class="fas fa-exclamation"></i> 一般
                                                            </span>
                                                        }
                                                        else
                                                        {
                                                            <span class="badge badge-danger">
                                                                <i class="fas fa-times"></i> 需关注
                                                            </span>
                                                        }
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 回款率分析 -->
                                <div class="mt-3">
                                    <div class="alert alert-info">
                                        <h5><i class="icon fas fa-info"></i> 回款率分析</h5>
                                        <div class="row">
                                            <div class="col-md-4">
                                                <strong>总体回款率：</strong>
                                                <span class="badge badge-@(Model.Report.CollectionRate >= 80 ? "success" : Model.Report.CollectionRate >= 60 ? "warning" : "danger") badge-lg">
                                                    @Model.Report.CollectionRate.ToString("F1")%
                                                </span>
                                            </div>
                                            <div class="col-md-4">
                                                <strong>最高月份：</strong>
                                                @{
                                                    var bestMonth = Model.Report.MonthlyStats.OrderByDescending(s => s.CollectionRate).FirstOrDefault();
                                                }
                                                @if (bestMonth != null)
                                                {
                                                    <span class="badge badge-success">@bestMonth.Month (@bestMonth.CollectionRate.ToString("F1")%)</span>
                                                }
                                            </div>
                                            <div class="col-md-4">
                                                <strong>最低月份：</strong>
                                                @{
                                                    var worstMonth = Model.Report.MonthlyStats.OrderBy(s => s.CollectionRate).FirstOrDefault();
                                                }
                                                @if (worstMonth != null)
                                                {
                                                    <span class="badge badge-danger">@worstMonth.Month (@worstMonth.CollectionRate.ToString("F1")%)</span>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                            else
                            {
                                <p class="text-muted">暂无月度统计数据</p>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
        else
        {
            <div class="alert alert-warning">
                <h4><i class="icon fas fa-exclamation-triangle"></i> 提示</h4>
                暂无款项统计数据，请检查查询条件或联系管理员。
            </div>
        }

        <!-- 快捷操作 -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-bolt"></i> 快捷操作
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="btn-group" role="group">
                            <a asp-controller="Payment" asp-action="Index" class="btn btn-success">
                                <i class="fas fa-money-bill"></i> 款项管理
                            </a>
                            <a asp-controller="Payment" asp-action="Create" class="btn btn-primary">
                                <i class="fas fa-plus"></i> 新增款项
                            </a>
                            <a asp-action="Dashboard" class="btn btn-info">
                                <i class="fas fa-tachometer-alt"></i> 综合仪表盘
                            </a>
                            <a asp-controller="Report" asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> 返回报表首页
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        $(document).ready(function() {
            // 显示提示消息
            @if (TempData["SuccessMessage"] != null)
            {
                <text>
                toastr.success('@TempData["SuccessMessage"]');
                </text>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <text>
                toastr.error('@TempData["ErrorMessage"]');
                </text>
            }
        });
    </script>
}
