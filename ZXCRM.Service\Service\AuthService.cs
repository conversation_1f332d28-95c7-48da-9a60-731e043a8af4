using AutoMapper;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using ZXCRM.Data.Entities;
using ZXCRM.Data.UnitOfWork;
using ZXCRM.Service.DTOs;
using ZXCRM.Service.Interface;

namespace ZXCRM.Service.Service
{
    public class AuthService : IAuthService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;

        public AuthService(IUnitOfWork unitOfWork, IMapper mapper, IConfiguration configuration)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _configuration = configuration;
        }

        public async Task<TokenDTO> LoginAsync(LoginDTO loginDto)
        {
            var users = await _unitOfWork.Users.FindAsync(u => u.Username == loginDto.Username);
            var user = users.FirstOrDefault();

            if (user == null)
            {
                throw new Exception("用户名或密码错误");
            }

            bool isPasswordValid = BCrypt.Net.BCrypt.Verify(loginDto.Password, user.Password);
            if (!isPasswordValid)
            {
                throw new Exception("用户名或密码错误");
            }

            // 获取用户的部门信息
            var department = await _unitOfWork.Departments.GetByIdAsync(user.DepartmentId);

            var token = GenerateJwtToken(user);

            // 手动创建UserDTO以确保所有字段都有值
            var userDto = new UserDTO
            {
                Id = user.Id,
                Username = user.Username,
                Name = user.Name,
                Gender = user.Gender,
                Email = user.Email,
                Phone = user.Phone,
                DepartmentId = user.DepartmentId,
                DepartmentName = department?.Name ?? string.Empty,
                CreatedAt = user.CreatedAt,
                UpdatedAt = user.UpdatedAt
            };

            return new TokenDTO
            {
                AccessToken = token,
                ExpiresAt = DateTime.Now.AddHours(24),
                User = userDto
            };
        }

        public Task<bool> ValidateTokenAsync(string token)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_configuration["Jwt:Key"] ?? "ZXCRMDefaultSecretKeyForDevelopment");

            try
            {
                tokenHandler.ValidateToken(token, new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidIssuer = _configuration["Jwt:Issuer"],
                    ValidateAudience = true,
                    ValidAudience = _configuration["Jwt:Audience"],
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero
                }, out _);

                return Task.FromResult(true);
            }
            catch
            {
                return Task.FromResult(false);
            }
        }

        public async Task<UserDTO?> GetUserFromTokenAsync(string token)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_configuration["Jwt:Key"] ?? "ZXCRMDefaultSecretKeyForDevelopment");

            try
            {
                var principal = tokenHandler.ValidateToken(token, new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidIssuer = _configuration["Jwt:Issuer"],
                    ValidateAudience = true,
                    ValidAudience = _configuration["Jwt:Audience"],
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero
                }, out _);

                var userIdClaim = principal.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
                {
                    return null;
                }

                var user = await _unitOfWork.Users.GetByIdAsync(userId);
                if (user == null)
                {
                    return null;
                }

                return _mapper.Map<UserDTO>(user);
            }
            catch
            {
                return null;
            }
        }

        public async Task<UserDTO?> GetCurrentUserAsync()
        {
            // 这个方法应该由调用者提供token
            // 在WPF应用程序中，应该从App.Current.Properties中获取token
            // 在服务层，我们不应该直接访问App.Current
            return null;
        }

        private string GenerateJwtToken(User user)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_configuration["Jwt:Key"] ?? "ZXCRMDefaultSecretKeyForDevelopment");

            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new Claim(ClaimTypes.Name, user.Username),
                new Claim(ClaimTypes.GivenName, user.Name),
                new Claim(ClaimTypes.Role, user.Role), // 添加角色信息
                new Claim("DepartmentId", user.DepartmentId.ToString())
            };

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.AddHours(24),
                Issuer = _configuration["Jwt:Issuer"],
                Audience = _configuration["Jwt:Audience"],
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
            };

            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }
    }
}
