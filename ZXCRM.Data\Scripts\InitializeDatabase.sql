-- 创建ZXCRM数据库
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'ZXCRM')
BEGIN
    CREATE DATABASE ZXCRM
	ON PRIMARY
( NAME = ZXCRM_data,
    FILENAME = 'D:\database\ZXCRM\ZXCRM_data.mdf',
    SIZE = 10MB,
    MAXSIZE = UNLIMITED,
    FILEGROWTH = 5MB )
LOG ON
( NAME = ZXCRM_log,
    FILENAME = 'D:\database\ZXCRM\ZXCRM_log.ldf',
    SIZE = 5MB,
    MAXSIZE = 2048MB,
    FILEGROWTH = 5MB ) ;

END
GO

USE ZXCRM;
GO

-- 创建部门表
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Departments]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Departments](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [Name] [nvarchar](50) NOT NULL,
        [Code] [nvarchar](20) NOT NULL,
        [ParentId] [int] NULL,
        [CreatedAt] [datetime] NOT NULL,
        [UpdatedAt] [datetime] NOT NULL,
        [IsDeleted] [bit] NOT NULL DEFAULT(0),
        CONSTRAINT [PK_Departments] PRIMARY KEY CLUSTERED ([Id] ASC)
    );
END
GO

-- 创建用户表
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Users](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [Username] [nvarchar](50) NOT NULL,
        [Password] [nvarchar](100) NOT NULL,
        [Name] [nvarchar](50) NOT NULL,
        [Gender] [nvarchar](10) NULL,
        [Email] [nvarchar](100) NULL,
        [Phone] [nvarchar](20) NULL,
        [DepartmentId] [int] NOT NULL,
        [CreatedAt] [datetime] NOT NULL,
        [UpdatedAt] [datetime] NOT NULL,
        [IsDeleted] [bit] NOT NULL DEFAULT(0),
        CONSTRAINT [PK_Users] PRIMARY KEY CLUSTERED ([Id] ASC)
    );
END
GO

-- 创建权限表
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Permissions]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Permissions](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [Name] [nvarchar](50) NOT NULL,
        [Code] [nvarchar](50) NOT NULL,
        [Description] [nvarchar](200) NULL,
        CONSTRAINT [PK_Permissions] PRIMARY KEY CLUSTERED ([Id] ASC)
    );
END
GO

-- 创建用户权限表
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[UserPermissions]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[UserPermissions](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [UserId] [int] NOT NULL,
        [PermissionId] [int] NOT NULL,
        [ModuleType] [nvarchar](50) NOT NULL,
        CONSTRAINT [PK_UserPermissions] PRIMARY KEY CLUSTERED ([Id] ASC)
    );
END
GO

-- 创建商机表
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Opportunities]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Opportunities](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [Name] [nvarchar](100) NOT NULL,
        [CustomerName] [nvarchar](100) NOT NULL,
        [ContactName] [nvarchar](50) NULL,
        [ContactPhone] [nvarchar](20) NULL,
        [Content] [nvarchar](500) NULL,
        [Status] [nvarchar](20) NOT NULL,
        [CreatedById] [int] NOT NULL,
        [CreatedAt] [datetime] NOT NULL,
        [UpdatedAt] [datetime] NOT NULL,
        [IsDeleted] [bit] NOT NULL DEFAULT(0),
        CONSTRAINT [PK_Opportunities] PRIMARY KEY CLUSTERED ([Id] ASC)
    );
END
GO

-- 创建订单表
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Orders]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Orders](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [Name] [nvarchar](100) NOT NULL,
        [Code] [nvarchar](50) NOT NULL,
        [CustomerName] [nvarchar](100) NOT NULL,
        [ContactName] [nvarchar](50) NULL,
        [ContactPhone] [nvarchar](20) NULL,
        [OpportunityId] [int] NULL,
        [Amount] [decimal](18, 2) NOT NULL,
        [Currency] [nvarchar](20) NOT NULL,
        [SettlementAmount] [decimal](18, 2) NOT NULL,
        [AccountManagerId] [int] NOT NULL,
        [DepartmentId] [int] NOT NULL,
        [SignDate] [datetime] NOT NULL,
        [Status] [nvarchar](20) NOT NULL,
        [ProjectManagerId] [int] NULL,
        [PerformanceDepartmentId] [int] NULL,
        [CreatedById] [int] NOT NULL,
        [CreatedAt] [datetime] NOT NULL,
        [UpdatedAt] [datetime] NOT NULL,
        [IsDeleted] [bit] NOT NULL DEFAULT(0),
        CONSTRAINT [PK_Orders] PRIMARY KEY CLUSTERED ([Id] ASC)
    );
END
GO

-- 创建款项表
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Payments]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Payments](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [Name] [nvarchar](100) NOT NULL,
        [Code] [nvarchar](50) NOT NULL,
        [OrderId] [int] NOT NULL,
        [PaymentType] [nvarchar](20) NOT NULL,
        [Amount] [decimal](18, 2) NOT NULL,
        [Currency] [nvarchar](20) NOT NULL,
        [SettlementAmount] [decimal](18, 2) NOT NULL,
        [ExpectedPaymentDate] [datetime] NULL,
        [ActualPaymentDate] [datetime] NULL,
        [InvoiceStatus] [nvarchar](20) NOT NULL,
        [InvoiceDate] [datetime] NULL,
        [CreatedAt] [datetime] NOT NULL,
        [UpdatedAt] [datetime] NOT NULL,
        [IsDeleted] [bit] NOT NULL DEFAULT(0),
        CONSTRAINT [PK_Payments] PRIMARY KEY CLUSTERED ([Id] ASC)
    );
END
GO

-- 创建发票表
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Invoices]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Invoices](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [PaymentId] [int] NOT NULL,
        [Company] [nvarchar](50) NOT NULL,
        [Type] [nvarchar](50) NOT NULL,
        [TaxRate] [decimal](5, 2) NOT NULL,
        [Amount] [decimal](18, 2) NOT NULL,
        [Status] [nvarchar](20) NOT NULL DEFAULT('Normal'),
        [Content] [nvarchar](200) NULL,
        [Code] [nvarchar](50) NULL,
        [ReceiverName] [nvarchar](50) NULL,
        [ReceiverPhone] [nvarchar](20) NULL,
        [MailingAddress] [nvarchar](200) NULL,
        [ApplicantPhone] [nvarchar](20) NULL,
        [CustomerEmail] [nvarchar](100) NULL,
        [CreatedById] [int] NOT NULL,
        [CreatedAt] [datetime] NOT NULL,
        [UpdatedAt] [datetime] NOT NULL,
        [IsDeleted] [bit] NOT NULL DEFAULT(0),
        CONSTRAINT [PK_Invoices] PRIMARY KEY CLUSTERED ([Id] ASC)
    );
END
GO

-- 添加外键约束
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE object_id = OBJECT_ID(N'[dbo].[FK_Departments_Departments]') AND parent_object_id = OBJECT_ID(N'[dbo].[Departments]'))
BEGIN
    ALTER TABLE [dbo].[Departments] WITH CHECK ADD CONSTRAINT [FK_Departments_Departments] FOREIGN KEY([ParentId])
    REFERENCES [dbo].[Departments] ([Id]);
END
GO

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE object_id = OBJECT_ID(N'[dbo].[FK_Users_Departments]') AND parent_object_id = OBJECT_ID(N'[dbo].[Users]'))
BEGIN
    ALTER TABLE [dbo].[Users] WITH CHECK ADD CONSTRAINT [FK_Users_Departments] FOREIGN KEY([DepartmentId])
    REFERENCES [dbo].[Departments] ([Id]);
END
GO

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE object_id = OBJECT_ID(N'[dbo].[FK_UserPermissions_Users]') AND parent_object_id = OBJECT_ID(N'[dbo].[UserPermissions]'))
BEGIN
    ALTER TABLE [dbo].[UserPermissions] WITH CHECK ADD CONSTRAINT [FK_UserPermissions_Users] FOREIGN KEY([UserId])
    REFERENCES [dbo].[Users] ([Id]);
END
GO

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE object_id = OBJECT_ID(N'[dbo].[FK_UserPermissions_Permissions]') AND parent_object_id = OBJECT_ID(N'[dbo].[UserPermissions]'))
BEGIN
    ALTER TABLE [dbo].[UserPermissions] WITH CHECK ADD CONSTRAINT [FK_UserPermissions_Permissions] FOREIGN KEY([PermissionId])
    REFERENCES [dbo].[Permissions] ([Id]);
END
GO

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE object_id = OBJECT_ID(N'[dbo].[FK_Opportunities_Users]') AND parent_object_id = OBJECT_ID(N'[dbo].[Opportunities]'))
BEGIN
    ALTER TABLE [dbo].[Opportunities] WITH CHECK ADD CONSTRAINT [FK_Opportunities_Users] FOREIGN KEY([CreatedById])
    REFERENCES [dbo].[Users] ([Id]);
END
GO

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE object_id = OBJECT_ID(N'[dbo].[FK_Orders_Opportunities]') AND parent_object_id = OBJECT_ID(N'[dbo].[Orders]'))
BEGIN
    ALTER TABLE [dbo].[Orders] WITH CHECK ADD CONSTRAINT [FK_Orders_Opportunities] FOREIGN KEY([OpportunityId])
    REFERENCES [dbo].[Opportunities] ([Id]);
END
GO

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE object_id = OBJECT_ID(N'[dbo].[FK_Orders_Users_AccountManager]') AND parent_object_id = OBJECT_ID(N'[dbo].[Orders]'))
BEGIN
    ALTER TABLE [dbo].[Orders] WITH CHECK ADD CONSTRAINT [FK_Orders_Users_AccountManager] FOREIGN KEY([AccountManagerId])
    REFERENCES [dbo].[Users] ([Id]);
END
GO

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE object_id = OBJECT_ID(N'[dbo].[FK_Orders_Users_ProjectManager]') AND parent_object_id = OBJECT_ID(N'[dbo].[Orders]'))
BEGIN
    ALTER TABLE [dbo].[Orders] WITH CHECK ADD CONSTRAINT [FK_Orders_Users_ProjectManager] FOREIGN KEY([ProjectManagerId])
    REFERENCES [dbo].[Users] ([Id]);
END
GO

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE object_id = OBJECT_ID(N'[dbo].[FK_Orders_Departments]') AND parent_object_id = OBJECT_ID(N'[dbo].[Orders]'))
BEGIN
    ALTER TABLE [dbo].[Orders] WITH CHECK ADD CONSTRAINT [FK_Orders_Departments] FOREIGN KEY([DepartmentId])
    REFERENCES [dbo].[Departments] ([Id]);
END
GO

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE object_id = OBJECT_ID(N'[dbo].[FK_Orders_Departments_Performance]') AND parent_object_id = OBJECT_ID(N'[dbo].[Orders]'))
BEGIN
    ALTER TABLE [dbo].[Orders] WITH CHECK ADD CONSTRAINT [FK_Orders_Departments_Performance] FOREIGN KEY([PerformanceDepartmentId])
    REFERENCES [dbo].[Departments] ([Id]);
END
GO

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE object_id = OBJECT_ID(N'[dbo].[FK_Orders_Users_CreatedBy]') AND parent_object_id = OBJECT_ID(N'[dbo].[Orders]'))
BEGIN
    ALTER TABLE [dbo].[Orders] WITH CHECK ADD CONSTRAINT [FK_Orders_Users_CreatedBy] FOREIGN KEY([CreatedById])
    REFERENCES [dbo].[Users] ([Id]);
END
GO

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE object_id = OBJECT_ID(N'[dbo].[FK_Payments_Orders]') AND parent_object_id = OBJECT_ID(N'[dbo].[Payments]'))
BEGIN
    ALTER TABLE [dbo].[Payments] WITH CHECK ADD CONSTRAINT [FK_Payments_Orders] FOREIGN KEY([OrderId])
    REFERENCES [dbo].[Orders] ([Id]);
END
GO

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE object_id = OBJECT_ID(N'[dbo].[FK_Invoices_Payments]') AND parent_object_id = OBJECT_ID(N'[dbo].[Invoices]'))
BEGIN
    ALTER TABLE [dbo].[Invoices] WITH CHECK ADD CONSTRAINT [FK_Invoices_Payments] FOREIGN KEY([PaymentId])
    REFERENCES [dbo].[Payments] ([Id]);
END
GO

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE object_id = OBJECT_ID(N'[dbo].[FK_Invoices_Users]') AND parent_object_id = OBJECT_ID(N'[dbo].[Invoices]'))
BEGIN
    ALTER TABLE [dbo].[Invoices] WITH CHECK ADD CONSTRAINT [FK_Invoices_Users] FOREIGN KEY([CreatedById])
    REFERENCES [dbo].[Users] ([Id]);
END
GO

-- 插入初始数据
-- 插入权限数据
IF NOT EXISTS (SELECT * FROM [dbo].[Permissions])
BEGIN
    INSERT INTO [dbo].[Permissions] ([Name], [Code], [Description])
    VALUES
        (N'查询', N'Query', N'查询数据权限'),
        (N'新建', N'Create', N'新建数据权限'),
        (N'修改', N'Update', N'修改数据权限'),
        (N'删除', N'Delete', N'删除数据权限');
END
GO

-- 插入初始部门
IF NOT EXISTS (SELECT * FROM [dbo].[Departments])
BEGIN
    INSERT INTO [dbo].[Departments] ([Name], [Code], [ParentId], [CreatedAt], [UpdatedAt], [IsDeleted])
    VALUES (N'管理部门', N'ADMIN', NULL, GETDATE(), GETDATE(), 0);
END
GO

-- 插入管理员用户
IF NOT EXISTS (SELECT * FROM [dbo].[Users] WHERE [Username] = N'admin')
BEGIN
    DECLARE @DepartmentId INT;
    SELECT @DepartmentId = [Id] FROM [dbo].[Departments] WHERE [Code] = N'ADMIN';

    INSERT INTO [dbo].[Users] ([Username], [Password], [Name], [Gender], [Email], [DepartmentId], [CreatedAt], [UpdatedAt], [IsDeleted])
    VALUES (N'admin', N'$2a$11$K3.6UaYlcWQo2kRwMTlFZuLcDlbVqJz5v8HkQ/JwQP.LFkIiDH.Hy', N'系统管理员', N'男', N'<EMAIL>', @DepartmentId, GETDATE(), GETDATE(), 0);
END
GO

-- 为管理员分配所有权限
IF NOT EXISTS (SELECT * FROM [dbo].[UserPermissions])
BEGIN
    DECLARE @AdminId INT;
    SELECT @AdminId = [Id] FROM [dbo].[Users] WHERE [Username] = N'admin';

    INSERT INTO [dbo].[UserPermissions] ([UserId], [PermissionId], [ModuleType])
    SELECT @AdminId, p.[Id], m.[ModuleType]
    FROM [dbo].[Permissions] p
    CROSS JOIN (
        SELECT N'商机' AS [ModuleType] UNION ALL
        SELECT N'订单' UNION ALL
        SELECT N'款项' UNION ALL
        SELECT N'发票'
    ) m;
END
GO
