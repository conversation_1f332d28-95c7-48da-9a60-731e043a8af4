using Microsoft.AspNetCore.Mvc;

namespace ZXCRM.WebAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public abstract class BaseController : ControllerBase
    {
        protected IActionResult Success(object? data = null, string message = "操作成功")
        {
            return Ok(new
            {
                Success = true,
                Message = message,
                Data = data
            });
        }

        protected IActionResult Failure(string message = "操作失败", object? errors = null)
        {
            return BadRequest(new
            {
                Success = false,
                Message = message,
                Errors = errors
            });
        }

        protected int GetCurrentUserId()
        {
            var userIdClaim = User.Claims.FirstOrDefault(c => c.Type == "UserId");
            if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
            {
                return userId;
            }
            return 0;
        }
    }
}
