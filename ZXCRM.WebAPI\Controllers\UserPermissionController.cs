using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ZXCRM.Service.Interface;
using ZXCRM.Service.DTOs;

namespace ZXCRM.WebAPI.Controllers
{
    [Authorize]
    public class UserPermissionController : BaseController
    {
        private readonly IUserPermissionService _userPermissionService;
        private readonly ILogger<UserPermissionController> _logger;

        public UserPermissionController(IUserPermissionService userPermissionService, ILogger<UserPermissionController> logger)
        {
            _userPermissionService = userPermissionService;
            _logger = logger;
        }

        [HttpGet("user/{userId}")]
        public async Task<IActionResult> GetUserPermissions(int userId)
        {
            try
            {
                _logger.LogInformation("Getting permissions for user: {UserId}", userId);
                var permissions = await _userPermissionService.GetByUserIdAsync(userId);
                return Success(permissions, "获取用户权限成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting permissions for user: {UserId}", userId);
                return Failure("获取用户权限失败");
            }
        }



        [HttpPut("user/{userId}")]
        public async Task<IActionResult> UpdateUserPermissions(int userId, [FromBody] List<UserPermissionDto> permissions)
        {
            try
            {
                _logger.LogInformation("Updating permissions for user: {UserId}", userId);

                // 这里需要实现批量更新用户权限的逻辑
                // 可能需要先删除现有权限，再添加新权限
                await _userPermissionService.UpdateUserPermissionsAsync(userId, permissions);
                return Success(null, "更新用户权限成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating permissions for user: {UserId}", userId);
                return Failure("更新用户权限失败");
            }
        }
    }
}
