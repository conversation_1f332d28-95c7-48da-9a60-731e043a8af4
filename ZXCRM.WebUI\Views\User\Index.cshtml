@model ZXCRM.WebUI.Models.ViewModels.UserListViewModel
@using ZXCRM.WebUI.Helpers
@{
    ViewData["Title"] = "用户管理";
}

<!-- 数据页面容器 -->
<div class="data-page-container">
    <!-- 查询区域（可收缩） -->
    <div class="query-section" id="querySection">
        <div class="query-header">
            <div class="query-title">
                <i class="fas fa-filter"></i>
                <span>筛选条件</span>
                <span class="badge badge-info ml-2">共 @Model.TotalCount 个用户</span>
            </div>
            <button type="button" class="query-toggle" onclick="toggleQuerySection()">
                <i class="fas fa-chevron-up" id="queryToggleIcon"></i>
            </button>
        </div>
        <div class="query-content" id="queryContent">
            <form asp-action="Index" method="get" class="query-form">
                <div class="query-row">
                    <div class="query-item">
                        <label>部门:</label>
                        <select name="departmentFilter" class="form-control form-control-sm">
                            <option value="">全部部门</option>
                            <!-- 这里可以添加部门选项 -->
                        </select>
                    </div>
                    <div class="query-item">
                        <label>状态:</label>
                        <select name="statusFilter" class="form-control form-control-sm">
                            <option value="">全部状态</option>
                            <option value="active">激活</option>
                            <option value="inactive">禁用</option>
                        </select>
                    </div>
                    <div class="query-item query-item-search">
                        <label>搜索:</label>
                        <div class="input-group input-group-sm">
                            <input type="text" name="searchTerm" value="@Model.SearchTerm" class="form-control" placeholder="用户名、姓名...">
                            <div class="input-group-append">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                                @if (!string.IsNullOrEmpty(Model.SearchTerm))
                                {
                                    <a asp-action="Index" class="btn btn-outline-danger" title="清除">
                                        <i class="fas fa-times"></i>
                                    </a>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
        <div class="action-left">
            <div class="btn-group btn-group-sm" role="group">
                <a asp-action="Create" class="btn btn-primary">
                    <i class="fas fa-plus"></i> 新增
                </a>
                <button type="button" class="btn btn-secondary" id="batchOperationBtn" disabled>
                    <i class="fas fa-cogs"></i> 批量操作
                </button>
            </div>
        </div>
        <div class="action-right">
            <div class="view-options">
                <span class="text-muted">每页:</span>
                <select class="form-control form-control-sm d-inline-block" style="width: auto;" onchange="changePageSize(this.value)">
                    <option value="25" selected="@(Model.PageSize == 25)">25</option>
                    <option value="50" selected="@(Model.PageSize == 50)">50</option>
                    <option value="100" selected="@(Model.PageSize == 100)">100</option>
                </select>
            </div>
        </div>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
        @if (Model.Users.Any())
        {
            <div class="table-container">
                <table class="table table-compact">
                    <thead class="table-header-fixed">
                        <tr>
                            <th width="35">
                                <div class="form-check form-check-sm">
                                    <input type="checkbox" class="form-check-input" id="selectAll">
                                    <label class="form-check-label" for="selectAll"></label>
                                </div>
                            </th>
                            <th width="100">用户名</th>
                            <th width="80">姓名</th>
                            <th width="160">邮箱</th>
                            <th width="100">部门</th>
                            <th width="60">角色</th>
                            <th width="50">状态</th>
                            <th width="100">创建时间</th>
                            <th width="90">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var user in Model.Users)
                        {
                            <tr class="table-row">
                                <td>
                                    <div class="form-check form-check-sm">
                                        <input type="checkbox" class="form-check-input user-checkbox" id="<EMAIL>" value="@user.Id">
                                        <label class="form-check-label" for="<EMAIL>"></label>
                                    </div>
                                </td>
                                <td class="text-nowrap">
                                    <strong>@user.Username</strong>
                                </td>
                                <td class="text-nowrap">@user.Name</td>
                                <td>
                                    @if (!string.IsNullOrEmpty(user.Email))
                                    {
                                        <a href="mailto:@user.Email" class="text-truncate d-block" style="max-width: 140px;" title="@user.Email">@user.Email</a>
                                    }
                                    else
                                    {
                                        <span class="text-muted">-</span>
                                    }
                                </td>
                                <td>
                                    <span class="badge badge-light badge-xs">@user.DepartmentName</span>
                                </td>
                                <td>
                                    <span class="badge badge-primary badge-xs">@user.Role</span>
                                </td>
                                <td>
                                    <span class="badge <EMAIL>(user.Status) badge-xs">
                                        @if (user.Status == "Active")
                                        {
                                            <i class="fas fa-check"></i>
                                        }
                                        else if (user.Status == "Locked")
                                        {
                                            <i class="fas fa-lock"></i>
                                        }
                                        else
                                        {
                                            <i class="fas fa-times"></i>
                                        }
                                        @EnumHelper.GetUserStatusText(user.Status)
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">@user.CreatedAt.ToString("MM-dd")</small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-xs" role="group">
                                        <a asp-action="Details" asp-route-id="@user.Id" class="btn btn-outline-info btn-xs" title="查看">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a asp-action="Edit" asp-route-id="@user.Id" class="btn btn-outline-warning btn-xs" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-danger btn-xs" title="删除"
                                                onclick="confirmDelete(@user.Id, '@user.Username')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="empty-state">
                <div class="empty-state-content">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无用户数据</h5>
                    @if (!string.IsNullOrEmpty(Model.SearchTerm))
                    {
                        <p class="text-muted">没有找到包含 "@Model.SearchTerm" 的用户</p>
                        <a asp-action="Index" class="btn btn-secondary">清除搜索</a>
                    }
                    else
                    {
                        <a asp-action="Create" class="btn btn-primary">创建第一个用户</a>
                    }
                </div>
            </div>
        }
    </div>

    <!-- 分页栏 -->
    @if (Model.TotalCount > 0)
    {
        <div class="pagination-bar">
            <div class="pagination-info">
                <small class="text-muted">
                    第 @((Model.PageIndex - 1) * Model.PageSize + 1)-@Math.Min(Model.PageIndex * Model.PageSize, Model.TotalCount) 条，共 @Model.TotalCount 条
                </small>
            </div>
            @if (Model.TotalCount > Model.PageSize)
            {
                var totalPages = (int)Math.Ceiling((double)Model.TotalCount / Model.PageSize);
                var startPage = Math.Max(1, Model.PageIndex - 2);
                var endPage = Math.Min(totalPages, Model.PageIndex + 2);
                <nav class="pagination-nav">
                    <ul class="pagination pagination-sm m-0">
                        @if (Model.PageIndex > 1)
                        {
                            <li class="page-item">
                                <a class="page-link" asp-action="Index" asp-route-pageIndex="@(Model.PageIndex - 1)" asp-route-searchTerm="@Model.SearchTerm" asp-route-pageSize="@Model.PageSize">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        }

                        @for (int i = startPage; i <= endPage; i++)
                        {
                            <li class="page-item @(i == Model.PageIndex ? "active" : "")">
                                <a class="page-link" asp-action="Index" asp-route-pageIndex="@i" asp-route-searchTerm="@Model.SearchTerm" asp-route-pageSize="@Model.PageSize">@i</a>
                            </li>
                        }

                        @if (Model.PageIndex < totalPages)
                        {
                            <li class="page-item">
                                <a class="page-link" asp-action="Index" asp-route-pageIndex="@(Model.PageIndex + 1)" asp-route-searchTerm="@Model.SearchTerm" asp-route-pageSize="@Model.PageSize">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        }
                    </ul>
                </nav>
            }
        </div>
    }
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>您确定要删除用户 <strong id="deleteUserName"></strong> 吗？</p>
                <p class="text-danger"><small>此操作不可撤销！</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(userId, userName) {
            $('#deleteUserName').text(userName);
            $('#deleteForm').attr('action', '@Url.Action("Delete")/' + userId);
            $('#deleteModal').modal('show');
        }

        // 查询区收缩切换
        function toggleQuerySection() {
            const queryContent = document.getElementById('queryContent');
            const queryToggleIcon = document.getElementById('queryToggleIcon');
            const isCollapsed = queryContent.style.display === 'none';

            if (isCollapsed) {
                queryContent.style.display = 'block';
                queryToggleIcon.className = 'fas fa-chevron-up';
                localStorage.setItem('querySection_collapsed', 'false');
            } else {
                queryContent.style.display = 'none';
                queryToggleIcon.className = 'fas fa-chevron-down';
                localStorage.setItem('querySection_collapsed', 'true');
            }
        }

        // 分页大小切换
        function changePageSize(pageSize) {
            var url = new URL(window.location);
            url.searchParams.set('pageSize', pageSize);
            url.searchParams.set('pageIndex', '1'); // 重置到第一页
            window.location.href = url.toString();
        }

        $(document).ready(function() {
            // 恢复查询区收缩状态
            const isCollapsed = localStorage.getItem('querySection_collapsed') === 'true';
            if (isCollapsed) {
                document.getElementById('queryContent').style.display = 'none';
                document.getElementById('queryToggleIcon').className = 'fas fa-chevron-down';
            }
            // 全选/取消全选
            $('#selectAll').change(function() {
                $('.user-checkbox').prop('checked', this.checked);
                updateBatchOperationButton();
            });

            // 单个复选框变化
            $('.user-checkbox').change(function() {
                var totalCheckboxes = $('.user-checkbox').length;
                var checkedCheckboxes = $('.user-checkbox:checked').length;

                $('#selectAll').prop('checked', totalCheckboxes === checkedCheckboxes);
                updateBatchOperationButton();
            });

            // 更新批量操作按钮状态
            function updateBatchOperationButton() {
                var checkedCount = $('.user-checkbox:checked').length;
                $('#batchOperationBtn').prop('disabled', checkedCount === 0);

                if (checkedCount > 0) {
                    $('#batchOperationBtn').html('<i class="fas fa-cogs"></i> 批量操作 (' + checkedCount + ')');
                } else {
                    $('#batchOperationBtn').html('<i class="fas fa-cogs"></i> 批量操作');
                }
            }

            // 批量操作点击事件
            $('#batchOperationBtn').click(function() {
                var selectedIds = $('.user-checkbox:checked').map(function() {
                    return this.value;
                }).get();

                if (selectedIds.length > 0) {
                    // 这里可以显示批量操作菜单
                    alert('选中了 ' + selectedIds.length + ' 个用户\nID: ' + selectedIds.join(', '));
                }
            });

            // 表格行点击选择（优化为仅在空白区域点击）
            $('tbody tr').click(function(e) {
                if (e.target.type !== 'checkbox' &&
                    !$(e.target).closest('.btn').length &&
                    !$(e.target).closest('a').length &&
                    !$(e.target).closest('.badge').length) {
                    var checkbox = $(this).find('.user-checkbox');
                    checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
                }
            });

            // 表格行悬停效果
            $('tbody tr').hover(
                function() {
                    $(this).addClass('table-row-hover');
                },
                function() {
                    $(this).removeClass('table-row-hover');
                }
            );
        });

        // 显示提示消息
        @if (TempData["SuccessMessage"] != null)
        {
            <text>
            toastr.success('@TempData["SuccessMessage"]');
            </text>
        }

        @if (TempData["ErrorMessage"] != null)
        {
            <text>
            toastr.error('@TempData["ErrorMessage"]');
            </text>
        }
    </script>
}
