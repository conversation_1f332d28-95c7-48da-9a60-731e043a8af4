@model ZXCRM.WebUI.Models.ViewModels.CreatePaymentViewModel
@{
    ViewData["Title"] = "新增款项";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">新增款项</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a asp-controller="Dashboard" asp-action="Index">首页</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Payment" asp-action="Index">款项管理</a></li>
                    <li class="breadcrumb-item active">新增款项</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-plus"></i> 款项信息
                        </h3>
                    </div>
                    <form asp-action="Create" method="post">
                        <div class="card-body">
                            <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                            <div class="row">
                                <div class="col-md-6">
                                    <!-- 款项名称 -->
                                    <div class="form-group">
                                        <label asp-for="Name" class="form-label required"></label>
                                        <input asp-for="Name" class="form-control" placeholder="请输入款项名称" />
                                        <span asp-validation-for="Name" class="text-danger"></span>
                                    </div>

                                    <!-- 关联订单 -->
                                    <div class="form-group">
                                        <label asp-for="OrderId" class="form-label required"></label>
                                        <select asp-for="OrderId" class="form-control">
                                            <option value="">请选择关联订单</option>
                                            @foreach (var order in Model.Orders)
                                            {
                                                <option value="@order.Id">
                                                    @order.Name (@order.Code) - @order.CustomerName - @order.Amount.ToString("N2") @order.Currency
                                                </option>
                                            }
                                        </select>
                                        <span asp-validation-for="OrderId" class="text-danger"></span>
                                    </div>

                                    <!-- 款项类型 -->
                                    <div class="form-group">
                                        <label asp-for="PaymentType" class="form-label required"></label>
                                        <select asp-for="PaymentType" class="form-control">
                                            <option value="">请选择款项类型</option>
                                            <option value="首付">首付</option>
                                            <option value="2付">2付</option>
                                            <option value="3付">3付</option>
                                            <option value="4付">4付</option>
                                            <option value="5付">5付</option>
                                            <option value="尾款">尾款</option>
                                            <option value="全款">全款</option>
                                        </select>
                                        <span asp-validation-for="PaymentType" class="text-danger"></span>
                                    </div>

                                    <!-- 款项金额 -->
                                    <div class="form-group">
                                        <label asp-for="Amount" class="form-label required"></label>
                                        <div class="input-group">
                                            <input asp-for="Amount" class="form-control" placeholder="0.00" step="0.01" />
                                            <div class="input-group-append">
                                                <select asp-for="Currency" class="form-control">
                                                    <option value="CNY">CNY</option>
                                                    <option value="USD">USD</option>
                                                    <option value="JPY">JPY</option>
                                                    <option value="EUR">EUR</option>
                                                </select>
                                            </div>
                                        </div>
                                        <span asp-validation-for="Amount" class="text-danger"></span>
                                    </div>

                                    <!-- 结算金额 -->
                                    <div class="form-group">
                                        <label asp-for="SettlementAmount" class="form-label required"></label>
                                        <input asp-for="SettlementAmount" class="form-control" placeholder="0.00" step="0.01" />
                                        <span asp-validation-for="SettlementAmount" class="text-danger"></span>
                                        <small class="form-text text-muted">结算金额通常与款项金额相同</small>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <!-- 预期回款日期 -->
                                    <div class="form-group">
                                        <label asp-for="ExpectedPaymentDate" class="form-label"></label>
                                        <input asp-for="ExpectedPaymentDate" class="form-control" type="date" />
                                        <span asp-validation-for="ExpectedPaymentDate" class="text-danger"></span>
                                        <small class="form-text text-muted">预期的回款日期</small>
                                    </div>

                                    <!-- 实际回款日期 -->
                                    <div class="form-group">
                                        <label asp-for="ActualPaymentDate" class="form-label"></label>
                                        <input asp-for="ActualPaymentDate" class="form-control" type="date" />
                                        <span asp-validation-for="ActualPaymentDate" class="text-danger"></span>
                                        <small class="form-text text-muted">如果已回款，请填写实际回款日期</small>
                                    </div>

                                    <!-- 开票状态 -->
                                    <div class="form-group">
                                        <label asp-for="InvoiceStatus" class="form-label required"></label>
                                        <select asp-for="InvoiceStatus" class="form-control">
                                            <option value="未开票">未开票</option>
                                            <option value="已开票">已开票</option>
                                            <option value="已寄出">已寄出</option>
                                        </select>
                                        <span asp-validation-for="InvoiceStatus" class="text-danger"></span>
                                    </div>

                                    <!-- 开票日期 -->
                                    <div class="form-group">
                                        <label asp-for="InvoiceDate" class="form-label"></label>
                                        <input asp-for="InvoiceDate" class="form-control" type="date" />
                                        <span asp-validation-for="InvoiceDate" class="text-danger"></span>
                                        <small class="form-text text-muted">如果已开票，请填写开票日期</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card-footer">
                            <div class="row">
                                <div class="col-md-6">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> 保存
                                    </button>
                                    <a asp-action="Index" class="btn btn-secondary ml-2">
                                        <i class="fas fa-times"></i> 取消
                                    </a>
                                </div>
                                <div class="col-md-6 text-right">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        标有 <span class="text-danger">*</span> 的字段为必填项
                                    </small>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="col-md-4">
                <!-- 帮助信息 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-question-circle"></i> 填写说明
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb"></i> 提示</h6>
                            <ul class="mb-0">
                                <li>款项名称建议包含款项类型和期数信息</li>
                                <li>必须选择一个已存在的订单进行关联</li>
                                <li>结算金额通常与款项金额相同，特殊情况可调整</li>
                                <li>预期回款日期有助于跟踪回款进度</li>
                                <li>开票状态影响发票管理流程</li>
                            </ul>
                        </div>

                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> 注意</h6>
                            <p class="mb-0">
                                创建款项后，系统会自动生成款项编号。
                                款项与订单关联后不可更改。
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 款项类型说明 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-tags"></i> 款项类型说明
                        </h3>
                    </div>
                    <div class="card-body">
                        <dl>
                            <dt>首付</dt>
                            <dd>订单签约时客户支付的第一笔款项</dd>

                            <dt>2付、3付、4付、5付</dt>
                            <dd>按合同约定分期支付的进度款项</dd>

                            <dt>尾款</dt>
                            <dd>项目完成后支付的最后一笔款项</dd>

                            <dt>全款</dt>
                            <dd>一次性支付的全部订单金额</dd>
                        </dl>
                    </div>
                </div>

                <!-- 快捷操作 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-bolt"></i> 快捷操作
                        </h3>
                    </div>
                    <div class="card-body">
                        <button type="button" class="btn btn-outline-primary btn-sm btn-block" onclick="copyAmount()">
                            <i class="fas fa-copy"></i> 复制款项金额到结算金额
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm btn-block" onclick="setToday()">
                            <i class="fas fa-calendar-day"></i> 设置预期回款日期为今天
                        </button>
                        <button type="button" class="btn btn-outline-info btn-sm btn-block" onclick="setInvoiceDate()">
                            <i class="fas fa-file-invoice"></i> 设置开票日期为今天
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        function copyAmount() {
            const amount = document.getElementById('Amount').value;
            if (amount) {
                document.getElementById('SettlementAmount').value = amount;
                toastr.info('已复制款项金额到结算金额');
            }
        }

        function setToday() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('ExpectedPaymentDate').value = today;
            toastr.info('已设置预期回款日期为今天');
        }

        function setInvoiceDate() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('InvoiceDate').value = today;
            toastr.info('已设置开票日期为今天');
        }

        // 表单验证增强
        $(document).ready(function() {
            // 金额输入验证
            $('#Amount, #SettlementAmount').on('input', function() {
                const value = parseFloat($(this).val());
                if (value <= 0) {
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });

            // 自动复制金额
            $('#Amount').on('blur', function() {
                const settlementAmount = $('#SettlementAmount').val();
                if (!settlementAmount || settlementAmount === '0') {
                    $('#SettlementAmount').val($(this).val());
                }
            });

            // 开票状态变更处理
            $('#InvoiceStatus').on('change', function() {
                const status = $(this).val();
                if (status === '已开票' || status === '已寄出') {
                    if (!$('#InvoiceDate').val()) {
                        const today = new Date().toISOString().split('T')[0];
                        $('#InvoiceDate').val(today);
                        toastr.info('已自动设置开票日期为今天');
                    }
                }
            });

            // 订单选择变更处理
            $('#OrderId').on('change', function() {
                const selectedOption = $(this).find('option:selected');
                if (selectedOption.val()) {
                    // 可以在这里添加根据订单自动填充款项名称的逻辑
                    const orderName = selectedOption.text().split(' (')[0];
                    const paymentType = $('#PaymentType').val();
                    if (paymentType && !$('#Name').val()) {
                        $('#Name').val(orderName + ' - ' + paymentType);
                    }
                }
            });

            // 款项类型变更处理
            $('#PaymentType').on('change', function() {
                const paymentType = $(this).val();
                const orderSelect = $('#OrderId');
                if (paymentType && orderSelect.val() && !$('#Name').val()) {
                    const orderName = orderSelect.find('option:selected').text().split(' (')[0];
                    $('#Name').val(orderName + ' - ' + paymentType);
                }
            });
        });
    </script>
}

@section Styles {
    <style>
        .required::after {
            content: " *";
            color: red;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .is-invalid {
            border-color: #dc3545;
        }
    </style>
}
