@model List<ZXCRM.Service.DTOs.DepartmentDTO>
@{
    ViewData["Title"] = "部门组织架构";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">部门组织架构</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a asp-controller="Dashboard" asp-action="Index">首页</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Department" asp-action="Index">部门管理</a></li>
                    <li class="breadcrumb-item active">组织架构</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <!-- 操作栏 -->
        <div class="row mb-3">
            <div class="col-md-6">
                <div class="btn-group" role="group">
                    <a asp-action="Create" class="btn btn-primary">
                        <i class="fas fa-plus"></i> 新增部门
                    </a>
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-list"></i> 列表视图
                    </a>
                </div>
            </div>
            <div class="col-md-6">
                <div class="btn-group float-right" role="group">
                    <button type="button" class="btn btn-outline-primary" onclick="expandAll()">
                        <i class="fas fa-expand-arrows-alt"></i> 展开全部
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="collapseAll()">
                        <i class="fas fa-compress-arrows-alt"></i> 收起全部
                    </button>
                </div>
            </div>
        </div>

        <!-- 部门树形结构 -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-sitemap"></i> 组织架构图
                </h3>
                <div class="card-tools">
                    <span class="badge badge-info">共 @GetTotalDepartmentCount(Model) 个部门</span>
                </div>
            </div>
            <div class="card-body">
                @if (Model.Any())
                {
                    <div class="department-tree">
                        @foreach (var department in Model)
                        {
                            @await Html.PartialAsync("_DepartmentTreeNode", department)
                        }
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="fas fa-sitemap fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无部门数据</h5>
                        <p class="text-muted">请先创建根部门来建立组织架构</p>
                        <a asp-action="Create" class="btn btn-primary">创建根部门</a>
                    </div>
                }
            </div>
        </div>

        <!-- 部门统计信息 -->
        @if (Model.Any())
        {
            <div class="row mt-3">
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-info"><i class="fas fa-building"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">总部门数</span>
                            <span class="info-box-number">@GetTotalDepartmentCount(Model)</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-success"><i class="fas fa-layer-group"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">根部门数</span>
                            <span class="info-box-number">@Model.Count</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-warning"><i class="fas fa-users"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">总人员数</span>
                            <span class="info-box-number">@GetTotalUserCount(Model)</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-danger"><i class="fas fa-chart-line"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">最大层级</span>
                            <span class="info-box-number">@GetMaxDepth(Model)</span>
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>
</section>

@section Styles {
    <style>
        .department-tree {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .tree-node {
            margin: 5px 0;
        }
        
        .tree-node-content {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            border: 1px solid #e3e6f0;
            border-radius: 6px;
            background: #fff;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .tree-node-content:hover {
            background: #f8f9fc;
            border-color: #d1d3e2;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .tree-toggle {
            width: 20px;
            height: 20px;
            border: none;
            background: #6c757d;
            color: white;
            border-radius: 50%;
            font-size: 10px;
            margin-right: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .tree-toggle:hover {
            background: #5a6268;
        }
        
        .tree-toggle.expanded {
            background: #007bff;
        }
        
        .tree-toggle:disabled {
            background: #e9ecef;
            color: #6c757d;
            cursor: not-allowed;
        }
        
        .tree-children {
            margin-left: 30px;
            border-left: 2px dashed #dee2e6;
            padding-left: 15px;
            margin-top: 5px;
        }
        
        .tree-children.collapsed {
            display: none;
        }
        
        .department-info {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .department-name {
            font-weight: 600;
            color: #2c3e50;
            margin-right: 10px;
        }
        
        .department-code {
            font-size: 0.85em;
            color: #6c757d;
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            margin-right: 10px;
        }
        
        .department-stats {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .user-count {
            font-size: 0.85em;
            color: #495057;
        }
        
        .department-actions {
            display: flex;
            gap: 5px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .tree-node-content:hover .department-actions {
            opacity: 1;
        }
        
        .btn-tree {
            padding: 2px 6px;
            font-size: 0.75em;
            border-radius: 3px;
        }
    </style>
}

@section Scripts {
    <script>
        function expandAll() {
            $('.tree-children').removeClass('collapsed');
            $('.tree-toggle').addClass('expanded').html('<i class="fas fa-minus"></i>');
        }
        
        function collapseAll() {
            $('.tree-children').addClass('collapsed');
            $('.tree-toggle').removeClass('expanded').html('<i class="fas fa-plus"></i>');
        }
        
        function toggleNode(button) {
            const children = $(button).closest('.tree-node').find('> .tree-children');
            const isCollapsed = children.hasClass('collapsed');
            
            if (isCollapsed) {
                children.removeClass('collapsed');
                $(button).addClass('expanded').html('<i class="fas fa-minus"></i>');
            } else {
                children.addClass('collapsed');
                $(button).removeClass('expanded').html('<i class="fas fa-plus"></i>');
            }
        }
        
        function confirmDelete(departmentId, departmentName) {
            if (confirm(`确定要删除部门 "${departmentName}" 吗？\n\n注意：删除部门前请确保该部门下没有用户和子部门！`)) {
                // 创建表单并提交
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '@Url.Action("Delete")/' + departmentId;
                
                // 添加防伪令牌
                const token = document.createElement('input');
                token.type = 'hidden';
                token.name = '__RequestVerificationToken';
                token.value = $('input[name="__RequestVerificationToken"]').val();
                form.appendChild(token);
                
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        // 显示提示消息
        @if (TempData["SuccessMessage"] != null)
        {
            <text>
            toastr.success('@TempData["SuccessMessage"]');
            </text>
        }

        @if (TempData["ErrorMessage"] != null)
        {
            <text>
            toastr.error('@TempData["ErrorMessage"]');
            </text>
        }
        
        // 页面加载完成后默认展开第一层
        $(document).ready(function() {
            $('.tree-node').each(function(index) {
                if ($(this).parents('.tree-children').length === 0) {
                    // 这是根节点，展开它
                    const children = $(this).find('> .tree-children');
                    if (children.length > 0) {
                        children.removeClass('collapsed');
                        $(this).find('> .tree-node-content .tree-toggle').addClass('expanded').html('<i class="fas fa-minus"></i>');
                    }
                }
            });
        });
    </script>
}

@functions {
    private int GetTotalDepartmentCount(List<ZXCRM.Service.DTOs.DepartmentDTO> departments)
    {
        int count = departments.Count;
        foreach (var dept in departments)
        {
            count += GetTotalDepartmentCount(dept.Children);
        }
        return count;
    }
    
    private int GetTotalUserCount(List<ZXCRM.Service.DTOs.DepartmentDTO> departments)
    {
        int count = 0;
        foreach (var dept in departments)
        {
            count += dept.UserCount;
            count += GetTotalUserCount(dept.Children);
        }
        return count;
    }
    
    private int GetMaxDepth(List<ZXCRM.Service.DTOs.DepartmentDTO> departments, int currentDepth = 1)
    {
        if (!departments.Any()) return currentDepth - 1;
        
        int maxDepth = currentDepth;
        foreach (var dept in departments)
        {
            if (dept.Children.Any())
            {
                int childDepth = GetMaxDepth(dept.Children, currentDepth + 1);
                maxDepth = Math.Max(maxDepth, childDepth);
            }
        }
        return maxDepth;
    }
}

@Html.AntiForgeryToken()
