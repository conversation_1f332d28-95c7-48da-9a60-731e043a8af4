using ZXCRM.Service.DTOs;

namespace ZXCRM.WebUI.Models.ViewModels
{
    public class PermissionListViewModel
    {
        public IEnumerable<PermissionDto> Permissions { get; set; } = new List<PermissionDto>();
        public string SearchTerm { get; set; } = string.Empty;
        public int PageIndex { get; set; } = 1;
        public int PageSize { get; set; } = 25;
        public int TotalCount { get; set; }
    }

    public class UserPermissionViewModel
    {
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public List<UserPermissionDto> UserPermissions { get; set; } = new();
        public List<PermissionDto> AllPermissions { get; set; } = new();
    }
}
