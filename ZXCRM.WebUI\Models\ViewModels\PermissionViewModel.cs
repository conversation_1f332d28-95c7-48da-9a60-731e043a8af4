using ZXCRM.Service.DTOs;

namespace ZXCRM.WebUI.Models.ViewModels
{
    public class PermissionListViewModel
    {
        public IEnumerable<PermissionDto> Permissions { get; set; } = new List<PermissionDto>();
        public string SearchTerm { get; set; } = string.Empty;
        public int PageIndex { get; set; } = 1;
        public int PageSize { get; set; } = 25;
        public int TotalCount { get; set; }
    }

    public class UserPermissionViewModel
    {
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public List<UserPermissionDto> UserPermissions { get; set; } = new();
        public List<PermissionDto> AllPermissions { get; set; } = new();
    }

    public class UserPermissionListViewModel
    {
        public IEnumerable<UserWithPermissionsDto> Users { get; set; } = new List<UserWithPermissionsDto>();
        public string SearchTerm { get; set; } = string.Empty;
        public int PageIndex { get; set; } = 1;
        public int PageSize { get; set; } = 25;
        public int TotalCount { get; set; }
    }

    public class UserWithPermissionsDto
    {
        public int Id { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public string Role { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public List<string> BusinessPermissions { get; set; } = new();
        public List<string> SystemPermissions { get; set; } = new();
        public bool HasBusinessAccess { get; set; }
        public bool HasSystemAccess { get; set; }
    }
}
