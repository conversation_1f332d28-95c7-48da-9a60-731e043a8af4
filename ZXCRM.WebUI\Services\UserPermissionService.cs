using ZXCRM.Service.DTOs;

namespace ZXCRM.WebUI.Services
{
    public class UserPermissionService : IUserPermissionService
    {
        private readonly IApiService _apiService;
        private readonly ILogger<UserPermissionService> _logger;

        public UserPermissionService(IApiService apiService, ILogger<UserPermissionService> logger)
        {
            _apiService = apiService;
            _logger = logger;
        }

        public async Task<IEnumerable<UserPermissionDto>> GetByUserIdAsync(int userId)
        {
            try
            {
                _logger.LogInformation("Getting user permissions for user: {UserId}", userId);

                var response = await _apiService.GetAsync<List<UserPermissionDto>>($"api/userpermission/user/{userId}");

                if (response.Success && response.Data != null)
                {
                    _logger.LogInformation("Successfully retrieved {Count} permissions for user: {UserId}", response.Data.Count, userId);
                    return response.Data;
                }
                else
                {
                    _logger.LogWarning("Failed to retrieve user permissions for user {UserId}: {Message}", userId, response.Message);
                    return new List<UserPermissionDto>();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user permissions for user: {UserId}", userId);
                return new List<UserPermissionDto>();
            }
        }

        public async Task<bool> AssignPermissionAsync(int userId, int permissionId, string moduleType)
        {
            try
            {
                _logger.LogInformation("Assigning permission {PermissionId} to user {UserId} for module {ModuleType}", 
                    permissionId, userId, moduleType);

                var userPermission = new UserPermissionDto
                {
                    UserId = userId,
                    PermissionId = permissionId,
                    ModuleType = moduleType
                };

                var response = await _apiService.PostAsync<UserPermissionDto>("api/userpermission", userPermission);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully assigned permission {PermissionId} to user {UserId}", permissionId, userId);
                    return true;
                }
                else
                {
                    _logger.LogWarning("Failed to assign permission {PermissionId} to user {UserId}: {Message}", 
                        permissionId, userId, response.Message);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error assigning permission {PermissionId} to user {UserId}", permissionId, userId);
                return false;
            }
        }

        public async Task<bool> RemovePermissionAsync(int userId, int permissionId, string moduleType)
        {
            try
            {
                _logger.LogInformation("Removing permission {PermissionId} from user {UserId} for module {ModuleType}", 
                    permissionId, userId, moduleType);

                var response = await _apiService.DeleteAsync<object>($"api/userpermission/{userId}/{permissionId}/{moduleType}");

                if (response.Success)
                {
                    _logger.LogInformation("Successfully removed permission {PermissionId} from user {UserId}", permissionId, userId);
                    return true;
                }
                else
                {
                    _logger.LogWarning("Failed to remove permission {PermissionId} from user {UserId}: {Message}", 
                        permissionId, userId, response.Message);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing permission {PermissionId} from user {UserId}", permissionId, userId);
                return false;
            }
        }

        public async Task<bool> UpdateUserPermissionsAsync(int userId, List<UserPermissionDto> permissions)
        {
            try
            {
                _logger.LogInformation("Updating permissions for user: {UserId}", userId);

                var response = await _apiService.PutAsync<object>($"api/userpermission/user/{userId}", permissions);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully updated permissions for user: {UserId}", userId);
                    return true;
                }
                else
                {
                    _logger.LogWarning("Failed to update permissions for user {UserId}: {Message}", userId, response.Message);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating permissions for user: {UserId}", userId);
                return false;
            }
        }
    }
}
