using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ZXCRM.Service.DTOs;
using ZXCRM.WebUI.Models.ViewModels;
using ZXCRM.WebUI.Services;

namespace ZXCRM.WebUI.Controllers
{
    [Authorize]
    public class DepartmentController : BaseController
    {
        private readonly IDepartmentService _departmentService;
        private readonly IUserService _userService;
        private readonly ILogger<DepartmentController> _logger;

        public DepartmentController(
            IDepartmentService departmentService,
            IUserService userService,
            ILogger<DepartmentController> logger)
        {
            _departmentService = departmentService;
            _userService = userService;
            _logger = logger;
        }

        // GET: Department/Tree
        public async Task<IActionResult> Tree()
        {
            try
            {
                _logger.LogInformation("Getting department tree structure");

                var response = await _departmentService.GetDepartmentTreeAsync();

                if (!response.Success || response.Data == null)
                {
                    _logger.LogWarning("Failed to get department tree: {Message}", response.Message);
                    TempData["ErrorMessage"] = response.Message ?? "获取部门组织架构失败";
                    return View(new List<DepartmentDTO>());
                }

                _logger.LogInformation("Successfully retrieved department tree with {Count} root departments", response.Data.Count);
                return View(response.Data.ToList());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting department tree");
                TempData["ErrorMessage"] = "获取部门组织架构时发生错误";
                return View(new List<DepartmentDTO>());
            }
        }

        // GET: Department
        public async Task<IActionResult> Index(string searchTerm = "", int pageIndex = 1, int pageSize = 10)
        {
            try
            {
                _logger.LogInformation("Getting departments list. SearchTerm: {SearchTerm}, PageIndex: {PageIndex}, PageSize: {PageSize}",
                    searchTerm, pageIndex, pageSize);

                var response = await _departmentService.GetDepartmentsAsync();

                if (!response.Success || response.Data == null)
                {
                    _logger.LogWarning("Failed to get departments: {Message}", response.Message);
                    TempData["ErrorMessage"] = response.Message ?? "获取部门列表失败";
                    return View(new DepartmentListViewModel());
                }

                // 应用搜索过滤
                var filteredDepartments = response.Data.AsQueryable();
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    filteredDepartments = filteredDepartments.Where(d =>
                        d.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase));
                }

                // 应用分页
                var totalCount = filteredDepartments.Count();
                var departments = filteredDepartments
                    .Skip((pageIndex - 1) * pageSize)
                    .Take(pageSize)
                    .Select(d => new DepartmentItemViewModel
                    {
                        Id = d.Id,
                        Name = d.Name,
                        UserCount = d.UserCount, // 使用从Service返回的实际用户数量
                        Status = d.Status ?? "Active", // 使用从DTO获取的状态
                        CreatedAt = d.CreatedAt
                    })
                    .ToList();

                var viewModel = new DepartmentListViewModel
                {
                    Departments = departments,
                    TotalCount = totalCount,
                    PageIndex = pageIndex,
                    PageSize = pageSize,
                    SearchTerm = searchTerm
                };

                _logger.LogInformation("Successfully retrieved {Count} departments", departments.Count);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting departments list");
                TempData["ErrorMessage"] = "获取部门列表时发生错误";
                return View(new DepartmentListViewModel());
            }
        }

        // GET: Department/Details/5
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                _logger.LogInformation("Getting department details for ID: {DepartmentId}", id);

                var departmentResponse = await _departmentService.GetDepartmentByIdAsync(id);

                if (!departmentResponse.Success || departmentResponse.Data == null)
                {
                    _logger.LogWarning("Department not found: {DepartmentId}", id);
                    TempData["ErrorMessage"] = "部门不存在";
                    return RedirectToAction(nameof(Index));
                }

                // 获取部门用户列表
                var usersResponse = await _departmentService.GetDepartmentUsersAsync(id);
                var departmentUsers = new List<DepartmentUserViewModel>();

                if (usersResponse.Success && usersResponse.Data != null)
                {
                    departmentUsers = usersResponse.Data.Select(u => new DepartmentUserViewModel
                    {
                        Id = u.Id,
                        Username = u.Username,
                        Name = u.Name,
                        Email = u.Email ?? string.Empty,
                        Role = u.Role,
                        Status = u.Status,
                        CreatedAt = u.CreatedAt
                    }).ToList();
                }

                var viewModel = new DepartmentDetailViewModel
                {
                    Id = departmentResponse.Data.Id,
                    Name = departmentResponse.Data.Name,
                    IsActive = true, // 暂时硬编码
                    CreatedAt = departmentResponse.Data.CreatedAt,
                    UpdatedAt = departmentResponse.Data.UpdatedAt,
                    TotalUsers = departmentUsers.Count,
                    ActiveUsers = departmentUsers.Count(u => u.Status == "Active"),
                    TotalOrders = 0, // 暂时设为0，后续可以从统计API获取
                    TotalRevenue = 0, // 暂时设为0，后续可以从统计API获取
                    Users = departmentUsers
                };

                _logger.LogInformation("Successfully retrieved department details for ID: {DepartmentId}", id);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting department details for ID: {DepartmentId}", id);
                TempData["ErrorMessage"] = "获取部门详情时发生错误";
                return RedirectToAction(nameof(Index));
            }
        }

        // GET: Department/Create
        public async Task<IActionResult> Create(int? parentId = null)
        {
            try
            {
                var viewModel = new CreateDepartmentViewModel();

                // 如果指定了父部门ID，设置默认值
                if (parentId.HasValue)
                {
                    viewModel.ParentId = parentId.Value;
                }

                // 加载可选的父部门列表
                await LoadAvailableParents(viewModel);

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading create department page");
                TempData["ErrorMessage"] = "加载创建部门页面时发生错误";
                return RedirectToAction(nameof(Index));
            }
        }

        // POST: Department/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CreateDepartmentViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    await LoadAvailableParents(model);
                    return View(model);
                }

                _logger.LogInformation("Creating new department: {DepartmentName}, ParentId: {ParentId}", model.Name, model.ParentId);

                var createDepartmentDto = new CreateDepartmentDTO
                {
                    Name = model.Name,
                    Code = model.Code,
                    ParentId = model.ParentId
                };

                var response = await _departmentService.CreateDepartmentAsync(createDepartmentDto);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully created department: {DepartmentName}", model.Name);
                    TempData["SuccessMessage"] = "部门创建成功";
                    return RedirectToAction(nameof(Tree));
                }
                else
                {
                    _logger.LogWarning("Failed to create department: {DepartmentName}. Message: {Message}", model.Name, response.Message);
                    ModelState.AddModelError(string.Empty, response.Message ?? "创建部门失败");
                    await LoadAvailableParents(model);
                    return View(model);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating department: {DepartmentName}", model.Name);
                ModelState.AddModelError(string.Empty, "创建部门时发生错误");
                await LoadAvailableParents(model);
                return View(model);
            }
        }

        // GET: Department/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            try
            {
                _logger.LogInformation("Loading edit department page for ID: {DepartmentId}", id);

                var response = await _departmentService.GetDepartmentByIdAsync(id);

                if (!response.Success || response.Data == null)
                {
                    _logger.LogWarning("Department not found for edit: {DepartmentId}", id);
                    TempData["ErrorMessage"] = "部门不存在";
                    return RedirectToAction(nameof(Index));
                }

                var viewModel = new EditDepartmentViewModel
                {
                    Id = response.Data.Id,
                    Name = response.Data.Name,
                    IsActive = true // 暂时硬编码
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading edit department page for ID: {DepartmentId}", id);
                TempData["ErrorMessage"] = "加载编辑部门页面时发生错误";
                return RedirectToAction(nameof(Index));
            }
        }

        // POST: Department/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, EditDepartmentViewModel model)
        {
            try
            {
                if (id != model.Id)
                {
                    TempData["ErrorMessage"] = "部门ID不匹配";
                    return RedirectToAction(nameof(Index));
                }

                if (!ModelState.IsValid)
                {
                    return View(model);
                }

                _logger.LogInformation("Updating department: {DepartmentId}", id);

                var updateDepartmentDto = new UpdateDepartmentDTO
                {
                    Id = model.Id,
                    Name = model.Name
                };

                var response = await _departmentService.UpdateDepartmentAsync(id, updateDepartmentDto);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully updated department: {DepartmentId}", id);
                    TempData["SuccessMessage"] = "部门更新成功";
                    return RedirectToAction(nameof(Index));
                }
                else
                {
                    _logger.LogWarning("Failed to update department: {DepartmentId}. Message: {Message}", id, response.Message);
                    ModelState.AddModelError(string.Empty, response.Message ?? "更新部门失败");
                    return View(model);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating department: {DepartmentId}", id);
                ModelState.AddModelError(string.Empty, "更新部门时发生错误");
                return View(model);
            }
        }

        // POST: Department/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                _logger.LogInformation("Deleting department: {DepartmentId}", id);

                var response = await _departmentService.DeleteDepartmentAsync(id);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully deleted department: {DepartmentId}", id);
                    TempData["SuccessMessage"] = "部门删除成功";
                }
                else
                {
                    _logger.LogWarning("Failed to delete department: {DepartmentId}. Message: {Message}", id, response.Message);
                    TempData["ErrorMessage"] = response.Message ?? "删除部门失败";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting department: {DepartmentId}", id);
                TempData["ErrorMessage"] = "删除部门时发生错误";
            }

            return RedirectToAction(nameof(Index));
        }

        private async Task LoadAvailableParents(CreateDepartmentViewModel model)
        {
            try
            {
                var response = await _departmentService.GetDepartmentTreeAsync();
                if (response.Success && response.Data != null)
                {
                    model.AvailableParents = BuildDepartmentSelectList(response.Data);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading available parent departments");
                model.AvailableParents = new List<DepartmentSelectItem>();
            }
        }

        private List<DepartmentSelectItem> BuildDepartmentSelectList(List<DepartmentDTO> departments, int level = 0)
        {
            var result = new List<DepartmentSelectItem>();

            foreach (var dept in departments.OrderBy(d => d.Name))
            {
                result.Add(new DepartmentSelectItem
                {
                    Id = dept.Id,
                    Name = dept.Name,
                    Code = dept.Code,
                    ParentId = dept.ParentId,
                    Level = level
                });

                // 递归添加子部门
                if (dept.Children.Any())
                {
                    result.AddRange(BuildDepartmentSelectList(dept.Children, level + 1));
                }
            }

            return result;
        }
    }
}
