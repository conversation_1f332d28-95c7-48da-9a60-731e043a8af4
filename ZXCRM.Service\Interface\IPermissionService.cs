using System.Collections.Generic;
using System.Threading.Tasks;
using ZXCRM.Service.DTOs;

namespace ZXCRM.Service.Interface
{
    public interface IPermissionService
    {
        Task<IEnumerable<PermissionDto>> GetAllAsync();
        Task<PermissionDto> GetByIdAsync(int id);
        Task<PermissionDto> CreateAsync(PermissionDto permission);
        Task<PermissionDto> UpdateAsync(PermissionDto permission);
        Task DeleteAsync(int id);
    }
} 