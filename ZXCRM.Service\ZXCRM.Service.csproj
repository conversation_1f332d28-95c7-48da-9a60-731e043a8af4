<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ZXCRM.Data\ZXCRM.Data.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="DTOs\" />
    <Folder Include="Interface\" />
    <Folder Include="Service\" />
    <Folder Include="Mappings\" />
    <Folder Include="Extensions\" />
  </ItemGroup>

</Project>
