<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - ZXCRM企业运营数据管理系统</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Toastr CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />

    @await RenderSectionAsync("Styles", required: false)
</head>
<body class="app-body">
    <div class="app-container">
        <!-- 左侧导航栏 -->
        <aside class="sidebar-navigation">
            <!-- Logo区域 -->
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-chart-line"></i>
                    <span class="logo-text">ZXCRM</span>
                </div>
                <!-- 移动端菜单按钮 -->
                <button class="mobile-menu-close d-md-none" onclick="toggleMobileMenu()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- 用户信息区域 -->
            <div class="sidebar-user">
                <div class="user-avatar">
                    <img src="~/img/user-avatar.png" alt="User Avatar">
                </div>
                <div class="user-info">
                    <div class="user-name">@User.FindFirst("UserName")?.Value</div>
                    <div class="user-actions">
                        <a asp-controller="Report" asp-action="Index" class="user-action-btn" title="首页">
                            <i class="fas fa-home"></i>
                        </a>
                        <a asp-controller="User" asp-action="Profile" class="user-action-btn" title="个人设置">
                            <i class="fas fa-cog"></i>
                        </a>
                        <a href="#" onclick="showLogoutModal()" class="user-action-btn" title="退出登录">
                            <i class="fas fa-sign-out-alt"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Tab区域 -->
            <div class="sidebar-tabs">
                <div class="tab-item active" data-tab="business">
                    <i class="fas fa-chart-line"></i>
                    <span class="tab-text">经营数据</span>
                </div>
                <!-- 预留其他Tab，暂时隐藏 -->
                <div class="tab-item hidden" data-tab="system">
                    <i class="fas fa-cogs"></i>
                    <span class="tab-text">系统管理</span>
                </div>
            </div>

            <!-- 导航菜单区域 -->
            <nav class="sidebar-menu">
                <div class="menu-group active" data-tab-content="business">
                    <ul class="menu-list">
                        <li class="menu-item">
                            <a asp-controller="Opportunity" asp-action="Index" class="menu-link">
                                <i class="fas fa-seedling"></i>
                                <span class="menu-text">商机管理</span>
                            </a>
                        </li>
                        <li class="menu-item">
                            <a asp-controller="Order" asp-action="Index" class="menu-link">
                                <i class="fas fa-shopping-cart"></i>
                                <span class="menu-text">订单管理</span>
                            </a>
                        </li>
                        <li class="menu-item">
                            <a asp-controller="Payment" asp-action="Index" class="menu-link">
                                <i class="fas fa-money-bill-wave"></i>
                                <span class="menu-text">款项管理</span>
                            </a>
                        </li>
                        <li class="menu-item">
                            <a asp-controller="Invoice" asp-action="Index" class="menu-link">
                                <i class="fas fa-file-invoice"></i>
                                <span class="menu-text">发票管理</span>
                            </a>
                        </li>
                        <li class="menu-item">
                            <a asp-controller="Report" asp-action="Index" class="menu-link">
                                <i class="fas fa-chart-bar"></i>
                                <span class="menu-text">报表统计</span>
                            </a>
                        </li>
                        <li class="menu-item">
                            <a asp-controller="User" asp-action="Index" class="menu-link">
                                <i class="fas fa-users"></i>
                                <span class="menu-text">用户管理</span>
                            </a>
                        </li>
                        <li class="menu-item">
                            <a asp-controller="Department" asp-action="Index" class="menu-link">
                                <i class="fas fa-building"></i>
                                <span class="menu-text">部门管理</span>
                            </a>
                        </li>
                        <li class="menu-item">
                            <a asp-controller="Permission" asp-action="Index" class="menu-link">
                                <i class="fas fa-shield-alt"></i>
                                <span class="menu-text">权限管理</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 预留系统管理菜单组 -->
                <div class="menu-group hidden" data-tab-content="system">
                    <ul class="menu-list">
                        <li class="menu-item">
                            <a asp-controller="System" asp-action="Health" class="menu-link">
                                <i class="fas fa-heartbeat"></i>
                                <span class="menu-text">系统监控</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </aside>

        <!-- 右侧内容区域 -->
        <main class="main-content">
            <!-- 移动端菜单按钮区域 -->
            <div class="mobile-header d-md-none">
                <button class="mobile-menu-toggle" onclick="toggleMobileMenu()">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <!-- 数据展示区域 -->
            <div class="content-body">
                <!-- Alert Messages -->
                @await Html.PartialAsync("_AlertMessages")

                <!-- Page Content -->
                @RenderBody()
            </div>
        </main>
    </div>

    <!-- 登出确认模态框 -->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="logoutModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="logoutModalLabel">
                        <i class="fas fa-sign-out-alt"></i> 确认退出
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>您确定要退出登录吗？</p>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="clearRememberedInfo">
                        <label class="form-check-label" for="clearRememberedInfo">
                            同时清除记住的登录信息
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="performLogout()">确认退出</button>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <!-- Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Toastr JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <!-- Custom JS -->
    <script src="~/js/site.js" asp-append-version="true"></script>

    <script>
        // 登出相关函数
        function showLogoutModal() {
            $('#logoutModal').modal('show');
        }

        function performLogout() {
            var clearRemembered = $('#clearRememberedInfo').is(':checked');

            // 创建表单并提交
            var form = document.createElement('form');
            form.method = 'POST';
            form.action = '@Url.Action("Logout", "Account")';

            // 添加防伪令牌
            var token = document.createElement('input');
            token.type = 'hidden';
            token.name = '__RequestVerificationToken';
            token.value = $('input[name="__RequestVerificationToken"]').val();
            form.appendChild(token);

            // 添加清除记住信息的参数
            if (clearRemembered) {
                var clearInput = document.createElement('input');
                clearInput.type = 'hidden';
                clearInput.name = 'clearRememberedInfo';
                clearInput.value = 'true';
                form.appendChild(clearInput);
            }

            document.body.appendChild(form);
            form.submit();
        }

        // 移动端菜单切换
        function toggleMobileMenu() {
            const sidebar = document.querySelector('.sidebar-navigation');
            const body = document.body;

            if (sidebar.classList.contains('show')) {
                sidebar.classList.remove('show');
                body.classList.remove('sidebar-open');
            } else {
                sidebar.classList.add('show');
                body.classList.add('sidebar-open');
            }
        }

        // Tab切换功能
        function switchTab(tabName) {
            // 切换Tab激活状态
            document.querySelectorAll('.tab-item').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

            // 切换菜单组显示
            document.querySelectorAll('.menu-group').forEach(group => {
                group.classList.remove('active');
                group.classList.add('hidden');
            });
            document.querySelector(`[data-tab-content="${tabName}"]`).classList.remove('hidden');
            document.querySelector(`[data-tab-content="${tabName}"]`).classList.add('active');
        }

        // 页面加载完成后的初始化
        $(document).ready(function() {
            // 高亮当前页面菜单项
            highlightCurrentMenu();

            // Tab点击事件
            $('.tab-item').click(function() {
                const tabName = $(this).data('tab');
                if (!$(this).hasClass('hidden')) {
                    switchTab(tabName);
                }
            });

            // 移动端点击遮罩关闭菜单
            $(document).click(function(e) {
                if ($(window).width() < 768) {
                    const sidebar = $('.sidebar-navigation');
                    const target = $(e.target);

                    if (!target.closest('.sidebar-navigation').length &&
                        !target.closest('.mobile-menu-toggle').length &&
                        sidebar.hasClass('show')) {
                        toggleMobileMenu();
                    }
                }
            });
        });

        // 高亮当前页面菜单项
        function highlightCurrentMenu() {
            const currentPath = window.location.pathname.toLowerCase();
            const menuLinks = document.querySelectorAll('.menu-link');

            // 先清除所有菜单项的选中状态
            menuLinks.forEach(link => {
                link.closest('.menu-item').classList.remove('active');
            });

            // 然后高亮当前页面对应的菜单项
            menuLinks.forEach(link => {
                const linkPath = link.getAttribute('href');
                if (linkPath && currentPath.includes(linkPath.toLowerCase())) {
                    link.closest('.menu-item').classList.add('active');
                }
            });
        }
    </script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
