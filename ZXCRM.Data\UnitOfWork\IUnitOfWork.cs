using ZXCRM.Data.Repositories;
using ZXCRM.Data.Entities;

namespace ZXCRM.Data.UnitOfWork
{
    public interface IUnitOfWork : IDisposable
    {
        IRepository<Department> Departments { get; }
        IRepository<User> Users { get; }
        IRepository<Permission> Permissions { get; }
        IRepository<UserPermission> UserPermissions { get; }
        IRepository<Opportunity> Opportunities { get; }
        IRepository<Order> Orders { get; }
        IRepository<Payment> Payments { get; }
        IRepository<Invoice> Invoices { get; }
        
        Task<int> SaveChangesAsync();
        Task BeginTransactionAsync();
        Task CommitTransactionAsync();
        Task RollbackTransactionAsync();
    }
}
