using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ZXCRM.WebUI.Services;
using ZXCRM.WebUI.Models.ViewModels;
using ZXCRM.Service.DTOs;

namespace ZXCRM.WebUI.Controllers
{
    [Authorize]
    public class ReportController : Controller
    {
        private readonly IReportService _reportService;
        private readonly IUserService _userService;
        private readonly IDepartmentService _departmentService;
        private readonly ILogger<ReportController> _logger;

        public ReportController(
            IReportService reportService,
            IUserService userService,
            IDepartmentService departmentService,
            ILogger<ReportController> logger)
        {
            _reportService = reportService;
            _userService = userService;
            _departmentService = departmentService;
            _logger = logger;
        }

        // GET: Report
        public async Task<IActionResult> Index(ReportQueryViewModel? query = null)
        {
            try
            {
                _logger.LogInformation("Loading report index page");

                // 设置默认查询条件（最近3个月）
                if (query == null)
                {
                    query = new ReportQueryViewModel
                    {
                        StartDate = DateTime.Now.AddMonths(-3),
                        EndDate = DateTime.Now
                    };
                }

                var viewModel = new ReportIndexViewModel
                {
                    Query = query
                };

                // 加载下拉列表数据
                await LoadDropdownData(viewModel);

                // 获取仪表盘统计数据
                var statsResponse = await _reportService.GetDashboardStatsAsync(query?.ToDTO());
                if (statsResponse.Success && statsResponse.Data != null)
                {
                    viewModel.DashboardStats = statsResponse.Data;
                }
                else
                {
                    _logger.LogWarning("Failed to load dashboard stats: {Message}", statsResponse.Message);
                }

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading report index page");
                TempData["ErrorMessage"] = "加载报表页面时发生错误";
                return View(new ReportIndexViewModel());
            }
        }

        // GET: Report/Dashboard
        public async Task<IActionResult> Dashboard(ReportQueryViewModel? query = null)
        {
            try
            {
                _logger.LogInformation("Loading dashboard page");

                if (query == null)
                {
                    query = new ReportQueryViewModel
                    {
                        StartDate = DateTime.Now.AddMonths(-3),
                        EndDate = DateTime.Now
                    };
                }

                var viewModel = new ReportDashboardViewModel
                {
                    Query = query
                };

                // 获取仪表盘统计
                var statsResponse = await _reportService.GetDashboardStatsAsync(query.ToDTO());
                if (statsResponse.Success && statsResponse.Data != null)
                {
                    viewModel.Stats = statsResponse.Data;
                }

                // 获取报表摘要
                var summaryResponse = await _reportService.GetReportSummaryAsync(query.ToDTO());
                if (summaryResponse.Success && summaryResponse.Data != null)
                {
                    viewModel.Summary = summaryResponse.Data;
                }

                // 获取趋势数据
                var trendsResponse = await _reportService.GetTrendsAsync(query.ToDTO());
                if (trendsResponse.Success && trendsResponse.Data != null)
                {
                    viewModel.Trends = trendsResponse.Data;
                }

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading dashboard page");
                TempData["ErrorMessage"] = "加载仪表盘时发生错误";
                return RedirectToAction(nameof(Index));
            }
        }

        // GET: Report/Opportunity
        public async Task<IActionResult> Opportunity(ReportQueryViewModel? query = null)
        {
            try
            {
                _logger.LogInformation("Loading opportunity report page");

                if (query == null)
                {
                    query = new ReportQueryViewModel
                    {
                        StartDate = DateTime.Now.AddMonths(-6),
                        EndDate = DateTime.Now
                    };
                }

                var viewModel = new OpportunityReportViewModel
                {
                    Query = query
                };

                await LoadDropdownData(viewModel);

                var response = await _reportService.GetOpportunityReportAsync(query.ToDTO());
                if (response.Success && response.Data != null)
                {
                    viewModel.Report = response.Data;
                }
                else
                {
                    _logger.LogWarning("Failed to load opportunity report: {Message}", response.Message);
                    TempData["ErrorMessage"] = response.Message ?? "获取商机报表失败";
                }

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading opportunity report page");
                TempData["ErrorMessage"] = "加载商机报表时发生错误";
                return RedirectToAction(nameof(Index));
            }
        }

        // GET: Report/Order
        public async Task<IActionResult> Order(ReportQueryViewModel? query = null)
        {
            try
            {
                _logger.LogInformation("Loading order report page");

                if (query == null)
                {
                    query = new ReportQueryViewModel
                    {
                        StartDate = DateTime.Now.AddMonths(-6),
                        EndDate = DateTime.Now
                    };
                }

                var viewModel = new OrderReportViewModel
                {
                    Query = query
                };

                await LoadDropdownData(viewModel);

                var response = await _reportService.GetOrderReportAsync(query.ToDTO());
                if (response.Success && response.Data != null)
                {
                    viewModel.Report = response.Data;
                }
                else
                {
                    _logger.LogWarning("Failed to load order report: {Message}", response.Message);
                    TempData["ErrorMessage"] = response.Message ?? "获取订单报表失败";
                }

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading order report page");
                TempData["ErrorMessage"] = "加载订单报表时发生错误";
                return RedirectToAction(nameof(Index));
            }
        }

        // GET: Report/Payment
        public async Task<IActionResult> Payment(ReportQueryViewModel? query = null)
        {
            try
            {
                _logger.LogInformation("Loading payment report page");

                if (query == null)
                {
                    query = new ReportQueryViewModel
                    {
                        StartDate = DateTime.Now.AddMonths(-6),
                        EndDate = DateTime.Now
                    };
                }

                var viewModel = new PaymentReportViewModel
                {
                    Query = query
                };

                var response = await _reportService.GetPaymentReportAsync(query.ToDTO());
                if (response.Success && response.Data != null)
                {
                    viewModel.Report = response.Data;
                }
                else
                {
                    _logger.LogWarning("Failed to load payment report: {Message}", response.Message);
                    TempData["ErrorMessage"] = response.Message ?? "获取款项报表失败";
                }

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading payment report page");
                TempData["ErrorMessage"] = "加载款项报表时发生错误";
                return RedirectToAction(nameof(Index));
            }
        }

        // GET: Report/Invoice
        public async Task<IActionResult> Invoice(ReportQueryViewModel? query = null)
        {
            try
            {
                _logger.LogInformation("Loading invoice report page");

                if (query == null)
                {
                    query = new ReportQueryViewModel
                    {
                        StartDate = DateTime.Now.AddMonths(-6),
                        EndDate = DateTime.Now
                    };
                }

                var viewModel = new InvoiceReportViewModel
                {
                    Query = query
                };

                var response = await _reportService.GetInvoiceReportAsync(query.ToDTO());
                if (response.Success && response.Data != null)
                {
                    viewModel.Report = response.Data;
                }
                else
                {
                    _logger.LogWarning("Failed to load invoice report: {Message}", response.Message);
                    TempData["ErrorMessage"] = response.Message ?? "获取发票报表失败";
                }

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading invoice report page");
                TempData["ErrorMessage"] = "加载发票报表时发生错误";
                return RedirectToAction(nameof(Index));
            }
        }

        private async Task LoadDropdownData<T>(T viewModel) where T : class
        {
            try
            {
                // 加载用户列表
                var users = new List<UserSelectItem>();
                var usersResponse = await _userService.GetUsersAsync();
                if (usersResponse.Success && usersResponse.Data != null)
                {
                    users = usersResponse.Data.Select(u => new UserSelectItem
                    {
                        Id = u.Id,
                        Name = u.Name,
                        Username = u.Username
                    }).ToList();
                }

                // 加载部门列表
                var departments = new List<DepartmentSelectItem>();
                var departmentsResponse = await _departmentService.GetDepartmentsAsync();
                if (departmentsResponse.Success && departmentsResponse.Data != null)
                {
                    departments = departmentsResponse.Data.Select(d => new DepartmentSelectItem
                    {
                        Id = d.Id,
                        Name = d.Name
                    }).ToList();
                }

                // 使用反射设置属性
                var usersProperty = typeof(T).GetProperty("Users");
                var departmentsProperty = typeof(T).GetProperty("Departments");

                usersProperty?.SetValue(viewModel, users);
                departmentsProperty?.SetValue(viewModel, departments);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error loading dropdown data");
            }
        }
    }
}
