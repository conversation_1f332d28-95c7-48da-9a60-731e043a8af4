@model ZXCRM.WebUI.Models.ViewModels.CreateOpportunityViewModel
@{
    ViewData["Title"] = "新增商机";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">新增商机</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a asp-controller="Dashboard" asp-action="Index">首页</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Opportunity" asp-action="Index">商机管理</a></li>
                    <li class="breadcrumb-item active">新增商机</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-plus"></i> 商机信息
                        </h3>
                    </div>
                    <form asp-action="Create" method="post">
                        <div class="card-body">
                            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <!-- 商机名称 -->
                                    <div class="form-group">
                                        <label asp-for="Name" class="form-label required"></label>
                                        <input asp-for="Name" class="form-control" placeholder="请输入商机名称" />
                                        <span asp-validation-for="Name" class="text-danger"></span>
                                    </div>

                                    <!-- 客户名称 -->
                                    <div class="form-group">
                                        <label asp-for="CustomerName" class="form-label required"></label>
                                        <input asp-for="CustomerName" class="form-control" placeholder="请输入客户名称" />
                                        <span asp-validation-for="CustomerName" class="text-danger"></span>
                                    </div>

                                    <!-- 联系人 -->
                                    <div class="form-group">
                                        <label asp-for="ContactName" class="form-label"></label>
                                        <input asp-for="ContactName" class="form-control" placeholder="请输入联系人姓名" />
                                        <span asp-validation-for="ContactName" class="text-danger"></span>
                                    </div>

                                    <!-- 联系电话 -->
                                    <div class="form-group">
                                        <label asp-for="ContactPhone" class="form-label"></label>
                                        <input asp-for="ContactPhone" class="form-control" placeholder="请输入联系电话" />
                                        <span asp-validation-for="ContactPhone" class="text-danger"></span>
                                    </div>

                                    <!-- 商机状态 -->
                                    <div class="form-group">
                                        <label asp-for="Status" class="form-label required"></label>
                                        <select asp-for="Status" class="form-control">
                                            @foreach (var status in ZXCRM.WebUI.Models.ViewModels.OpportunityStatus.GetAllStatuses())
                                            {
                                                <option value="@status">@status</option>
                                            }
                                        </select>
                                        <span asp-validation-for="Status" class="text-danger"></span>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <!-- 商机内容 -->
                                    <div class="form-group">
                                        <label asp-for="Content" class="form-label"></label>
                                        <textarea asp-for="Content" class="form-control" rows="10" placeholder="请详细描述商机内容、需求、预期等信息..."></textarea>
                                        <span asp-validation-for="Content" class="text-danger"></span>
                                        <small class="form-text text-muted">详细的商机描述有助于后续跟进</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card-footer">
                            <div class="row">
                                <div class="col-md-6">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> 保存
                                    </button>
                                    <a asp-action="Index" class="btn btn-secondary ml-2">
                                        <i class="fas fa-times"></i> 取消
                                    </a>
                                </div>
                                <div class="col-md-6 text-right">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i> 
                                        标有 <span class="text-danger">*</span> 的字段为必填项
                                    </small>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="col-md-4">
                <!-- 帮助信息 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-question-circle"></i> 填写说明
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb"></i> 提示</h6>
                            <ul class="mb-0">
                                <li>商机名称建议包含项目或产品信息</li>
                                <li>客户名称请填写完整的公司名称</li>
                                <li>联系人信息有助于后续沟通</li>
                                <li>商机内容越详细越有助于跟进</li>
                                <li>根据实际情况选择合适的商机状态</li>
                            </ul>
                        </div>

                        <div class="alert alert-success">
                            <h6><i class="fas fa-trophy"></i> 成功秘诀</h6>
                            <p class="mb-0">
                                详细记录商机信息，及时跟进客户需求，
                                合理评估商机价值，提高成交转化率。
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 商机状态说明 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-line"></i> 状态说明
                        </h3>
                    </div>
                    <div class="card-body">
                        <dl>
                            <dt>
                                <span class="badge badge-secondary">
                                    <i class="fas fa-seedling"></i> 潜在
                                </span>
                            </dt>
                            <dd>初步接触，了解客户需求</dd>
                            
                            <dt>
                                <span class="badge badge-info">
                                    <i class="fas fa-check-circle"></i> 合格
                                </span>
                            </dt>
                            <dd>客户需求明确，有购买意向</dd>
                            
                            <dt>
                                <span class="badge badge-warning">
                                    <i class="fas fa-file-alt"></i> 方案
                                </span>
                            </dt>
                            <dd>已提供解决方案或报价</dd>
                            
                            <dt>
                                <span class="badge badge-primary">
                                    <i class="fas fa-handshake"></i> 谈判
                                </span>
                            </dt>
                            <dd>正在商务谈判阶段</dd>
                            
                            <dt>
                                <span class="badge badge-success">
                                    <i class="fas fa-trophy"></i> 成交
                                </span>
                            </dt>
                            <dd>商机成功转化为订单</dd>
                        </dl>
                    </div>
                </div>

                <!-- 快捷操作 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-bolt"></i> 快捷操作
                        </h3>
                    </div>
                    <div class="card-body">
                        <button type="button" class="btn btn-outline-primary btn-sm btn-block" onclick="fillSampleData()">
                            <i class="fas fa-magic"></i> 填充示例数据
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm btn-block" onclick="clearForm()">
                            <i class="fas fa-eraser"></i> 清空表单
                        </button>
                        <button type="button" class="btn btn-outline-info btn-sm btn-block" onclick="generateOpportunityName()">
                            <i class="fas fa-robot"></i> 智能生成商机名称
                        </button>
                    </div>
                </div>

                <!-- 商机流程 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-route"></i> 商机流程
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <div class="time-label">
                                <span class="bg-info">标准流程</span>
                            </div>
                            <div>
                                <i class="fas fa-seedling bg-secondary"></i>
                                <div class="timeline-item">
                                    <h3 class="timeline-header">1. 潜在商机</h3>
                                    <div class="timeline-body">
                                        发现并记录商机
                                    </div>
                                </div>
                            </div>
                            <div>
                                <i class="fas fa-check-circle bg-info"></i>
                                <div class="timeline-item">
                                    <h3 class="timeline-header">2. 需求确认</h3>
                                    <div class="timeline-body">
                                        深入了解客户需求
                                    </div>
                                </div>
                            </div>
                            <div>
                                <i class="fas fa-file-alt bg-warning"></i>
                                <div class="timeline-item">
                                    <h3 class="timeline-header">3. 方案制定</h3>
                                    <div class="timeline-body">
                                        提供解决方案
                                    </div>
                                </div>
                            </div>
                            <div>
                                <i class="fas fa-handshake bg-primary"></i>
                                <div class="timeline-item">
                                    <h3 class="timeline-header">4. 商务谈判</h3>
                                    <div class="timeline-body">
                                        价格和条款谈判
                                    </div>
                                </div>
                            </div>
                            <div>
                                <i class="fas fa-trophy bg-success"></i>
                                <div class="timeline-item">
                                    <h3 class="timeline-header">5. 成功成交</h3>
                                    <div class="timeline-body">
                                        转化为正式订单
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        function fillSampleData() {
            $('#Name').val('XX公司ERP系统升级项目');
            $('#CustomerName').val('XX科技有限公司');
            $('#ContactName').val('张经理');
            $('#ContactPhone').val('138-0000-0000');
            $('#Content').val('客户现有ERP系统老旧，需要升级到新版本，包括财务模块、库存管理、销售管理等功能。预算约50万元，希望在3个月内完成实施。');
            $('#Status').val('合格');
            toastr.info('已填充示例数据');
        }

        function clearForm() {
            if (confirm('确定要清空表单吗？')) {
                $('#Name').val('');
                $('#CustomerName').val('');
                $('#ContactName').val('');
                $('#ContactPhone').val('');
                $('#Content').val('');
                $('#Status').val('潜在');
                toastr.info('表单已清空');
            }
        }

        function generateOpportunityName() {
            const customerName = $('#CustomerName').val();
            if (customerName) {
                const projectTypes = ['系统开发', '软件定制', '技术咨询', '系统集成', '数据分析', '平台建设'];
                const randomType = projectTypes[Math.floor(Math.random() * projectTypes.length)];
                const generatedName = customerName + randomType + '项目';
                $('#Name').val(generatedName);
                toastr.success('已生成商机名称');
            } else {
                toastr.warning('请先填写客户名称');
            }
        }

        // 表单验证增强
        $(document).ready(function() {
            // 客户名称变更时自动生成商机名称建议
            $('#CustomerName').on('blur', function() {
                const customerName = $(this).val();
                const currentName = $('#Name').val();
                if (customerName && !currentName) {
                    generateOpportunityName();
                }
            });

            // 联系电话格式验证
            $('#ContactPhone').on('input', function() {
                const phone = $(this).val();
                const phoneRegex = /^1[3-9]\d{9}$|^\d{3,4}-\d{7,8}$/;
                if (phone && !phoneRegex.test(phone.replace(/\s|-/g, ''))) {
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });

            // 字符计数
            $('#Content').on('input', function() {
                const length = $(this).val().length;
                const maxLength = 1000;
                const remaining = maxLength - length;
                
                let countText = `${length}/${maxLength}`;
                if (remaining < 100) {
                    countText = `<span class="text-warning">${countText}</span>`;
                }
                if (remaining < 0) {
                    countText = `<span class="text-danger">${countText}</span>`;
                }
                
                // 更新字符计数显示（如果有的话）
                $('.char-count').html(countText);
            });
        });
    </script>
}

@section Styles {
    <style>
        .required::after {
            content: " *";
            color: red;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .is-invalid {
            border-color: #dc3545;
        }

        .timeline {
            position: relative;
            margin: 0 0 30px 0;
            padding: 0;
            list-style: none;
        }

        .timeline:before {
            content: '';
            position: absolute;
            top: 0;
            left: 25px;
            height: 100%;
            width: 2px;
            background: #dee2e6;
        }

        .timeline > div {
            position: relative;
            margin-right: 10px;
            margin-bottom: 15px;
        }

        .timeline > div > .timeline-item {
            box-shadow: 0 1px 1px rgba(0,0,0,0.1);
            border-radius: 3px;
            margin-top: 0;
            background: #fff;
            color: #444;
            margin-left: 60px;
            margin-right: 15px;
            padding: 5px;
            position: relative;
        }

        .timeline > div > .fas {
            width: 30px;
            height: 30px;
            font-size: 15px;
            line-height: 30px;
            position: absolute;
            color: #666;
            background: #f4f4f4;
            border-radius: 50%;
            text-align: center;
            left: 18px;
            top: 0;
        }
    </style>
}
