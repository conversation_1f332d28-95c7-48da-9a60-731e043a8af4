using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ZXCRM.Service.DTOs;
using ZXCRM.Service.Interface;

namespace ZXCRM.WebAPI.Controllers
{
    [Authorize]
    public class InvoiceController : BaseController
    {
        private readonly IInvoiceService _invoiceService;
        private readonly ILogger<InvoiceController> _logger;

        public InvoiceController(IInvoiceService invoiceService, ILogger<InvoiceController> logger)
        {
            _invoiceService = invoiceService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> GetInvoices()
        {
            try
            {
                _logger.LogInformation("Getting all invoices");
                var invoices = await _invoiceService.GetAllInvoicesAsync();
                return Success(invoices, "获取发票列表成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoices");
                return Failure("获取发票列表失败");
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetInvoice(int id)
        {
            try
            {
                _logger.LogInformation("Getting invoice by ID: {InvoiceId}", id);
                var invoice = await _invoiceService.GetInvoiceByIdAsync(id);

                if (invoice == null)
                {
                    _logger.LogWarning("Invoice not found: {InvoiceId}", id);
                    return Failure($"发票 {id} 不存在");
                }

                return Success(invoice, "获取发票详情成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoice by ID: {InvoiceId}", id);
                return Failure("获取发票详情失败");
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreateInvoice([FromBody] CreateInvoiceDTO createInvoiceDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Failure("数据验证失败", ModelState);
                }

                _logger.LogInformation("Creating new invoice for payment: {PaymentId}", createInvoiceDto.PaymentId);
                var invoice = await _invoiceService.CreateInvoiceAsync(createInvoiceDto);

                return Success(invoice, "创建发票成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating invoice for payment: {PaymentId}", createInvoiceDto.PaymentId);
                return Failure(ex.Message.Contains("不存在") || ex.Message.Contains("不能超过") ? ex.Message : "创建发票失败");
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateInvoice(int id, [FromBody] UpdateInvoiceDTO updateInvoiceDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Failure("数据验证失败", ModelState);
                }

                // 设置ID到DTO中
                updateInvoiceDto.Id = id;

                _logger.LogInformation("Updating invoice: {InvoiceId}", id);
                var invoice = await _invoiceService.UpdateInvoiceAsync(updateInvoiceDto);

                if (invoice == null)
                {
                    _logger.LogWarning("Invoice not found for update: {InvoiceId}", id);
                    return Failure($"发票 {id} 不存在");
                }

                return Success(invoice, "更新发票成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating invoice: {InvoiceId}", id);
                return Failure(ex.Message.Contains("不能超过") ? ex.Message : "更新发票失败");
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteInvoice(int id)
        {
            try
            {
                _logger.LogInformation("Deleting invoice: {InvoiceId}", id);
                var success = await _invoiceService.DeleteInvoiceAsync(id);

                if (!success)
                {
                    _logger.LogWarning("Invoice not found for deletion: {InvoiceId}", id);
                    return Failure($"发票 {id} 不存在");
                }

                return Success(null, "删除发票成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting invoice: {InvoiceId}", id);
                return Failure("删除发票失败");
            }
        }

        [HttpGet("payment/{paymentId}")]
        public async Task<IActionResult> GetInvoicesByPayment(int paymentId)
        {
            try
            {
                _logger.LogInformation("Getting invoices by payment: {PaymentId}", paymentId);
                var invoices = await _invoiceService.GetInvoicesByPaymentIdAsync(paymentId);
                return Success(invoices, "获取款项发票成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoices by payment: {PaymentId}", paymentId);
                return Failure("获取款项发票失败");
            }
        }

        [HttpGet("payment/{paymentId}/amount")]
        public async Task<IActionResult> GetValidInvoiceAmountByPayment(int paymentId)
        {
            try
            {
                _logger.LogInformation("Getting valid invoice amount by payment: {PaymentId}", paymentId);
                var amount = await _invoiceService.GetValidInvoiceAmountByPaymentIdAsync(paymentId);
                return Success(new { Amount = amount }, "获取有效发票金额成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting valid invoice amount by payment: {PaymentId}", paymentId);
                return Failure("获取有效发票金额失败");
            }
        }
    }
}
