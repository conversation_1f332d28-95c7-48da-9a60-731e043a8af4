@{
    var controller = ViewContext.RouteData.Values["controller"]?.ToString();
    var action = ViewContext.RouteData.Values["action"]?.ToString();
}

<li class="breadcrumb-item"><a asp-controller="Dashboard" asp-action="Index">首页</a></li>

@if (controller != "Dashboard")
{
    @switch (controller)
    {
        case "User":
            <li class="breadcrumb-item">
                @if (action == "Index")
                {
                    <span>用户管理</span>
                }
                else
                {
                    <a asp-controller="User" asp-action="Index">用户管理</a>
                }
            </li>
            @if (action != "Index")
            {
                <li class="breadcrumb-item active">
                    @switch (action)
                    {
                        case "Create":
                            <span>新增用户</span>
                            break;
                        case "Edit":
                            <span>编辑用户</span>
                            break;
                        case "Details":
                            <span>用户详情</span>
                            break;
                        default:
                            <span>@action</span>
                            break;
                    }
                </li>
            }
            break;
        case "Order":
            <li class="breadcrumb-item">
                @if (action == "Index")
                {
                    <span>订单管理</span>
                }
                else
                {
                    <a asp-controller="Order" asp-action="Index">订单管理</a>
                }
            </li>
            @if (action != "Index")
            {
                <li class="breadcrumb-item active">
                    @switch (action)
                    {
                        case "Create":
                            <span>新增订单</span>
                            break;
                        case "Edit":
                            <span>编辑订单</span>
                            break;
                        case "Details":
                            <span>订单详情</span>
                            break;
                        default:
                            <span>@action</span>
                            break;
                    }
                </li>
            }
            break;
        default:
            <li class="breadcrumb-item active">@ViewData["Title"]</li>
            break;
    }
}
