@model ZXCRM.WebUI.Models.ViewModels.CreateOrderViewModel
@{
    ViewData["Title"] = "新增订单";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">新增订单</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a asp-controller="Dashboard" asp-action="Index">首页</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Order" asp-action="Index">订单管理</a></li>
                    <li class="breadcrumb-item active">新增订单</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-plus"></i> 订单信息
                        </h3>
                    </div>
                    <form asp-action="Create" method="post">
                        <div class="card-body">
                            <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                            <div class="row">
                                <div class="col-md-6">
                                    <!-- 关联商机 -->
                                    <div class="form-group">
                                        <label asp-for="OpportunityId" class="form-label"></label>
                                        <select asp-for="OpportunityId" class="form-control" id="OpportunitySelect">
                                            <option value="">请选择关联商机（可选）</option>
                                            @foreach (var opportunity in Model.Opportunities)
                                            {
                                                <option value="@opportunity.Id"
                                                        data-customer="@opportunity.CustomerName"
                                                        data-name="@opportunity.Name">
                                                    @opportunity.Name - @opportunity.CustomerName
                                                </option>
                                            }
                                        </select>
                                        <span asp-validation-for="OpportunityId" class="text-danger"></span>
                                        <small class="form-text text-muted">选择商机后将自动填充客户信息</small>
                                    </div>

                                    <!-- 订单名称 -->
                                    <div class="form-group">
                                        <label asp-for="Name" class="form-label required"></label>
                                        <input asp-for="Name" class="form-control" placeholder="请输入订单名称" />
                                        <span asp-validation-for="Name" class="text-danger"></span>
                                    </div>

                                    <!-- 客户名称 -->
                                    <div class="form-group">
                                        <label asp-for="CustomerName" class="form-label required"></label>
                                        <input asp-for="CustomerName" class="form-control" placeholder="请输入客户名称" />
                                        <span asp-validation-for="CustomerName" class="text-danger"></span>
                                    </div>

                                    <!-- 联系人 -->
                                    <div class="form-group">
                                        <label asp-for="ContactName" class="form-label"></label>
                                        <input asp-for="ContactName" class="form-control" placeholder="请输入联系人姓名" />
                                        <span asp-validation-for="ContactName" class="text-danger"></span>
                                    </div>

                                    <!-- 联系电话 -->
                                    <div class="form-group">
                                        <label asp-for="ContactPhone" class="form-label"></label>
                                        <input asp-for="ContactPhone" class="form-control" placeholder="请输入联系电话" />
                                        <span asp-validation-for="ContactPhone" class="text-danger"></span>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <!-- 订单金额 -->
                                    <div class="form-group">
                                        <label asp-for="Amount" class="form-label required"></label>
                                        <div class="input-group">
                                            <input asp-for="Amount" class="form-control" placeholder="0.00" step="0.01" />
                                            <div class="input-group-append">
                                                <select asp-for="Currency" class="form-control">
                                                    <option value="CNY">CNY</option>
                                                    <option value="USD">USD</option>
                                                    <option value="JPY">JPY</option>
                                                    <option value="EUR">EUR</option>
                                                </select>
                                            </div>
                                        </div>
                                        <span asp-validation-for="Amount" class="text-danger"></span>
                                    </div>

                                    <!-- 结算金额 -->
                                    <div class="form-group">
                                        <label asp-for="SettlementAmount" class="form-label required"></label>
                                        <input asp-for="SettlementAmount" class="form-control" placeholder="0.00" step="0.01" />
                                        <span asp-validation-for="SettlementAmount" class="text-danger"></span>
                                        <small class="form-text text-muted">结算金额通常与订单金额相同</small>
                                    </div>

                                    <!-- 签约日期 -->
                                    <div class="form-group">
                                        <label asp-for="SignDate" class="form-label required"></label>
                                        <input asp-for="SignDate" class="form-control" type="date" />
                                        <span asp-validation-for="SignDate" class="text-danger"></span>
                                    </div>

                                    <!-- 订单状态 -->
                                    <div class="form-group">
                                        <label asp-for="Status" class="form-label required"></label>
                                        <select asp-for="Status" class="form-control">
                                            <option value="">请选择状态</option>
                                            <option value="New">新建</option>
                                            <option value="InProgress">进行中</option>
                                            <option value="Completed">已完成</option>
                                            <option value="Cancelled">已取消</option>
                                        </select>
                                        <span asp-validation-for="Status" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <div class="row">
                                <div class="col-md-6">
                                    <!-- 客户经理 -->
                                    <div class="form-group">
                                        <label asp-for="AccountManagerId" class="form-label required"></label>
                                        <select asp-for="AccountManagerId" class="form-control">
                                            <option value="">请选择客户经理</option>
                                            @foreach (var user in Model.Users)
                                            {
                                                <option value="@user.Id">@user.Name (@user.Username)</option>
                                            }
                                        </select>
                                        <span asp-validation-for="AccountManagerId" class="text-danger"></span>
                                    </div>

                                    <!-- 创建部门 -->
                                    <div class="form-group">
                                        <label asp-for="DepartmentId" class="form-label required"></label>
                                        <select asp-for="DepartmentId" class="form-control">
                                            <option value="">请选择创建部门</option>
                                            @foreach (var dept in Model.Departments)
                                            {
                                                <option value="@dept.Id">@dept.Name</option>
                                            }
                                        </select>
                                        <span asp-validation-for="DepartmentId" class="text-danger"></span>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <!-- 项目经理 -->
                                    <div class="form-group">
                                        <label asp-for="ProjectManagerId" class="form-label"></label>
                                        <select asp-for="ProjectManagerId" class="form-control">
                                            <option value="">请选择项目经理（可选）</option>
                                            @foreach (var user in Model.Users)
                                            {
                                                <option value="@user.Id">@user.Name (@user.Username)</option>
                                            }
                                        </select>
                                        <span asp-validation-for="ProjectManagerId" class="text-danger"></span>
                                    </div>

                                    <!-- 业绩归属部门 -->
                                    <div class="form-group">
                                        <label asp-for="PerformanceDepartmentId" class="form-label"></label>
                                        <select asp-for="PerformanceDepartmentId" class="form-control">
                                            <option value="">请选择业绩归属部门（可选）</option>
                                            @foreach (var dept in Model.Departments)
                                            {
                                                <option value="@dept.Id">@dept.Name</option>
                                            }
                                        </select>
                                        <span asp-validation-for="PerformanceDepartmentId" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card-footer">
                            <div class="row">
                                <div class="col-md-6">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> 保存
                                    </button>
                                    <a asp-action="Index" class="btn btn-secondary ml-2">
                                        <i class="fas fa-times"></i> 取消
                                    </a>
                                </div>
                                <div class="col-md-6 text-right">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        标有 <span class="text-danger">*</span> 的字段为必填项
                                    </small>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="col-md-4">
                <!-- 帮助信息 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-question-circle"></i> 填写说明
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb"></i> 提示</h6>
                            <ul class="mb-0">
                                <li>选择关联商机后将自动填充客户信息</li>
                                <li>订单名称建议包含项目或产品关键信息</li>
                                <li>客户名称请填写完整的公司或个人名称</li>
                                <li>结算金额通常与订单金额相同，特殊情况可调整</li>
                                <li>客户经理负责订单的跟进和维护</li>
                                <li>项目经理负责订单的执行和交付</li>
                            </ul>
                        </div>

                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> 注意</h6>
                            <p class="mb-0">
                                创建订单后，系统会自动生成订单编号。
                                订单状态可以在后续进行调整。
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 快捷操作 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-bolt"></i> 快捷操作
                        </h3>
                    </div>
                    <div class="card-body">
                        <button type="button" class="btn btn-outline-primary btn-sm btn-block" onclick="copyAmount()">
                            <i class="fas fa-copy"></i> 复制订单金额到结算金额
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm btn-block" onclick="setToday()">
                            <i class="fas fa-calendar-day"></i> 设置签约日期为今天
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        function copyAmount() {
            const amount = document.getElementById('Amount').value;
            if (amount) {
                document.getElementById('SettlementAmount').value = amount;
                toastr.info('已复制订单金额到结算金额');
            }
        }

        function setToday() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('SignDate').value = today;
            toastr.info('已设置签约日期为今天');
        }

        // 表单验证增强
        $(document).ready(function() {
            // 商机选择变更事件
            $('#OpportunitySelect').on('change', function() {
                const selectedOption = $(this).find('option:selected');
                if (selectedOption.val()) {
                    const customerName = selectedOption.data('customer');
                    const opportunityName = selectedOption.data('name');

                    // 自动填充客户信息
                    $('#CustomerName').val(customerName);

                    // 生成订单名称建议
                    if (!$('#Name').val()) {
                        $('#Name').val(customerName + ' - ' + opportunityName);
                    }

                    toastr.success('已自动填充商机相关信息');
                } else {
                    // 清空自动填充的信息（如果用户手动清除了商机选择）
                    if (confirm('是否清空已填充的客户信息？')) {
                        $('#CustomerName').val('');
                        $('#Name').val('');
                    }
                }
            });

            // 金额输入验证
            $('#Amount, #SettlementAmount').on('input', function() {
                const value = parseFloat($(this).val());
                if (value <= 0) {
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });

            // 自动复制金额
            $('#Amount').on('blur', function() {
                const settlementAmount = $('#SettlementAmount').val();
                if (!settlementAmount || settlementAmount === '0') {
                    $('#SettlementAmount').val($(this).val());
                }
            });
        });
    </script>
}

@section Styles {
    <style>
        .required::after {
            content: " *";
            color: red;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .is-invalid {
            border-color: #dc3545;
        }
    </style>
}
