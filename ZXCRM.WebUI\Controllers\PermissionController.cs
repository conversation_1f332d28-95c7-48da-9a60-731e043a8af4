using Microsoft.AspNetCore.Mvc;
using ZXCRM.Service.DTOs;
using ZXCRM.WebUI.Models.ViewModels;
using ZXCRM.WebUI.Services;

namespace ZXCRM.WebUI.Controllers
{
    public class PermissionController : BaseController
    {
        private readonly IPermissionService _permissionService;
        private readonly IUserPermissionService _userPermissionService;
        private readonly IUserService _userService;
        private readonly ILogger<PermissionController> _logger;

        public PermissionController(
            IPermissionService permissionService,
            IUserPermissionService userPermissionService,
            IUserService userService,
            ILogger<PermissionController> logger)
        {
            _permissionService = permissionService;
            _userPermissionService = userPermissionService;
            _userService = userService;
            _logger = logger;
        }

        // GET: Permission (用户权限管理首页)
        public async Task<IActionResult> Index(string searchTerm = "", int pageIndex = 1, int pageSize = 25)
        {
            try
            {
                // 验证分页参数
                pageIndex = Math.Max(1, pageIndex);
                pageSize = Math.Max(1, Math.Min(100, pageSize));

                _logger.LogInformation("Getting user permissions list. SearchTerm: {SearchTerm}, PageIndex: {PageIndex}, PageSize: {PageSize}",
                    searchTerm, pageIndex, pageSize);

                // 获取所有用户
                var usersResponse = await _userService.GetUsersAsync();
                if (!usersResponse.Success || usersResponse.Data == null)
                {
                    SetErrorMessage("获取用户列表失败");
                    return View(new UserPermissionListViewModel());
                }

                var users = usersResponse.Data;

                // 应用搜索过滤
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    users = users.Where(u =>
                        u.Username.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        u.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        u.DepartmentName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)
                    ).ToList();
                }

                var totalCount = users.Count();

                // 应用分页
                var pagedUsers = users
                    .Skip((pageIndex - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                // 构建用户权限信息
                var userPermissionList = new List<UserWithPermissionsDto>();
                foreach (var user in pagedUsers)
                {
                    var userPermissions = await _userPermissionService.GetByUserIdAsync(user.Id);
                    var allPermissions = await _permissionService.GetAllAsync();

                    var businessPermissions = new List<string>();
                    var systemPermissions = new List<string>();

                    foreach (var up in userPermissions)
                    {
                        var permission = allPermissions.FirstOrDefault(p => p.Id == up.PermissionId);
                        if (permission != null)
                        {
                            // 判断是业务模块还是系统模块
                            var businessModules = new[] { "商机管理", "订单管理", "款项管理", "发票管理", "报表统计" };
                            if (businessModules.Contains(up.ModuleType))
                            {
                                businessPermissions.Add(permission.Code);
                            }
                            else
                            {
                                systemPermissions.Add(permission.Code);
                            }
                        }
                    }

                    userPermissionList.Add(new UserWithPermissionsDto
                    {
                        Id = user.Id,
                        Username = user.Username,
                        Name = user.Name,
                        DepartmentName = user.DepartmentName,
                        Role = user.Role,
                        Status = user.Status,
                        CreatedAt = user.CreatedAt,
                        BusinessPermissions = businessPermissions.Distinct().ToList(),
                        SystemPermissions = systemPermissions.Distinct().ToList(),
                        HasBusinessAccess = businessPermissions.Any(),
                        HasSystemAccess = systemPermissions.Any()
                    });
                }

                var viewModel = new UserPermissionListViewModel
                {
                    Users = userPermissionList,
                    SearchTerm = searchTerm,
                    PageIndex = pageIndex,
                    PageSize = pageSize,
                    TotalCount = totalCount
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user permissions list");
                SetErrorMessage("获取用户权限列表时发生错误");
                return View(new UserPermissionListViewModel());
            }
        }

        // GET: Permission/Details/5
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                var permission = await _permissionService.GetByIdAsync(id);
                if (permission == null)
                {
                    SetErrorMessage("权限不存在");
                    return RedirectToAction(nameof(Index));
                }

                return View(permission);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting permission details for ID: {Id}", id);
                SetErrorMessage("获取权限详情时发生错误");
                return RedirectToAction(nameof(Index));
            }
        }

        // GET: Permission/Create
        public IActionResult Create()
        {
            return View(new PermissionDto());
        }

        // POST: Permission/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(PermissionDto permission)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _permissionService.CreateAsync(permission);
                    if (result != null)
                    {
                        SetSuccessMessage($"权限 {permission.Name} 创建成功");
                        return RedirectToAction(nameof(Index));
                    }
                    else
                    {
                        SetErrorMessage("创建权限失败");
                    }
                }

                return View(permission);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating permission: {PermissionName}", permission.Name);
                SetErrorMessage("创建权限时发生错误");
                return View(permission);
            }
        }

        // GET: Permission/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            try
            {
                var permission = await _permissionService.GetByIdAsync(id);
                if (permission == null)
                {
                    SetErrorMessage("权限不存在");
                    return RedirectToAction(nameof(Index));
                }

                return View(permission);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting permission for edit. ID: {Id}", id);
                SetErrorMessage("获取权限信息时发生错误");
                return RedirectToAction(nameof(Index));
            }
        }

        // POST: Permission/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, PermissionDto permission)
        {
            if (id != permission.Id)
            {
                SetErrorMessage("权限ID不匹配");
                return RedirectToAction(nameof(Index));
            }

            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _permissionService.UpdateAsync(permission);
                    if (result != null)
                    {
                        SetSuccessMessage($"权限 {permission.Name} 更新成功");
                        return RedirectToAction(nameof(Index));
                    }
                    else
                    {
                        SetErrorMessage("更新权限失败");
                    }
                }

                return View(permission);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating permission: {PermissionId}", id);
                SetErrorMessage("更新权限时发生错误");
                return View(permission);
            }
        }

        // POST: Permission/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var permission = await _permissionService.GetByIdAsync(id);
                if (permission == null)
                {
                    SetErrorMessage("权限不存在");
                    return RedirectToAction(nameof(Index));
                }

                await _permissionService.DeleteAsync(id);
                SetSuccessMessage($"权限 {permission.Name} 删除成功");
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting permission: {PermissionId}", id);
                SetErrorMessage("删除权限时发生错误，可能存在关联的用户权限");
                return RedirectToAction(nameof(Index));
            }
        }

        // GET: Permission/UserPermissions/5
        public async Task<IActionResult> UserPermissions(int userId)
        {
            try
            {
                var userResponse = await _userService.GetUserByIdAsync(userId);
                if (!userResponse.Success || userResponse.Data == null)
                {
                    SetErrorMessage("用户不存在");
                    return RedirectToAction("Index", "User");
                }

                var userPermissions = await _userPermissionService.GetByUserIdAsync(userId);
                var allPermissions = await _permissionService.GetAllAsync();

                var viewModel = new UserPermissionViewModel
                {
                    UserId = userId,
                    UserName = userResponse.Data.Name,
                    UserPermissions = userPermissions.ToList(),
                    AllPermissions = allPermissions.ToList()
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user permissions for user: {UserId}", userId);
                SetErrorMessage("获取用户权限时发生错误");
                return RedirectToAction("Index", "User");
            }
        }
    }
}
