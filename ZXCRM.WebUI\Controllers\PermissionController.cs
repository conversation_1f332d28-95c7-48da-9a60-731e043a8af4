using Microsoft.AspNetCore.Mvc;
using ZXCRM.Service.DTOs;
using ZXCRM.WebUI.Models.ViewModels;
using ZXCRM.WebUI.Services;

namespace ZXCRM.WebUI.Controllers
{
    public class PermissionController : BaseController
    {
        private readonly IPermissionService _permissionService;
        private readonly IUserPermissionService _userPermissionService;
        private readonly IUserService _userService;
        private readonly ILogger<PermissionController> _logger;

        public PermissionController(
            IPermissionService permissionService,
            IUserPermissionService userPermissionService,
            IUserService userService,
            ILogger<PermissionController> logger)
        {
            _permissionService = permissionService;
            _userPermissionService = userPermissionService;
            _userService = userService;
            _logger = logger;
        }

        // GET: Permission
        public async Task<IActionResult> Index(string searchTerm = "", int pageIndex = 1, int pageSize = 25)
        {
            try
            {
                // 验证分页参数
                pageIndex = Math.Max(1, pageIndex);
                pageSize = Math.Max(1, Math.Min(100, pageSize));

                _logger.LogInformation("Getting permissions list. SearchTerm: {SearchTerm}, PageIndex: {PageIndex}, PageSize: {PageSize}",
                    searchTerm, pageIndex, pageSize);

                var permissions = await _permissionService.GetAllAsync();

                // 应用搜索过滤
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    permissions = permissions.Where(p =>
                        p.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        p.Code.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        (!string.IsNullOrEmpty(p.Description) && p.Description.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)));
                }

                var totalCount = permissions.Count();
                var pagedPermissions = permissions
                    .Skip((pageIndex - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                var viewModel = new PermissionListViewModel
                {
                    Permissions = pagedPermissions,
                    SearchTerm = searchTerm,
                    PageIndex = pageIndex,
                    PageSize = pageSize,
                    TotalCount = totalCount
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting permissions list");
                SetErrorMessage("获取权限列表时发生错误");
                return View(new PermissionListViewModel());
            }
        }

        // GET: Permission/Details/5
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                var permission = await _permissionService.GetByIdAsync(id);
                if (permission == null)
                {
                    SetErrorMessage("权限不存在");
                    return RedirectToAction(nameof(Index));
                }

                return View(permission);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting permission details for ID: {Id}", id);
                SetErrorMessage("获取权限详情时发生错误");
                return RedirectToAction(nameof(Index));
            }
        }

        // GET: Permission/Create
        public IActionResult Create()
        {
            return View(new PermissionDto());
        }

        // POST: Permission/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(PermissionDto permission)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _permissionService.CreateAsync(permission);
                    if (result != null)
                    {
                        SetSuccessMessage($"权限 {permission.Name} 创建成功");
                        return RedirectToAction(nameof(Index));
                    }
                    else
                    {
                        SetErrorMessage("创建权限失败");
                    }
                }

                return View(permission);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating permission: {PermissionName}", permission.Name);
                SetErrorMessage("创建权限时发生错误");
                return View(permission);
            }
        }

        // GET: Permission/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            try
            {
                var permission = await _permissionService.GetByIdAsync(id);
                if (permission == null)
                {
                    SetErrorMessage("权限不存在");
                    return RedirectToAction(nameof(Index));
                }

                return View(permission);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting permission for edit. ID: {Id}", id);
                SetErrorMessage("获取权限信息时发生错误");
                return RedirectToAction(nameof(Index));
            }
        }

        // POST: Permission/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, PermissionDto permission)
        {
            if (id != permission.Id)
            {
                SetErrorMessage("权限ID不匹配");
                return RedirectToAction(nameof(Index));
            }

            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _permissionService.UpdateAsync(permission);
                    if (result != null)
                    {
                        SetSuccessMessage($"权限 {permission.Name} 更新成功");
                        return RedirectToAction(nameof(Index));
                    }
                    else
                    {
                        SetErrorMessage("更新权限失败");
                    }
                }

                return View(permission);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating permission: {PermissionId}", id);
                SetErrorMessage("更新权限时发生错误");
                return View(permission);
            }
        }

        // POST: Permission/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var permission = await _permissionService.GetByIdAsync(id);
                if (permission == null)
                {
                    SetErrorMessage("权限不存在");
                    return RedirectToAction(nameof(Index));
                }

                await _permissionService.DeleteAsync(id);
                SetSuccessMessage($"权限 {permission.Name} 删除成功");
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting permission: {PermissionId}", id);
                SetErrorMessage("删除权限时发生错误，可能存在关联的用户权限");
                return RedirectToAction(nameof(Index));
            }
        }

        // GET: Permission/UserPermissions/5
        public async Task<IActionResult> UserPermissions(int userId)
        {
            try
            {
                var userResponse = await _userService.GetUserByIdAsync(userId);
                if (!userResponse.Success || userResponse.Data == null)
                {
                    SetErrorMessage("用户不存在");
                    return RedirectToAction("Index", "User");
                }

                var userPermissions = await _userPermissionService.GetByUserIdAsync(userId);
                var allPermissions = await _permissionService.GetAllAsync();

                var viewModel = new UserPermissionViewModel
                {
                    UserId = userId,
                    UserName = userResponse.Data.Name,
                    UserPermissions = userPermissions.ToList(),
                    AllPermissions = allPermissions.ToList()
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user permissions for user: {UserId}", userId);
                SetErrorMessage("获取用户权限时发生错误");
                return RedirectToAction("Index", "User");
            }
        }
    }
}
