using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using ZXCRM.Service.DTOs;
using ZXCRM.WebUI.Models.ViewModels;
using ZXCRM.WebUI.Services;

namespace ZXCRM.WebUI.Controllers
{
    [AllowAnonymous]
    public class AccountController : Controller
    {
        private readonly IAuthService _authService;
        private readonly ILogger<AccountController> _logger;

        public AccountController(IAuthService authService, ILogger<AccountController> logger)
        {
            _authService = authService;
            _logger = logger;
        }

        [HttpGet]
        public IActionResult Login(string? returnUrl = null)
        {
            // 如果用户已经登录，重定向到报表统计（首页）
            if (User.Identity?.IsAuthenticated == true)
            {
                return RedirectToAction("Index", "Report");
            }

            var model = new LoginViewModel { ReturnUrl = returnUrl };

            // 从Cookie中读取记住的登录信息
            if (Request.Cookies.ContainsKey("RememberedUsername"))
            {
                model.Username = Request.Cookies["RememberedUsername"] ?? string.Empty;
                model.RememberMe = true; // 如果有记住的信息，默认勾选记住我
            }

            if (Request.Cookies.ContainsKey("RememberedPassword"))
            {
                model.Password = Request.Cookies["RememberedPassword"] ?? string.Empty;
            }

            ViewData["ReturnUrl"] = returnUrl;
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Login(LoginViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return View(model);
                }

                _logger.LogInformation("Login attempt for user: {Username}", model.Username);

                var loginRequest = new LoginDTO
                {
                    Username = model.Username,
                    Password = model.Password
                };

                var response = await _authService.LoginAsync(loginRequest);

                if (response.Success && response.Data != null)
                {
                    // 从JWT Token中解析Role
                    var userRole = ExtractRoleFromJwtToken(response.Data.AccessToken) ?? "User";

                    // 创建用户声明
                    var claims = new List<Claim>
                    {
                        new Claim(ClaimTypes.Name, response.Data.User.Username ?? string.Empty),
                        new Claim("UserId", response.Data.User.Id.ToString()),
                        new Claim("UserName", response.Data.User.Name ?? string.Empty),
                        new Claim("Email", response.Data.User.Email ?? string.Empty),
                        new Claim("DepartmentId", response.Data.User.DepartmentId.ToString()),
                        new Claim("DepartmentName", response.Data.User.DepartmentName ?? string.Empty),
                        new Claim(ClaimTypes.Role, userRole), // 从JWT Token中获取的真实角色
                        new Claim("AccessToken", response.Data.AccessToken ?? string.Empty)
                    };

                    var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
                    var authProperties = new AuthenticationProperties
                    {
                        IsPersistent = model.RememberMe,
                        ExpiresUtc = DateTimeOffset.UtcNow.AddHours(8)
                    };

                    await HttpContext.SignInAsync(
                        CookieAuthenticationDefaults.AuthenticationScheme,
                        new ClaimsPrincipal(claimsIdentity),
                        authProperties);

                    // 处理记住登录信息
                    if (model.RememberMe)
                    {
                        // 保存用户名和密码到Cookie（有效期30天）
                        var cookieOptions = new CookieOptions
                        {
                            Expires = DateTimeOffset.UtcNow.AddDays(30),
                            HttpOnly = true,
                            Secure = Request.IsHttps,
                            SameSite = SameSiteMode.Lax
                        };

                        Response.Cookies.Append("RememberedUsername", model.Username, cookieOptions);
                        Response.Cookies.Append("RememberedPassword", model.Password, cookieOptions);
                    }
                    else
                    {
                        // 如果用户取消了记住我，删除相关Cookie
                        Response.Cookies.Delete("RememberedUsername");
                        Response.Cookies.Delete("RememberedPassword");
                    }

                    _logger.LogInformation("User {Username} logged in successfully", model.Username);

                    // 重定向到返回URL或首页
                    if (!string.IsNullOrEmpty(model.ReturnUrl) && Url.IsLocalUrl(model.ReturnUrl))
                    {
                        return Redirect(model.ReturnUrl);
                    }

                    return RedirectToAction("Index", "Report");
                }
                else
                {
                    _logger.LogWarning("Login failed for user: {Username}. Message: {Message}",
                        model.Username, response.Message);

                    ModelState.AddModelError(string.Empty, response.Message ?? "登录失败，请检查用户名和密码");
                    return View(model);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login for user: {Username}", model.Username);
                ModelState.AddModelError(string.Empty, "登录过程中发生错误，请稍后重试");
                return View(model);
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Logout(bool clearRememberedInfo = false)
        {
            try
            {
                _logger.LogInformation("User {Username} logging out", User.Identity?.Name);

                // 调用API登出
                await _authService.LogoutAsync();

                // 清除本地认证Cookie
                await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);

                // 如果用户选择清除记住的登录信息
                if (clearRememberedInfo)
                {
                    Response.Cookies.Delete("RememberedUsername");
                    Response.Cookies.Delete("RememberedPassword");
                    _logger.LogInformation("Remembered login info cleared");
                }

                _logger.LogInformation("User logged out successfully");

                return RedirectToAction("Login");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout");

                // 即使API调用失败，也要清除本地Cookie
                await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);

                return RedirectToAction("Login");
            }
        }

        [HttpGet]
        public IActionResult AccessDenied()
        {
            return View();
        }

        /// <summary>
        /// 从JWT Token中提取Role信息
        /// </summary>
        private string? ExtractRoleFromJwtToken(string? token)
        {
            if (string.IsNullOrEmpty(token))
                return null;

            try
            {
                // JWT Token格式: header.payload.signature
                var parts = token.Split('.');
                if (parts.Length != 3)
                    return null;

                // 解码payload部分
                var payload = parts[1];

                // 添加必要的padding
                switch (payload.Length % 4)
                {
                    case 2: payload += "=="; break;
                    case 3: payload += "="; break;
                }

                var payloadBytes = Convert.FromBase64String(payload);
                var payloadJson = Encoding.UTF8.GetString(payloadBytes);

                // 解析JSON获取role
                var payloadData = JsonSerializer.Deserialize<Dictionary<string, object>>(payloadJson);

                if (payloadData != null && payloadData.TryGetValue("role", out var roleValue))
                {
                    return roleValue?.ToString();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting role from JWT token");
            }

            return null;
        }
    }
}
