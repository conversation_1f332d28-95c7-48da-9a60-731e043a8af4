using ZXCRM.Service.DTOs;

namespace ZXCRM.Service.Interface
{
    public interface IUserService
    {
        Task<IEnumerable<UserDTO>> GetAllUsersAsync();
        Task<UserDTO?> GetUserByIdAsync(int id);
        Task<UserDTO?> GetUserByUsernameAsync(string username);
        Task<UserDTO> CreateUserAsync(CreateUserDTO createUserDto);
        Task<UserDTO?> UpdateUserAsync(UpdateUserDTO updateUserDto);
        Task<bool> DeleteUserAsync(int id);
        Task<bool> CheckUserPermissionAsync(int userId, string moduleType, string permissionCode);
    }
}
