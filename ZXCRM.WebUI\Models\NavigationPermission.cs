namespace ZXCRM.WebUI.Models
{
    /// <summary>
    /// 导航栏权限配置
    /// </summary>
    public class NavigationItem
    {
        public string Name { get; set; } = string.Empty;
        public string Controller { get; set; } = string.Empty;
        public string Action { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string ModuleType { get; set; } = string.Empty;
        public string PermissionCode { get; set; } = string.Empty;
        public int Order { get; set; }
        public bool IsAlwaysVisible { get; set; } = false; // 是否总是可见（如仪表盘）
    }

    /// <summary>
    /// 导航栏权限管理器
    /// </summary>
    public static class NavigationPermissionManager
    {
        /// <summary>
        /// 获取所有导航项配置
        /// </summary>
        public static List<NavigationItem> GetAllNavigationItems()
        {
            return new List<NavigationItem>
            {
                new NavigationItem
                {
                    Name = "仪表盘",
                    Controller = "Dashboard",
                    Action = "Index",
                    Icon = "fas fa-tachometer-alt",
                    ModuleType = "",
                    PermissionCode = "",
                    Order = 1,
                    IsAlwaysVisible = true // 仪表盘对所有用户可见
                },
                new NavigationItem
                {
                    Name = "用户管理",
                    Controller = "User",
                    Action = "Index",
                    Icon = "fas fa-users",
                    ModuleType = "用户",
                    PermissionCode = "Query",
                    Order = 2
                },
                new NavigationItem
                {
                    Name = "部门管理",
                    Controller = "Department",
                    Action = "Index",
                    Icon = "fas fa-building",
                    ModuleType = "部门",
                    PermissionCode = "Query",
                    Order = 3
                },
                new NavigationItem
                {
                    Name = "商机管理",
                    Controller = "Opportunity",
                    Action = "Index",
                    Icon = "fas fa-seedling",
                    ModuleType = "商机",
                    PermissionCode = "Query",
                    Order = 4
                },
                new NavigationItem
                {
                    Name = "订单管理",
                    Controller = "Order",
                    Action = "Index",
                    Icon = "fas fa-shopping-cart",
                    ModuleType = "订单",
                    PermissionCode = "Query",
                    Order = 5
                },
                new NavigationItem
                {
                    Name = "款项管理",
                    Controller = "Payment",
                    Action = "Index",
                    Icon = "fas fa-money-bill",
                    ModuleType = "款项",
                    PermissionCode = "Query",
                    Order = 6
                },
                new NavigationItem
                {
                    Name = "发票管理",
                    Controller = "Invoice",
                    Action = "Index",
                    Icon = "fas fa-file-invoice",
                    ModuleType = "发票",
                    PermissionCode = "Query",
                    Order = 7
                },
                new NavigationItem
                {
                    Name = "报表统计",
                    Controller = "Report",
                    Action = "Index",
                    Icon = "fas fa-chart-bar",
                    ModuleType = "报表",
                    PermissionCode = "Query",
                    Order = 8
                }
            };
        }

        /// <summary>
        /// 根据用户权限过滤导航项
        /// </summary>
        public static List<NavigationItem> FilterNavigationByPermissions(List<NavigationItem> allItems, Dictionary<string, bool> userPermissions)
        {
            var filteredItems = new List<NavigationItem>();

            foreach (var item in allItems.OrderBy(i => i.Order))
            {
                // 总是可见的项目（如仪表盘）
                if (item.IsAlwaysVisible)
                {
                    filteredItems.Add(item);
                    continue;
                }

                // 检查用户是否有该模块的权限
                var permissionKey = $"{item.ModuleType}_{item.PermissionCode}";
                if (userPermissions.ContainsKey(permissionKey) && userPermissions[permissionKey])
                {
                    filteredItems.Add(item);
                }
            }

            return filteredItems;
        }

        /// <summary>
        /// 生成权限键
        /// </summary>
        public static string GeneratePermissionKey(string moduleType, string permissionCode)
        {
            return $"{moduleType}_{permissionCode}";
        }
    }

    /// <summary>
    /// 用户导航权限ViewModel
    /// </summary>
    public class UserNavigationViewModel
    {
        public List<NavigationItem> VisibleNavigationItems { get; set; } = new();
        public Dictionary<string, bool> UserPermissions { get; set; } = new();
        public string CurrentController { get; set; } = string.Empty;
        public string CurrentAction { get; set; } = string.Empty;
    }
}
