using System.ComponentModel.DataAnnotations;

namespace ZXCRM.WebUI.Models.ViewModels
{
    public class OrderListViewModel
    {
        public List<OrderItemViewModel> Orders { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageIndex { get; set; }
        public int PageSize { get; set; }
        public string SearchTerm { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
    }

    public class OrderItemViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string AccountManagerName { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public DateTime SignDate { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public class CreateOrderViewModel
    {
        [Required(ErrorMessage = "订单名称不能为空")]
        [StringLength(200, ErrorMessage = "订单名称长度不能超过200个字符")]
        [Display(Name = "订单名称")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "客户名称不能为空")]
        [StringLength(200, ErrorMessage = "客户名称长度不能超过200个字符")]
        [Display(Name = "客户名称")]
        public string CustomerName { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "联系人姓名长度不能超过100个字符")]
        [Display(Name = "联系人")]
        public string? ContactName { get; set; }

        [Phone(ErrorMessage = "联系电话格式不正确")]
        [StringLength(20, ErrorMessage = "联系电话长度不能超过20个字符")]
        [Display(Name = "联系电话")]
        public string? ContactPhone { get; set; }

        [Required(ErrorMessage = "订单金额不能为空")]
        [Range(0.01, double.MaxValue, ErrorMessage = "订单金额必须大于0")]
        [Display(Name = "订单金额")]
        public decimal Amount { get; set; }

        [Required(ErrorMessage = "币种不能为空")]
        [Display(Name = "币种")]
        public string Currency { get; set; } = "CNY";

        [Required(ErrorMessage = "结算金额不能为空")]
        [Range(0.01, double.MaxValue, ErrorMessage = "结算金额必须大于0")]
        [Display(Name = "结算金额")]
        public decimal SettlementAmount { get; set; }

        [Required(ErrorMessage = "请选择客户经理")]
        [Display(Name = "客户经理")]
        public int AccountManagerId { get; set; }

        [Required(ErrorMessage = "请选择创建部门")]
        [Display(Name = "创建部门")]
        public int DepartmentId { get; set; }

        [Required(ErrorMessage = "签约日期不能为空")]
        [Display(Name = "签约日期")]
        public DateTime SignDate { get; set; } = DateTime.Today;

        [Required(ErrorMessage = "请选择订单状态")]
        [Display(Name = "订单状态")]
        public string Status { get; set; } = "待确认";

        [Display(Name = "项目经理")]
        public int? ProjectManagerId { get; set; }

        [Display(Name = "业绩归属部门")]
        public int? PerformanceDepartmentId { get; set; }

        [Display(Name = "关联商机")]
        public int? OpportunityId { get; set; }

        // 用于下拉列表
        public List<UserSelectItem> Users { get; set; } = new();
        public List<DepartmentSelectItem> Departments { get; set; } = new();
        public List<OpportunitySelectItem> Opportunities { get; set; } = new();
    }

    public class EditOrderViewModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "订单名称不能为空")]
        [StringLength(200, ErrorMessage = "订单名称长度不能超过200个字符")]
        [Display(Name = "订单名称")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "订单编号不能为空")]
        [StringLength(50, ErrorMessage = "订单编号长度不能超过50个字符")]
        [Display(Name = "订单编号")]
        public string Code { get; set; } = string.Empty;

        [Required(ErrorMessage = "客户名称不能为空")]
        [StringLength(200, ErrorMessage = "客户名称长度不能超过200个字符")]
        [Display(Name = "客户名称")]
        public string CustomerName { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "联系人姓名长度不能超过100个字符")]
        [Display(Name = "联系人")]
        public string? ContactName { get; set; }

        [Phone(ErrorMessage = "联系电话格式不正确")]
        [StringLength(20, ErrorMessage = "联系电话长度不能超过20个字符")]
        [Display(Name = "联系电话")]
        public string? ContactPhone { get; set; }

        [Required(ErrorMessage = "订单金额不能为空")]
        [Range(0.01, double.MaxValue, ErrorMessage = "订单金额必须大于0")]
        [Display(Name = "订单金额")]
        public decimal Amount { get; set; }

        [Required(ErrorMessage = "币种不能为空")]
        [Display(Name = "币种")]
        public string Currency { get; set; } = "CNY";

        [Required(ErrorMessage = "结算金额不能为空")]
        [Range(0.01, double.MaxValue, ErrorMessage = "结算金额必须大于0")]
        [Display(Name = "结算金额")]
        public decimal SettlementAmount { get; set; }

        [Required(ErrorMessage = "请选择客户经理")]
        [Display(Name = "客户经理")]
        public int AccountManagerId { get; set; }

        [Required(ErrorMessage = "请选择创建部门")]
        [Display(Name = "创建部门")]
        public int DepartmentId { get; set; }

        [Required(ErrorMessage = "签约日期不能为空")]
        [Display(Name = "签约日期")]
        public DateTime SignDate { get; set; }

        [Required(ErrorMessage = "请选择订单状态")]
        [Display(Name = "订单状态")]
        public string Status { get; set; } = string.Empty;

        [Display(Name = "项目经理")]
        public int? ProjectManagerId { get; set; }

        [Display(Name = "业绩归属部门")]
        public int? PerformanceDepartmentId { get; set; }

        // 用于下拉列表
        public List<UserSelectItem> Users { get; set; } = new();
        public List<DepartmentSelectItem> Departments { get; set; } = new();
    }

    public class OrderDetailViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string ContactName { get; set; } = string.Empty;
        public string ContactPhone { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public decimal SettlementAmount { get; set; }
        public string Status { get; set; } = string.Empty;
        public string AccountManagerName { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public string ProjectManagerName { get; set; } = string.Empty;
        public string PerformanceDepartmentName { get; set; } = string.Empty;
        public string OpportunityName { get; set; } = string.Empty;
        public DateTime SignDate { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public string CreatedByName { get; set; } = string.Empty;
    }

    public class UserSelectItem
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Username { get; set; } = string.Empty;
    }

    public class OpportunitySelectItem
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
    }

    // DepartmentSelectItem 已经在 DepartmentViewModel.cs 中定义了，这里不需要重复定义
}
