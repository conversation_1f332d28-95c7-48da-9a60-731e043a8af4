using Microsoft.EntityFrameworkCore;
using ZXCRM.Data.Entities;

namespace ZXCRM.Data.Context
{
    public static class DbInitializer
    {
        public static async Task InitializeAsync(ZXCRMDbContext context)
        {
            // 检查管理员用户是否存在
            var adminUser = await context.Users.FirstOrDefaultAsync(u => u.Username == "admin");

            if (adminUser == null)
            {
                throw new Exception("管理员用户不存在，请先手动执行数据库初始化脚本。");
            }
            else
            {
                // 更新现有管理员用户的密码
                adminUser.Password = BCrypt.Net.BCrypt.HashPassword("admin123");
                adminUser.UpdatedAt = DateTime.Now;
                context.Users.Update(adminUser);
                await context.SaveChangesAsync();
            }
        }
    }
}
