using ZXCRM.Service.DTOs;

namespace ZXCRM.WebUI.Services
{
    public interface IUserPermissionService
    {
        Task<IEnumerable<UserPermissionDto>> GetByUserIdAsync(int userId);
        Task<bool> AssignPermissionAsync(int userId, int permissionId, string moduleType);
        Task<bool> RemovePermissionAsync(int userId, int permissionId, string moduleType);
        Task<bool> UpdateUserPermissionsAsync(int userId, List<UserPermissionDto> permissions);
    }
}
