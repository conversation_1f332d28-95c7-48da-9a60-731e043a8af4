using ZXCRM.Service.DTOs;
using ZXCRM.WebUI.Models;

namespace ZXCRM.WebUI.Services
{
    public interface IInvoiceService
    {
        Task<ApiResponse<List<InvoiceDTO>>> GetInvoicesAsync();
        Task<ApiResponse<InvoiceDTO>> GetInvoiceByIdAsync(int id);
        Task<ApiResponse<InvoiceDTO>> CreateInvoiceAsync(CreateInvoiceDTO request);
        Task<ApiResponse<InvoiceDTO>> UpdateInvoiceAsync(int id, UpdateInvoiceDTO request);
        Task<ApiResponse<object>> DeleteInvoiceAsync(int id);
        Task<ApiResponse<List<InvoiceDTO>>> GetInvoicesByPaymentIdAsync(int paymentId);
        Task<ApiResponse<decimal>> GetValidInvoiceAmountByPaymentIdAsync(int paymentId);
    }

    public class InvoiceService : IInvoiceService
    {
        private readonly IApiService _apiService;
        private readonly ILogger<InvoiceService> _logger;

        public InvoiceService(IApiService apiService, ILogger<InvoiceService> logger)
        {
            _apiService = apiService;
            _logger = logger;
        }

        public async Task<ApiResponse<List<InvoiceDTO>>> GetInvoicesAsync()
        {
            try
            {
                _logger.LogInformation("Getting invoices from API");
                var response = await _apiService.GetAsync<List<InvoiceDTO>>("api/invoice");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoices");
                return new ApiResponse<List<InvoiceDTO>>
                {
                    Success = false,
                    Message = "获取发票列表失败",
                    Data = new List<InvoiceDTO>()
                };
            }
        }

        public async Task<ApiResponse<InvoiceDTO>> GetInvoiceByIdAsync(int id)
        {
            try
            {
                _logger.LogInformation("Getting invoice by ID: {InvoiceId}", id);
                var response = await _apiService.GetAsync<InvoiceDTO>($"api/invoice/{id}");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoice by ID: {InvoiceId}", id);
                return new ApiResponse<InvoiceDTO>
                {
                    Success = false,
                    Message = "获取发票详情失败",
                    Data = null
                };
            }
        }

        public async Task<ApiResponse<InvoiceDTO>> CreateInvoiceAsync(CreateInvoiceDTO request)
        {
            try
            {
                _logger.LogInformation("Creating invoice for payment: {PaymentId}", request.PaymentId);
                var response = await _apiService.PostAsync<InvoiceDTO>("api/invoice", request);
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating invoice for payment: {PaymentId}", request.PaymentId);
                return new ApiResponse<InvoiceDTO>
                {
                    Success = false,
                    Message = "创建发票失败",
                    Data = null
                };
            }
        }

        public async Task<ApiResponse<InvoiceDTO>> UpdateInvoiceAsync(int id, UpdateInvoiceDTO request)
        {
            try
            {
                _logger.LogInformation("Updating invoice: {InvoiceId}", id);
                var response = await _apiService.PutAsync<InvoiceDTO>($"api/invoice/{id}", request);
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating invoice: {InvoiceId}", id);
                return new ApiResponse<InvoiceDTO>
                {
                    Success = false,
                    Message = "更新发票失败",
                    Data = null
                };
            }
        }

        public async Task<ApiResponse<object>> DeleteInvoiceAsync(int id)
        {
            try
            {
                _logger.LogInformation("Deleting invoice: {InvoiceId}", id);
                var response = await _apiService.DeleteAsync<object>($"api/invoice/{id}");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting invoice: {InvoiceId}", id);
                return new ApiResponse<object>
                {
                    Success = false,
                    Message = "删除发票失败",
                    Data = null
                };
            }
        }

        public async Task<ApiResponse<List<InvoiceDTO>>> GetInvoicesByPaymentIdAsync(int paymentId)
        {
            try
            {
                _logger.LogInformation("Getting invoices by payment: {PaymentId}", paymentId);
                var response = await _apiService.GetAsync<List<InvoiceDTO>>($"api/invoice/payment/{paymentId}");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoices by payment: {PaymentId}", paymentId);
                return new ApiResponse<List<InvoiceDTO>>
                {
                    Success = false,
                    Message = "获取款项发票失败",
                    Data = new List<InvoiceDTO>()
                };
            }
        }

        public async Task<ApiResponse<decimal>> GetValidInvoiceAmountByPaymentIdAsync(int paymentId)
        {
            try
            {
                _logger.LogInformation("Getting valid invoice amount by payment: {PaymentId}", paymentId);
                var response = await _apiService.GetAsync<dynamic>($"api/invoice/payment/{paymentId}/amount");
                
                if (response.Success && response.Data != null)
                {
                    var amount = Convert.ToDecimal(response.Data.GetType().GetProperty("Amount")?.GetValue(response.Data) ?? 0);
                    return new ApiResponse<decimal>
                    {
                        Success = true,
                        Message = response.Message,
                        Data = amount
                    };
                }
                
                return new ApiResponse<decimal>
                {
                    Success = false,
                    Message = response.Message ?? "获取有效发票金额失败",
                    Data = 0
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting valid invoice amount by payment: {PaymentId}", paymentId);
                return new ApiResponse<decimal>
                {
                    Success = false,
                    Message = "获取有效发票金额失败",
                    Data = 0
                };
            }
        }
    }
}
