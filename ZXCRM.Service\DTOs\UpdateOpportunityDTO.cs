using System.ComponentModel.DataAnnotations;

namespace ZXCRM.Service.DTOs
{
    public class UpdateOpportunityDTO
    {
        [Required(ErrorMessage = "商机ID不能为空")]
        public int Id { get; set; }

        [Required(ErrorMessage = "商机名称不能为空")]
        [StringLength(100, ErrorMessage = "商机名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "客户名称不能为空")]
        [StringLength(100, ErrorMessage = "客户名称长度不能超过100个字符")]
        public string CustomerName { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "联系人长度不能超过50个字符")]
        public string? ContactName { get; set; }

        [StringLength(20, ErrorMessage = "联系电话长度不能超过20个字符")]
        public string? ContactPhone { get; set; }

        [StringLength(500, ErrorMessage = "商机内容长度不能超过500个字符")]
        public string? Content { get; set; }

        [Required(ErrorMessage = "商机状态不能为空")]
        [StringLength(20, ErrorMessage = "商机状态长度不能超过20个字符")]
        public string Status { get; set; } = string.Empty;
    }
}
