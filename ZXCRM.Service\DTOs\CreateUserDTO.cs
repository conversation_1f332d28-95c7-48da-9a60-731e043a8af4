using System.ComponentModel.DataAnnotations;

namespace ZXCRM.Service.DTOs
{
    public class CreateUserDTO
    {
        [Required(ErrorMessage = "用户名不能为空")]
        [StringLength(50, ErrorMessage = "用户名长度不能超过50个字符")]
        public string Username { get; set; } = string.Empty;

        [Required(ErrorMessage = "密码不能为空")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "密码长度必须在6-100个字符之间")]
        public string Password { get; set; } = string.Empty;

        [Required(ErrorMessage = "姓名不能为空")]
        [StringLength(50, ErrorMessage = "姓名长度不能超过50个字符")]
        public string Name { get; set; } = string.Empty;

        public string? Gender { get; set; }
        public string? Email { get; set; }
        public string? Phone { get; set; }

        [Required(ErrorMessage = "部门ID不能为空")]
        public int DepartmentId { get; set; }
    }
}
