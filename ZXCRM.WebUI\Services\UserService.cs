using ZXCRM.Service.DTOs;
using ZXCRM.WebUI.Models;

namespace ZXCRM.WebUI.Services
{
    public class UserService : IUserService
    {
        private readonly IApiService _apiService;
        private readonly ILogger<UserService> _logger;

        public UserService(IApiService apiService, ILogger<UserService> logger)
        {
            _apiService = apiService;
            _logger = logger;
        }

        public async Task<ApiResponse<List<UserDTO>>> GetUsersAsync()
        {
            try
            {
                _logger.LogInformation("Getting users list");

                var response = await _apiService.GetAsync<List<UserDTO>>("api/user");

                if (response.Success)
                {
                    _logger.LogInformation("Successfully retrieved {Count} users", response.Data?.Count ?? 0);
                }
                else
                {
                    _logger.LogWarning("Failed to retrieve users: {Message}", response.Message);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting users list");
                return new ApiResponse<List<UserDTO>>
                {
                    Success = false,
                    Message = $"Failed to get users: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<UserDTO>> GetUserByIdAsync(int id)
        {
            try
            {
                _logger.LogInformation("Getting user by ID: {UserId}", id);

                var response = await _apiService.GetAsync<UserDTO>($"api/user/{id}");

                if (response.Success)
                {
                    _logger.LogInformation("Successfully retrieved user: {UserId}", id);
                }
                else
                {
                    _logger.LogWarning("Failed to retrieve user {UserId}: {Message}", id, response.Message);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user by ID: {UserId}", id);
                return new ApiResponse<UserDTO>
                {
                    Success = false,
                    Message = $"Failed to get user: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<UserDTO>> CreateUserAsync(CreateUserDTO request)
        {
            try
            {
                _logger.LogInformation("Creating new user: {Username}", request.Username);

                var response = await _apiService.PostAsync<UserDTO>("api/user", request);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully created user: {Username}", request.Username);
                }
                else
                {
                    _logger.LogWarning("Failed to create user {Username}: {Message}", request.Username, response.Message);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user: {Username}", request.Username);
                return new ApiResponse<UserDTO>
                {
                    Success = false,
                    Message = $"Failed to create user: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<UserDTO>> UpdateUserAsync(int id, UpdateUserDTO request)
        {
            try
            {
                _logger.LogInformation("Updating user: {UserId}", id);

                var response = await _apiService.PutAsync<UserDTO>($"api/user/{id}", request);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully updated user: {UserId}", id);
                }
                else
                {
                    _logger.LogWarning("Failed to update user {UserId}: {Message}", id, response.Message);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user: {UserId}", id);
                return new ApiResponse<UserDTO>
                {
                    Success = false,
                    Message = $"Failed to update user: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<object>> DeleteUserAsync(int id)
        {
            try
            {
                _logger.LogInformation("Deleting user: {UserId}", id);

                var response = await _apiService.DeleteAsync<object>($"api/user/{id}");

                if (response.Success)
                {
                    _logger.LogInformation("Successfully deleted user: {UserId}", id);
                }
                else
                {
                    _logger.LogWarning("Failed to delete user {UserId}: {Message}", id, response.Message);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user: {UserId}", id);
                return new ApiResponse<object>
                {
                    Success = false,
                    Message = $"Failed to delete user: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<List<DepartmentDTO>>> GetDepartmentsAsync()
        {
            try
            {
                _logger.LogInformation("Getting departments list");

                var response = await _apiService.GetAsync<List<DepartmentDTO>>("api/department");

                if (response.Success)
                {
                    _logger.LogInformation("Successfully retrieved {Count} departments", response.Data?.Count ?? 0);
                }
                else
                {
                    _logger.LogWarning("Failed to retrieve departments: {Message}", response.Message);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting departments list");
                return new ApiResponse<List<DepartmentDTO>>
                {
                    Success = false,
                    Message = $"Failed to get departments: {ex.Message}"
                };
            }
        }

        public async Task<bool> CheckUserPermissionAsync(int userId, string moduleType, string permissionCode)
        {
            try
            {
                _logger.LogInformation("Checking permission for user {UserId}, module {ModuleType}, permission {PermissionCode}",
                    userId, moduleType, permissionCode);

                var response = await _apiService.GetAsync<bool>($"api/user/check-permission?moduleType={Uri.EscapeDataString(moduleType)}&permissionCode={Uri.EscapeDataString(permissionCode)}");

                if (response.Success)
                {
                    _logger.LogInformation("Permission check result for user {UserId}: {HasPermission}", userId, response.Data);
                    return response.Data;
                }
                else
                {
                    _logger.LogWarning("Permission check failed for user {UserId}: {Message}", userId, response.Message);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking permission for user {UserId}, module {ModuleType}, permission {PermissionCode}",
                    userId, moduleType, permissionCode);
                return false;
            }
        }
    }
}
