@model ZXCRM.Service.DTOs.PermissionDto
@{
    ViewData["Title"] = "创建权限";
}

<!-- 创建页面容器 -->
<div class="create-page-container">
    <!-- 页面标题栏 -->
    <div class="create-header">
        <div class="create-title">
            <h2>
                <i class="fas fa-plus-circle"></i>
                创建权限
            </h2>
            <small class="text-muted">添加新的系统权限</small>
        </div>
        <div class="create-actions">
            <a asp-action="Index" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> 返回列表
            </a>
        </div>
    </div>

    <!-- 创建表单 -->
    <div class="create-content">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-key"></i>
                            权限信息
                        </h5>
                    </div>
                    <div class="card-body">
                        <form asp-action="Create" method="post" class="needs-validation" novalidate>
                            @Html.AntiForgeryToken()

                            <div class="row">
                                <div class="col-md-12">
                                    <!-- 权限名称 -->
                                    <div class="form-group mb-3">
                                        <label asp-for="Name" class="form-label required">权限名称</label>
                                        <input asp-for="Name" class="form-control" placeholder="请输入权限名称" required maxlength="50">
                                        <span asp-validation-for="Name" class="text-danger"></span>
                                        <div class="form-text">权限的显示名称，如"查看订单"、"编辑用户"等</div>
                                    </div>

                                    <!-- 权限代码 -->
                                    <div class="form-group mb-3">
                                        <label asp-for="Code" class="form-label required">权限代码</label>
                                        <input asp-for="Code" class="form-control" placeholder="请输入权限代码" required maxlength="50">
                                        <span asp-validation-for="Code" class="text-danger"></span>
                                        <div class="form-text">权限的唯一标识符，如"ORDER_VIEW"、"USER_EDIT"等，建议使用大写字母和下划线</div>
                                    </div>

                                    <!-- 模块类型 -->
                                    <div class="form-group mb-3">
                                        <label asp-for="ModuleType" class="form-label">模块类型</label>
                                        <select asp-for="ModuleType" class="form-select">
                                            <option value="">请选择模块类型</option>
                                            <option value="系统管理">系统管理</option>
                                            <option value="用户管理">用户管理</option>
                                            <option value="部门管理">部门管理</option>
                                            <option value="权限管理">权限管理</option>
                                            <option value="商机管理">商机管理</option>
                                            <option value="订单管理">订单管理</option>
                                            <option value="款项管理">款项管理</option>
                                            <option value="发票管理">发票管理</option>
                                            <option value="报表统计">报表统计</option>
                                        </select>
                                        <span asp-validation-for="ModuleType" class="text-danger"></span>
                                        <div class="form-text">权限所属的功能模块</div>
                                    </div>

                                    <!-- 权限描述 -->
                                    <div class="form-group mb-4">
                                        <label asp-for="Description" class="form-label">权限描述</label>
                                        <textarea asp-for="Description" class="form-control" rows="3" placeholder="请输入权限描述" maxlength="200"></textarea>
                                        <span asp-validation-for="Description" class="text-danger"></span>
                                        <div class="form-text">详细描述该权限的作用和使用场景</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 表单按钮 -->
                            <div class="form-actions">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-save"></i> 创建权限
                                                </button>
                                                <button type="reset" class="btn btn-outline-secondary">
                                                    <i class="fas fa-undo"></i> 重置表单
                                                </button>
                                            </div>
                                            <div>
                                                <a asp-action="Index" class="btn btn-outline-secondary">
                                                    <i class="fas fa-list"></i> 返回列表
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 常用权限示例 -->
                <div class="card mt-3">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-lightbulb text-warning"></i>
                            常用权限示例
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-muted">订单管理</h6>
                                <ul class="list-unstyled text-sm">
                                    <li><code>ORDER_VIEW</code> - 查看订单</li>
                                    <li><code>ORDER_CREATE</code> - 创建订单</li>
                                    <li><code>ORDER_EDIT</code> - 编辑订单</li>
                                    <li><code>ORDER_DELETE</code> - 删除订单</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-muted">用户管理</h6>
                                <ul class="list-unstyled text-sm">
                                    <li><code>USER_VIEW</code> - 查看用户</li>
                                    <li><code>USER_CREATE</code> - 创建用户</li>
                                    <li><code>USER_EDIT</code> - 编辑用户</li>
                                    <li><code>USER_DELETE</code> - 删除用户</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 创建提示 -->
                <div class="card mt-3">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-info-circle text-info"></i>
                            创建提示
                        </h6>
                        <ul class="mb-0 text-muted">
                            <li>权限名称应该简洁明了，便于管理员理解</li>
                            <li>权限代码必须唯一，建议使用模块_操作的格式</li>
                            <li>创建权限后，可以在用户管理中为用户分配该权限</li>
                            <li>建议按功能模块分组创建权限，便于管理</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // 表单验证
            $('.needs-validation').on('submit', function(e) {
                if (!this.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                $(this).addClass('was-validated');
            });

            // 权限代码自动转换为大写
            $('#Code').on('input', function() {
                $(this).val($(this).val().toUpperCase());
            });

            // 根据权限名称自动生成权限代码建议
            $('#Name').on('blur', function() {
                var name = $(this).val();
                var codeInput = $('#Code');
                
                if (name && !codeInput.val()) {
                    var suggestedCode = name
                        .replace(/查看|浏览/g, 'VIEW')
                        .replace(/创建|新增|添加/g, 'CREATE')
                        .replace(/编辑|修改|更新/g, 'EDIT')
                        .replace(/删除|移除/g, 'DELETE')
                        .replace(/订单/g, 'ORDER')
                        .replace(/用户/g, 'USER')
                        .replace(/部门/g, 'DEPT')
                        .replace(/权限/g, 'PERM')
                        .replace(/商机/g, 'OPP')
                        .replace(/款项/g, 'PAY')
                        .replace(/发票/g, 'INV')
                        .replace(/报表/g, 'RPT')
                        .replace(/[^A-Z_]/g, '_')
                        .replace(/_+/g, '_')
                        .replace(/^_|_$/g, '');
                    
                    if (suggestedCode) {
                        codeInput.val(suggestedCode);
                        codeInput.focus();
                    }
                }
            });

            // 字符计数
            $('#Description').on('input', function() {
                var maxLength = 200;
                var currentLength = $(this).val().length;
                var remaining = maxLength - currentLength;
                
                var helpText = $(this).siblings('.form-text');
                if (remaining < 20) {
                    helpText.html('还可以输入 ' + remaining + ' 个字符');
                    helpText.removeClass('text-muted').addClass('text-warning');
                } else {
                    helpText.html('详细描述该权限的作用和使用场景');
                    helpText.removeClass('text-warning').addClass('text-muted');
                }
            });

            // 示例权限点击复制
            $('code').on('click', function() {
                var code = $(this).text();
                $('#Code').val(code);
                toastr.info('已复制权限代码: ' + code);
            });
        });

        // 显示提示消息
        @if (TempData["SuccessMessage"] != null)
        {
            <text>
            toastr.success('@TempData["SuccessMessage"]');
            </text>
        }

        @if (TempData["ErrorMessage"] != null)
        {
            <text>
            toastr.error('@TempData["ErrorMessage"]');
            </text>
        }
    </script>

    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}

<style>
    .create-page-container {
        padding: 20px;
    }

    .create-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #dee2e6;
    }

    .create-title h2 {
        margin: 0;
        color: #495057;
        font-size: 1.5rem;
    }

    .create-title i {
        margin-right: 10px;
        color: #28a745;
    }

    .create-actions .btn {
        margin-left: 10px;
    }

    .form-label.required::after {
        content: " *";
        color: #dc3545;
    }

    .form-actions {
        border-top: 1px solid #dee2e6;
        padding-top: 20px;
        margin-top: 20px;
    }

    .card-title i {
        margin-right: 8px;
        color: #6c757d;
    }

    .form-text {
        font-size: 0.875em;
        margin-top: 0.25rem;
    }

    .text-sm {
        font-size: 0.875rem;
    }

    code {
        cursor: pointer;
        transition: background-color 0.2s;
    }

    code:hover {
        background-color: #e9ecef;
    }

    .was-validated .form-control:valid {
        border-color: #28a745;
    }

    .was-validated .form-control:invalid {
        border-color: #dc3545;
    }
</style>
