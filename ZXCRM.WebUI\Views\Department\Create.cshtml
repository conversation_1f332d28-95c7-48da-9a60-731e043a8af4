@model ZXCRM.WebUI.Models.ViewModels.CreateDepartmentViewModel
@{
    ViewData["Title"] = "新增部门";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">新增部门</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a asp-controller="Dashboard" asp-action="Index">首页</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Department" asp-action="Index">部门管理</a></li>
                    <li class="breadcrumb-item active">新增部门</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-building"></i> 部门信息
                        </h3>
                    </div>
                    <form asp-action="Create" method="post">
                        <div class="card-body">
                            <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>

                            <!-- 部门名称 -->
                            <div class="form-group">
                                <label asp-for="Name" class="form-label required"></label>
                                <input asp-for="Name" class="form-control" placeholder="请输入部门名称" />
                                <span asp-validation-for="Name" class="text-danger"></span>
                                <small class="form-text text-muted">部门名称应该简洁明了，便于识别</small>
                            </div>

                            <!-- 部门编码 -->
                            <div class="form-group">
                                <label asp-for="Code" class="form-label"></label>
                                <input asp-for="Code" class="form-control" placeholder="请输入部门编码（可选）" />
                                <span asp-validation-for="Code" class="text-danger"></span>
                                <small class="form-text text-muted">部门编码用于系统内部标识，可以为空</small>
                            </div>

                            <!-- 上级部门 -->
                            <div class="form-group">
                                <label asp-for="ParentId" class="form-label"></label>
                                <select asp-for="ParentId" class="form-control">
                                    <option value="">-- 选择上级部门（根部门请留空）--</option>
                                    @foreach (var parent in Model.AvailableParents)
                                    {
                                        <option value="@parent.Id">@parent.DisplayName</option>
                                    }
                                </select>
                                <span asp-validation-for="ParentId" class="text-danger"></span>
                                <small class="form-text text-muted">如果不选择上级部门，将创建为根部门</small>
                            </div>

                            <!-- 状态 -->
                            <div class="form-group">
                                <div class="form-check">
                                    <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                                    <label asp-for="IsActive" class="form-check-label"></label>
                                </div>
                                <span asp-validation-for="IsActive" class="text-danger"></span>
                                <small class="form-text text-muted">启用状态的部门可以分配用户</small>
                            </div>
                        </div>

                        <div class="card-footer">
                            <div class="row">
                                <div class="col-md-6">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> 保存
                                    </button>
                                    <a asp-action="Index" class="btn btn-secondary ml-2">
                                        <i class="fas fa-times"></i> 取消
                                    </a>
                                </div>
                                <div class="col-md-6 text-right">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        标有 <span class="text-danger">*</span> 的字段为必填项
                                    </small>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="col-md-4">
                <!-- 帮助信息 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-question-circle"></i> 填写说明
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb"></i> 提示</h6>
                            <ul class="mb-0">
                                <li>部门名称必须唯一，不能重复</li>
                                <li>新建部门默认为启用状态</li>
                                <li>创建后可以为部门分配用户</li>
                            </ul>
                        </div>

                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle"></i> 建议</h6>
                            <p class="mb-0">
                                建议按照公司组织架构创建部门，
                                如：技术部、销售部、财务部等。
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 部门示例 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-list"></i> 常见部门示例
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                技术部
                                <span class="badge bg-primary badge-pill text-white">IT</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                销售部
                                <span class="badge bg-success badge-pill text-white">Sales</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                财务部
                                <span class="badge bg-warning badge-pill text-dark">Finance</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                人事部
                                <span class="badge bg-info badge-pill text-white">HR</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                市场部
                                <span class="badge bg-secondary badge-pill text-white">Marketing</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        // 表单验证增强
        $(document).ready(function() {
            // 部门名称实时验证
            $('#Name').on('blur', function() {
                const name = $(this).val();
                if (name.length > 0 && name.length < 2) {
                    $(this).addClass('is-invalid');
                    $(this).next('.text-danger').text('部门名称长度至少2个字符');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });


        });
    </script>
}

@section Styles {
    <style>
        .required::after {
            content: " *";
            color: red;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .is-invalid {
            border-color: #dc3545;
        }

        .list-group-item {
            border-left: none;
            border-right: none;
        }

        .list-group-item:first-child {
            border-top: none;
        }

        .list-group-item:last-child {
            border-bottom: none;
        }
    </style>
}
