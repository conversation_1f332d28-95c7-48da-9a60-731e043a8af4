using System.ComponentModel.DataAnnotations;

namespace ZXCRM.WebUI.Models.ViewModels
{
    public class DepartmentListViewModel
    {
        public List<DepartmentItemViewModel> Departments { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageIndex { get; set; }
        public int PageSize { get; set; }
        public string SearchTerm { get; set; } = string.Empty;
    }

    public class DepartmentItemViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public int UserCount { get; set; }
        public string Status { get; set; } = "Active";
        public DateTime CreatedAt { get; set; }
    }



    public class CreateDepartmentViewModel
    {
        [Required(ErrorMessage = "部门名称不能为空")]
        [StringLength(100, ErrorMessage = "部门名称长度不能超过100个字符")]
        [Display(Name = "部门名称")]
        public string Name { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "部门编码长度不能超过50个字符")]
        [Display(Name = "部门编码")]
        public string Code { get; set; } = string.Empty;

        [Display(Name = "上级部门")]
        public int? ParentId { get; set; }

        [Display(Name = "是否启用")]
        public bool IsActive { get; set; } = true;

        // 用于下拉列表的部门选项
        public List<DepartmentSelectItem> AvailableParents { get; set; } = new();
    }

    public class EditDepartmentViewModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "部门名称不能为空")]
        [StringLength(100, ErrorMessage = "部门名称长度不能超过100个字符")]
        [Display(Name = "部门名称")]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "是否启用")]
        public bool IsActive { get; set; }
    }

    public class DepartmentDetailViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }

        // 部门统计信息
        public int TotalUsers { get; set; }
        public int ActiveUsers { get; set; }
        public int TotalOrders { get; set; }
        public decimal TotalRevenue { get; set; }

        // 部门用户列表
        public List<DepartmentUserViewModel> Users { get; set; } = new();
    }

    public class DepartmentUserViewModel
    {
        public int Id { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Role { get; set; } = string.Empty;
        public string Status { get; set; } = "Active";
        public DateTime CreatedAt { get; set; }
    }
}
