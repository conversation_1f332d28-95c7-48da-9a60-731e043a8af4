-- 修复admin用户角色为SuperAdmin
-- 检查当前admin用户信息
SELECT Id, Username, Name, Role, Email, DepartmentId 
FROM Users 
WHERE Username = 'admin';

-- 更新admin用户角色为SuperAdmin
UPDATE Users 
SET Role = 'SuperAdmin'
WHERE Username = 'admin';

-- 验证更新结果
SELECT Id, Username, Name, Role, Email, DepartmentId 
FROM Users 
WHERE Username = 'admin';

-- 检查admin用户的权限配置
SELECT up.Id, up.UserId, up.PermissionId, up.ModuleType, p.Name as PermissionName, p.Code as PermissionCode
FROM UserPermissions up
INNER JOIN Permissions p ON up.PermissionId = p.Id
WHERE up.UserId = 1
ORDER BY up.ModuleType, p.Code;

PRINT '=== Admin用户角色已更新为SuperAdmin ===';
