using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ZXCRM.Service.DTOs;
using ZXCRM.Service.Interface;

namespace ZXCRM.WebAPI.Controllers
{
    [Authorize]
    public class OrderController : BaseController
    {
        private readonly IOrderService _orderService;
        private readonly ILogger<OrderController> _logger;

        public OrderController(IOrderService orderService, ILogger<OrderController> logger)
        {
            _orderService = orderService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> GetOrders()
        {
            try
            {
                _logger.LogInformation("=== WebAPI: 开始获取订单列表 ===");
                var orders = await _orderService.GetAllOrdersAsync();

                _logger.LogInformation("WebAPI: 从Service获取到 {Count} 个订单", orders.Count());

                // 记录第一个订单的API层数据
                if (orders.Any())
                {
                    var firstOrder = orders.First();
                    _logger.LogInformation("WebAPI: 第一个订单数据: ID={Id}, Name={Name}, Code={Code}, Amount={Amount}, Currency={Currency}, Status={Status}",
                        firstOrder.Id, firstOrder.Name ?? "NULL", firstOrder.Code ?? "NULL", firstOrder.Amount, firstOrder.Currency ?? "NULL", firstOrder.Status ?? "NULL");
                }

                _logger.LogInformation("=== WebAPI: 订单列表返回成功 ===");
                return Success(orders, "获取订单列表成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "WebAPI: Error getting orders");
                return Failure("获取订单列表失败");
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetOrder(int id)
        {
            try
            {
                _logger.LogInformation("=== WebAPI: 开始获取订单详情 ID={Id} ===", id);
                var order = await _orderService.GetOrderByIdAsync(id);

                if (order == null)
                {
                    _logger.LogWarning("WebAPI: 订单不存在: {OrderId}", id);
                    return Failure($"订单 {id} 不存在");
                }

                _logger.LogInformation("WebAPI: 订单详情数据: ID={Id}, Name={Name}, Code={Code}, Amount={Amount}, Currency={Currency}, Status={Status}",
                    order.Id, order.Name ?? "NULL", order.Code ?? "NULL", order.Amount, order.Currency ?? "NULL", order.Status ?? "NULL");

                _logger.LogInformation("=== WebAPI: 订单详情返回成功 ===");
                return Success(order, "获取订单详情成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "WebAPI: Error getting order by ID: {OrderId}", id);
                return Failure("获取订单详情失败");
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreateOrder([FromBody] CreateOrderDTO createOrderDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Failure("数据验证失败", ModelState);
                }

                _logger.LogInformation("Creating new order for customer: {CustomerName}", createOrderDto.CustomerName);
                var order = await _orderService.CreateOrderAsync(createOrderDto);

                return Success(order, "创建订单成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating order for customer: {CustomerName}", createOrderDto.CustomerName);
                return Failure("创建订单失败");
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateOrder(int id, [FromBody] UpdateOrderDTO updateOrderDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Failure("数据验证失败", ModelState);
                }

                // 设置ID到DTO中
                updateOrderDto.Id = id;

                _logger.LogInformation("Updating order: {OrderId}", id);
                var order = await _orderService.UpdateOrderAsync(updateOrderDto);

                if (order == null)
                {
                    _logger.LogWarning("Order not found for update: {OrderId}", id);
                    return Failure($"订单 {id} 不存在");
                }

                return Success(order, "更新订单成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating order: {OrderId}", id);
                return Failure("更新订单失败");
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteOrder(int id)
        {
            try
            {
                _logger.LogInformation("Deleting order: {OrderId}", id);
                var success = await _orderService.DeleteOrderAsync(id);

                if (!success)
                {
                    _logger.LogWarning("Order not found for deletion: {OrderId}", id);
                    return Failure($"订单 {id} 不存在");
                }

                return Success(null, "删除订单成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting order: {OrderId}", id);
                return Failure("删除订单失败");
            }
        }

        [HttpGet("user/{userId}")]
        public async Task<IActionResult> GetOrdersByUser(int userId)
        {
            try
            {
                _logger.LogInformation("Getting orders by user: {UserId}", userId);
                var orders = await _orderService.GetOrdersByUserIdAsync(userId);
                return Success(orders, "获取用户订单成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting orders by user: {UserId}", userId);
                return Failure("获取用户订单失败");
            }
        }
    }
}
