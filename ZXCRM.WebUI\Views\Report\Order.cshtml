@model ZXCRM.WebUI.Models.ViewModels.OrderReportViewModel
@{
    ViewData["Title"] = "订单统计报表";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">订单统计报表</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a asp-controller="Dashboard" asp-action="Index">首页</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Report" asp-action="Index">报表统计</a></li>
                    <li class="breadcrumb-item active">订单统计</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <!-- 查询条件 -->
        <div class="card collapsed-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-filter"></i> 查询条件
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body" style="display: none;">
                <form method="get" asp-action="Order">
                    <div class="row">
                        <div class="col-md-2">
                            <div class="form-group">
                                <label asp-for="Query.StartDate">开始日期</label>
                                <input asp-for="Query.StartDate" type="date" class="form-control" />
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label asp-for="Query.EndDate">结束日期</label>
                                <input asp-for="Query.EndDate" type="date" class="form-control" />
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label asp-for="Query.DepartmentId">部门</label>
                                <select asp-for="Query.DepartmentId" class="form-control">
                                    <option value="">全部部门</option>
                                    @foreach (var dept in Model.Departments)
                                    {
                                        <option value="@dept.Id">@dept.Name</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label asp-for="Query.UserId">客户经理</label>
                                <select asp-for="Query.UserId" class="form-control">
                                    <option value="">全部用户</option>
                                    @foreach (var user in Model.Users)
                                    {
                                        <option value="@user.Id">@user.Name</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label asp-for="Query.Currency">币种</label>
                                <select asp-for="Query.Currency" class="form-control">
                                    <option value="">全部币种</option>
                                    @foreach (var currency in Model.Currencies)
                                    {
                                        <option value="@currency">@currency</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary btn-block">
                                        <i class="fas fa-search"></i> 查询
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        @if (Model.Report != null)
        {
            <!-- 总体统计 -->
            <div class="row">
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3>@Model.Report.TotalCount</h3>
                            <p>总订单数</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3>@Model.Report.TotalAmount.ToString("N0")</h3>
                            <p>订单总金额</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3>@(Model.Report.TotalCount > 0 ? (Model.Report.TotalAmount / Model.Report.TotalCount).ToString("N0") : "0")</h3>
                            <p>平均订单金额</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-danger">
                        <div class="inner">
                            <h3>@Model.Report.CurrencyStats.Count</h3>
                            <p>涉及币种数</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-coins"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 币种分布和部门业绩 -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-coins"></i> 币种分布统计
                            </h3>
                        </div>
                        <div class="card-body">
                            @if (Model.Report.CurrencyStats.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>币种</th>
                                                <th>订单数</th>
                                                <th>金额</th>
                                                <th>占比</th>
                                                <th>分布</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var stat in Model.Report.CurrencyStats)
                                            {
                                                <tr>
                                                    <td>
                                                        <span class="badge badge-@(stat.Currency == "RMB" ? "success" : stat.Currency == "USD" ? "primary" : "info")">
                                                            @stat.Currency
                                                        </span>
                                                    </td>
                                                    <td><strong>@stat.Count</strong></td>
                                                    <td>@stat.Amount.ToString("N0")</td>
                                                    <td>@stat.Percentage.ToString("F1")%</td>
                                                    <td>
                                                        <div class="progress progress-xs">
                                                            <div class="progress-bar bg-@(stat.Currency == "RMB" ? "success" : stat.Currency == "USD" ? "primary" : "info")" 
                                                                 style="width: @stat.Percentage%"></div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <p class="text-muted">暂无币种统计数据</p>
                            }
                        </div>
                    </div>
                </div>

                <!-- 部门业绩 -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-building"></i> 部门业绩排行
                            </h3>
                        </div>
                        <div class="card-body">
                            @if (Model.Report.DepartmentStats.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>部门</th>
                                                <th>订单数</th>
                                                <th>金额</th>
                                                <th>占比</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var stat in Model.Report.DepartmentStats.Take(10))
                                            {
                                                <tr>
                                                    <td><strong>@stat.DepartmentName</strong></td>
                                                    <td>@stat.Count</td>
                                                    <td>@stat.Amount.ToString("N0")</td>
                                                    <td>
                                                        <span class="badge badge-@(stat.Percentage >= 30 ? "success" : stat.Percentage >= 15 ? "warning" : "secondary")">
                                                            @stat.Percentage.ToString("F1")%
                                                        </span>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <p class="text-muted">暂无部门统计数据</p>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- 用户业绩和月度趋势 -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-users"></i> 客户经理业绩
                            </h3>
                        </div>
                        <div class="card-body">
                            @if (Model.Report.UserStats.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>客户经理</th>
                                                <th>订单数</th>
                                                <th>总金额</th>
                                                <th>平均金额</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var stat in Model.Report.UserStats.Take(10))
                                            {
                                                <tr>
                                                    <td><strong>@stat.UserName</strong></td>
                                                    <td>@stat.Count</td>
                                                    <td>@stat.Amount.ToString("N0")</td>
                                                    <td>
                                                        <span class="badge badge-@(stat.AverageAmount >= 100000 ? "success" : stat.AverageAmount >= 50000 ? "warning" : "secondary")">
                                                            @stat.AverageAmount.ToString("N0")
                                                        </span>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <p class="text-muted">暂无用户统计数据</p>
                            }
                        </div>
                    </div>
                </div>

                <!-- 月度趋势 -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-chart-line"></i> 月度趋势分析
                            </h3>
                        </div>
                        <div class="card-body">
                            @if (Model.Report.MonthlyStats.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>月份</th>
                                                <th>订单数</th>
                                                <th>金额</th>
                                                <th>平均金额</th>
                                                <th>趋势</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var stat in Model.Report.MonthlyStats.OrderBy(s => s.Month))
                                            {
                                                <tr>
                                                    <td><strong>@stat.Month</strong></td>
                                                    <td>@stat.Count</td>
                                                    <td>@stat.Amount.ToString("N0")</td>
                                                    <td>@stat.AverageAmount.ToString("N0")</td>
                                                    <td>
                                                        <div class="progress progress-xs">
                                                            <div class="progress-bar bg-primary" style="width: @(stat.Count * 5)%"></div>
                                                        </div>
                                                        <span class="badge badge-info">@stat.Count 单</span>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <p class="text-muted">暂无月度统计数据</p>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
        else
        {
            <div class="alert alert-warning">
                <h4><i class="icon fas fa-exclamation-triangle"></i> 提示</h4>
                暂无订单统计数据，请检查查询条件或联系管理员。
            </div>
        }

        <!-- 快捷操作 -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-bolt"></i> 快捷操作
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="btn-group" role="group">
                            <a asp-controller="Order" asp-action="Index" class="btn btn-success">
                                <i class="fas fa-shopping-cart"></i> 订单管理
                            </a>
                            <a asp-controller="Order" asp-action="Create" class="btn btn-primary">
                                <i class="fas fa-plus"></i> 新增订单
                            </a>
                            <a asp-action="Dashboard" class="btn btn-info">
                                <i class="fas fa-tachometer-alt"></i> 综合仪表盘
                            </a>
                            <a asp-controller="Report" asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> 返回报表首页
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        $(document).ready(function() {
            // 显示提示消息
            @if (TempData["SuccessMessage"] != null)
            {
                <text>
                toastr.success('@TempData["SuccessMessage"]');
                </text>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <text>
                toastr.error('@TempData["ErrorMessage"]');
                </text>
            }
        });
    </script>
}
