@model ZXCRM.WebUI.Models.ViewModels.InvoiceDetailViewModel
@{
    ViewData["Title"] = "发票详情";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">发票详情</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a asp-controller="Dashboard" asp-action="Index">首页</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Invoice" asp-action="Index">发票管理</a></li>
                    <li class="breadcrumb-item active">发票详情</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <!-- 基本信息 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-file-invoice"></i> 发票基本信息
                        </h3>
                        <div class="card-tools">
                            @if (Model.Status != "Cancelled")
                            {
                                <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning btn-sm">
                                    <i class="fas fa-edit"></i> 编辑
                                </a>
                            }
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">发票代号:</dt>
                                    <dd class="col-sm-8">
                                        @if (!string.IsNullOrEmpty(Model.Code))
                                        {
                                            <code>@Model.Code</code>
                                        }
                                        else
                                        {
                                            <span class="text-muted">未设置</span>
                                        }
                                    </dd>
                                    
                                    <dt class="col-sm-4">发票公司:</dt>
                                    <dd class="col-sm-8">
                                        <span class="badge badge-primary badge-lg">@Model.Company</span>
                                    </dd>
                                    
                                    <dt class="col-sm-4">发票类型:</dt>
                                    <dd class="col-sm-8">
                                        @switch (Model.Type)
                                        {
                                            case "增值税专用发票":
                                                <span class="badge badge-success badge-lg">@Model.Type</span>
                                                break;
                                            case "增值税普通发票":
                                                <span class="badge badge-info badge-lg">@Model.Type</span>
                                                break;
                                            case "收据":
                                                <span class="badge badge-warning badge-lg">@Model.Type</span>
                                                break;
                                            default:
                                                <span class="badge badge-secondary badge-lg">@Model.Type</span>
                                                break;
                                        }
                                    </dd>
                                    
                                    <dt class="col-sm-4">发票税率:</dt>
                                    <dd class="col-sm-8">
                                        @if (Model.TaxRate == 0)
                                        {
                                            <span class="badge badge-light badge-lg">免税</span>
                                        }
                                        else
                                        {
                                            <span class="badge badge-secondary badge-lg">@(Model.TaxRate.ToString("0.##"))%</span>
                                        }
                                    </dd>
                                    
                                    <dt class="col-sm-4">发票金额:</dt>
                                    <dd class="col-sm-8">
                                        <span class="badge badge-success badge-lg">
                                            @Model.Amount.ToString("N2")
                                        </span>
                                    </dd>
                                </dl>
                            </div>
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">发票状态:</dt>
                                    <dd class="col-sm-8">
                                        @switch (Model.Status)
                                        {
                                            case "Normal":
                                                <span class="badge badge-success badge-lg">
                                                    <i class="fas fa-check"></i> 正常
                                                </span>
                                                break;
                                            case "Cancelled":
                                                <span class="badge badge-danger badge-lg">
                                                    <i class="fas fa-times"></i> 已作废
                                                </span>
                                                break;
                                            default:
                                                <span class="badge badge-secondary badge-lg">@Model.Status</span>
                                                break;
                                        }
                                    </dd>
                                    
                                    <dt class="col-sm-4">发票内容:</dt>
                                    <dd class="col-sm-8">
                                        @if (!string.IsNullOrEmpty(Model.Content))
                                        {
                                            @Model.Content
                                        }
                                        else
                                        {
                                            <span class="text-muted">未设置</span>
                                        }
                                    </dd>
                                    
                                    <dt class="col-sm-4">关联款项:</dt>
                                    <dd class="col-sm-8">
                                        <a asp-controller="Payment" asp-action="Details" asp-route-id="@Model.PaymentId" class="text-decoration-none">
                                            <strong>@Model.PaymentName</strong>
                                        </a>
                                        <br><small class="text-muted">@Model.PaymentCode</small>
                                    </dd>
                                    
                                    <dt class="col-sm-4">关联订单:</dt>
                                    <dd class="col-sm-8">
                                        <strong>@Model.OrderName</strong>
                                        <br><small class="text-info">@Model.CustomerName</small>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 收件人信息 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-user"></i> 收件人信息
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">收件人:</dt>
                                    <dd class="col-sm-8">
                                        @if (!string.IsNullOrEmpty(Model.ReceiverName))
                                        {
                                            @Model.ReceiverName
                                        }
                                        else
                                        {
                                            <span class="text-muted">未设置</span>
                                        }
                                    </dd>
                                    
                                    <dt class="col-sm-4">收件人电话:</dt>
                                    <dd class="col-sm-8">
                                        @if (!string.IsNullOrEmpty(Model.ReceiverPhone))
                                        {
                                            @Model.ReceiverPhone
                                        }
                                        else
                                        {
                                            <span class="text-muted">未设置</span>
                                        }
                                    </dd>
                                    
                                    <dt class="col-sm-4">申请人电话:</dt>
                                    <dd class="col-sm-8">
                                        @if (!string.IsNullOrEmpty(Model.ApplicantPhone))
                                        {
                                            @Model.ApplicantPhone
                                        }
                                        else
                                        {
                                            <span class="text-muted">未设置</span>
                                        }
                                    </dd>
                                </dl>
                            </div>
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">邮寄地址:</dt>
                                    <dd class="col-sm-8">
                                        @if (!string.IsNullOrEmpty(Model.MailingAddress))
                                        {
                                            @Model.MailingAddress
                                        }
                                        else
                                        {
                                            <span class="text-muted">未设置</span>
                                        }
                                    </dd>
                                    
                                    <dt class="col-sm-4">客户邮箱:</dt>
                                    <dd class="col-sm-8">
                                        @if (!string.IsNullOrEmpty(Model.CustomerEmail))
                                        {
                                            <a href="mailto:@Model.CustomerEmail">@Model.CustomerEmail</a>
                                        }
                                        else
                                        {
                                            <span class="text-muted">未设置</span>
                                        }
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统信息 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-cog"></i> 系统信息
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">创建人:</dt>
                                    <dd class="col-sm-8">
                                        <span class="badge badge-primary">@Model.CreatedByName</span>
                                    </dd>
                                    
                                    <dt class="col-sm-4">创建时间:</dt>
                                    <dd class="col-sm-8">@Model.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss")</dd>
                                </dl>
                            </div>
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">最后更新:</dt>
                                    <dd class="col-sm-8">@Model.UpdatedAt.ToString("yyyy-MM-dd HH:mm:ss")</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <!-- 操作面板 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-tools"></i> 操作
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="btn-group-vertical btn-block">
                            @if (Model.Status != "Cancelled")
                            {
                                <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning">
                                    <i class="fas fa-edit"></i> 编辑发票
                                </a>
                                <button type="button" class="btn btn-danger" onclick="confirmDelete(@Model.Id, '@Model.Code')">
                                    <i class="fas fa-trash"></i> 删除发票
                                </button>
                                <div class="dropdown-divider"></div>
                                <button type="button" class="btn btn-secondary" onclick="markAsCancelled()">
                                    <i class="fas fa-ban"></i> 标记作废
                                </button>
                            }
                            else
                            {
                                <button type="button" class="btn btn-secondary" disabled>
                                    <i class="fas fa-ban"></i> 已作废
                                </button>
                            }
                            <div class="dropdown-divider"></div>
                            <button type="button" class="btn btn-info">
                                <i class="fas fa-print"></i> 打印发票
                            </button>
                            <button type="button" class="btn btn-success">
                                <i class="fas fa-download"></i> 导出PDF
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 发票统计 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-pie"></i> 发票统计
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="info-box">
                            <span class="info-box-icon bg-success"><i class="fas fa-file-invoice"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">发票金额</span>
                                <span class="info-box-number">@Model.Amount.ToString("N2")</span>
                            </div>
                        </div>
                        
                        @if (Model.TaxRate > 0)
                        {
                            var taxAmount = Model.Amount * Model.TaxRate / 100;
                            var netAmount = Model.Amount - taxAmount;
                            
                            <div class="info-box">
                                <span class="info-box-icon bg-info"><i class="fas fa-calculator"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">税额</span>
                                    <span class="info-box-number">@taxAmount.ToString("N2")</span>
                                </div>
                            </div>
                            
                            <div class="info-box">
                                <span class="info-box-icon bg-warning"><i class="fas fa-money-bill"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">不含税金额</span>
                                    <span class="info-box-number">@netAmount.ToString("N2")</span>
                                </div>
                            </div>
                        }
                    </div>
                </div>

                <!-- 快捷导航 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-compass"></i> 快捷导航
                        </h3>
                    </div>
                    <div class="card-body">
                        <a asp-action="Index" class="btn btn-outline-primary btn-block">
                            <i class="fas fa-list"></i> 返回发票列表
                        </a>
                        <a asp-action="Create" class="btn btn-outline-success btn-block">
                            <i class="fas fa-plus"></i> 新增发票
                        </a>
                        <a asp-controller="Payment" asp-action="Details" asp-route-id="@Model.PaymentId" class="btn btn-outline-info btn-block">
                            <i class="fas fa-money-bill-wave"></i> 查看关联款项
                        </a>
                        <a asp-action="Create" asp-route-paymentId="@Model.PaymentId" class="btn btn-outline-warning btn-block">
                            <i class="fas fa-copy"></i> 为此款项新增发票
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>您确定要删除发票 <strong id="deleteInvoiceCode"></strong> 吗？</p>
                <p class="text-danger"><small>删除发票将会影响相关款项的开票状态！</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(invoiceId, invoiceCode) {
            $('#deleteInvoiceCode').text(invoiceCode || '未设置代号');
            $('#deleteForm').attr('action', '@Url.Action("Delete")/' + invoiceId);
            $('#deleteModal').modal('show');
        }

        function markAsCancelled() {
            if (confirm('确定要标记此发票为作废吗？')) {
                // 这里可以添加AJAX调用来更新发票状态
                toastr.info('功能开发中...');
            }
        }
    </script>
}

@section Styles {
    <style>
        .badge-lg {
            font-size: 0.9em;
            padding: 0.5em 0.75em;
        }
        
        .info-box {
            margin-bottom: 1rem;
        }
    </style>
}
