@model ZXCRM.WebUI.Models.ViewModels.OrderListViewModel
@using ZXCRM.WebUI.Helpers
@{
    ViewData["Title"] = "订单显示调试";
}

<div class="container-fluid">
    <h2>订单显示调试页面</h2>
    
    @if (ViewBag.Error != null)
    {
        <div class="alert alert-danger">
            <h4>错误信息</h4>
            <p>@ViewBag.Error</p>
        </div>
    }
    else
    {
        <!-- 基本信息 -->
        <div class="card mb-3">
            <div class="card-header">
                <h3>基本信息</h3>
            </div>
            <div class="card-body">
                <p><strong>订单数量:</strong> @(Model.Orders?.Count ?? 0)</p>
                <p><strong>总数量:</strong> @Model.TotalCount</p>
                <p><strong>页面索引:</strong> @Model.PageIndex</p>
                <p><strong>页面大小:</strong> @Model.PageSize</p>
            </div>
        </div>

        @if (Model.Orders != null && Model.Orders.Any())
        {
            <!-- 详细数据表格 -->
            <div class="card">
                <div class="card-header">
                    <h3>订单详细数据</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-sm">
                            <thead class="thead-light">
                                <tr>
                                    <th>ID</th>
                                    <th>订单名称</th>
                                    <th>订单编号</th>
                                    <th>客户名称</th>
                                    <th>原始金额</th>
                                    <th>原始币种</th>
                                    <th>格式化金额</th>
                                    <th>原始状态</th>
                                    <th>格式化状态</th>
                                    <th>状态颜色</th>
                                    <th>客户经理</th>
                                    <th>部门</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var order in Model.Orders)
                                {
                                    <tr>
                                        <td>@order.Id</td>
                                        <td>@order.Name</td>
                                        <td>@order.Code</td>
                                        <td>@order.CustomerName</td>
                                        <td style="background-color: #fff3cd;">
                                            <strong>@order.Amount</strong>
                                        </td>
                                        <td style="background-color: #fff3cd;">
                                            <strong>"@order.Currency"</strong>
                                        </td>
                                        <td style="background-color: #d4edda;">
                                            <strong>@EnumHelper.FormatAmount(order.Amount, order.Currency)</strong>
                                        </td>
                                        <td style="background-color: #fff3cd;">
                                            <strong>"@order.Status"</strong>
                                        </td>
                                        <td style="background-color: #d4edda;">
                                            <strong>@EnumHelper.GetOrderStatusText(order.Status)</strong>
                                        </td>
                                        <td style="background-color: #d4edda;">
                                            <span class="badge <EMAIL>(order.Status)">
                                                @EnumHelper.GetOrderStatusColor(order.Status)
                                            </span>
                                        </td>
                                        <td>@order.AccountManagerName</td>
                                        <td>@order.DepartmentName</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- EnumHelper测试 -->
            <div class="card mt-3">
                <div class="card-header">
                    <h3>EnumHelper方法测试</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>金额格式化测试:</h5>
                            <ul>
                                <li>FormatAmount(100000, "CNY"): <strong>@EnumHelper.FormatAmount(100000, "CNY")</strong></li>
                                <li>FormatAmount(100000, "USD"): <strong>@EnumHelper.FormatAmount(100000, "USD")</strong></li>
                                <li>FormatAmount(0, "CNY"): <strong>@EnumHelper.FormatAmount(0, "CNY")</strong></li>
                                <li>FormatAmount(100000, null): <strong>@EnumHelper.FormatAmount(100000, null)</strong></li>
                                <li>FormatAmount(100000, ""): <strong>@EnumHelper.FormatAmount(100000, "")</strong></li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>状态格式化测试:</h5>
                            <ul>
                                <li>GetOrderStatusText("New"): <strong>@EnumHelper.GetOrderStatusText("New")</strong></li>
                                <li>GetOrderStatusText("InProgress"): <strong>@EnumHelper.GetOrderStatusText("InProgress")</strong></li>
                                <li>GetOrderStatusText("Completed"): <strong>@EnumHelper.GetOrderStatusText("Completed")</strong></li>
                                <li>GetOrderStatusText("Cancelled"): <strong>@EnumHelper.GetOrderStatusText("Cancelled")</strong></li>
                                <li>GetOrderStatusText(""): <strong>@EnumHelper.GetOrderStatusText("")</strong></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 实际显示效果测试 -->
            <div class="card mt-3">
                <div class="card-header">
                    <h3>实际显示效果测试（模拟列表页面）</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>订单信息</th>
                                    <th>客户</th>
                                    <th>金额</th>
                                    <th>状态</th>
                                    <th>经理</th>
                                    <th>部门</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var order in Model.Orders)
                                {
                                    <tr>
                                        <td>
                                            <div class="text-truncate" style="max-width: 160px;" title="@order.Name">
                                                <strong>@order.Name</strong>
                                            </div>
                                            <small class="text-muted">@order.Code</small>
                                        </td>
                                        <td class="text-nowrap">@order.CustomerName</td>
                                        <td>
                                            <span class="badge badge-success badge-xs">
                                                @EnumHelper.FormatAmount(order.Amount, order.Currency)
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge <EMAIL>(order.Status) badge-xs">
                                                @if (order.Status == "New")
                                                {
                                                    <i class="fas fa-plus"></i>
                                                }
                                                else if (order.Status == "InProgress")
                                                {
                                                    <i class="fas fa-play"></i>
                                                }
                                                else if (order.Status == "Completed")
                                                {
                                                    <i class="fas fa-check-circle"></i>
                                                }
                                                else if (order.Status == "Cancelled")
                                                {
                                                    <i class="fas fa-times"></i>
                                                }
                                                else
                                                {
                                                    <i class="fas fa-question"></i>
                                                }
                                                @EnumHelper.GetOrderStatusText(order.Status)
                                            </span>
                                        </td>
                                        <td class="text-nowrap">@order.AccountManagerName</td>
                                        <td class="text-nowrap">@order.DepartmentName</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        }
        else
        {
            <div class="alert alert-warning">
                <h4>没有订单数据</h4>
                <p>Model.Orders 为空或null</p>
            </div>
        }
    }
</div>

<div class="mt-3">
    <a href="/Order" class="btn btn-primary">返回订单列表</a>
    <a href="/Debug/SimpleDataTest" class="btn btn-info">简单数据测试</a>
    <a href="/Debug/CheckDatabaseRaw" class="btn btn-success">检查数据库</a>
</div>
