{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ApiSettings": {"BaseUrl": "http://localhost:5000/", "Timeout": 30, "RetryCount": 3, "RetryDelay": 1000}, "Authentication": {"CookieName": "ZXCRM.Auth", "ExpireTimeSpan": "08:00:00", "SlidingExpiration": true, "RequireHttps": false}, "Cache": {"DefaultExpiration": "00:15:00", "SlidingExpiration": "00:05:00"}, "Performance": {"EnableResponseCompression": true, "EnableResponseCaching": true, "EnableStaticFileCompression": true}}