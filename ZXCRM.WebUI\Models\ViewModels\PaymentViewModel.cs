using System.ComponentModel.DataAnnotations;

namespace ZXCRM.WebUI.Models.ViewModels
{
    public class PaymentListViewModel
    {
        public List<PaymentItemViewModel> Payments { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageIndex { get; set; }
        public int PageSize { get; set; }
        public string SearchTerm { get; set; } = string.Empty;
        public string PaymentType { get; set; } = string.Empty;
        public string InvoiceStatus { get; set; } = string.Empty;
        public int? OrderId { get; set; }
    }

    public class PaymentItemViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public int OrderId { get; set; }
        public string OrderName { get; set; } = string.Empty;
        public string OrderCode { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string PaymentType { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public decimal SettlementAmount { get; set; }
        public DateTime? ExpectedPaymentDate { get; set; }
        public DateTime? ActualPaymentDate { get; set; }
        public string PaymentStatus { get; set; } = "Pending";
        public string InvoiceStatus { get; set; } = string.Empty;
        public DateTime? InvoiceDate { get; set; }
        public string AccountManagerName { get; set; } = string.Empty;
        public string ProjectManagerName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
    }

    public class CreatePaymentViewModel
    {
        [Required(ErrorMessage = "款项名称不能为空")]
        [StringLength(200, ErrorMessage = "款项名称长度不能超过200个字符")]
        [Display(Name = "款项名称")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "请选择关联订单")]
        [Display(Name = "关联订单")]
        public int OrderId { get; set; }

        [Required(ErrorMessage = "请选择款项类型")]
        [Display(Name = "款项类型")]
        public string PaymentType { get; set; } = string.Empty;

        [Required(ErrorMessage = "款项金额不能为空")]
        [Range(0.01, double.MaxValue, ErrorMessage = "款项金额必须大于0")]
        [Display(Name = "款项金额")]
        public decimal Amount { get; set; }

        [Required(ErrorMessage = "币种不能为空")]
        [Display(Name = "币种")]
        public string Currency { get; set; } = "CNY";

        [Required(ErrorMessage = "结算金额不能为空")]
        [Range(0.01, double.MaxValue, ErrorMessage = "结算金额必须大于0")]
        [Display(Name = "结算金额")]
        public decimal SettlementAmount { get; set; }

        [Display(Name = "预期回款日期")]
        public DateTime? ExpectedPaymentDate { get; set; }

        [Display(Name = "实际回款日期")]
        public DateTime? ActualPaymentDate { get; set; }

        [Required(ErrorMessage = "请选择开票状态")]
        [Display(Name = "开票状态")]
        public string InvoiceStatus { get; set; } = "未开票";

        [Display(Name = "开票日期")]
        public DateTime? InvoiceDate { get; set; }

        // 用于下拉列表
        public List<OrderSelectItem> Orders { get; set; } = new();
    }

    public class EditPaymentViewModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "款项名称不能为空")]
        [StringLength(200, ErrorMessage = "款项名称长度不能超过200个字符")]
        [Display(Name = "款项名称")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "款项编号不能为空")]
        [StringLength(50, ErrorMessage = "款项编号长度不能超过50个字符")]
        [Display(Name = "款项编号")]
        public string Code { get; set; } = string.Empty;

        [Required(ErrorMessage = "请选择关联订单")]
        [Display(Name = "关联订单")]
        public int OrderId { get; set; }

        [Required(ErrorMessage = "请选择款项类型")]
        [Display(Name = "款项类型")]
        public string PaymentType { get; set; } = string.Empty;

        [Required(ErrorMessage = "款项金额不能为空")]
        [Range(0.01, double.MaxValue, ErrorMessage = "款项金额必须大于0")]
        [Display(Name = "款项金额")]
        public decimal Amount { get; set; }

        [Required(ErrorMessage = "币种不能为空")]
        [Display(Name = "币种")]
        public string Currency { get; set; } = "CNY";

        [Required(ErrorMessage = "结算金额不能为空")]
        [Range(0.01, double.MaxValue, ErrorMessage = "结算金额必须大于0")]
        [Display(Name = "结算金额")]
        public decimal SettlementAmount { get; set; }

        [Display(Name = "预期回款日期")]
        public DateTime? ExpectedPaymentDate { get; set; }

        [Display(Name = "实际回款日期")]
        public DateTime? ActualPaymentDate { get; set; }

        [Required(ErrorMessage = "请选择开票状态")]
        [Display(Name = "开票状态")]
        public string InvoiceStatus { get; set; } = string.Empty;

        [Display(Name = "开票日期")]
        public DateTime? InvoiceDate { get; set; }

        // 用于下拉列表
        public List<OrderSelectItem> Orders { get; set; } = new();
    }

    public class PaymentDetailViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public int OrderId { get; set; }
        public string OrderName { get; set; } = string.Empty;
        public string OrderCode { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string PaymentType { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public decimal SettlementAmount { get; set; }
        public DateTime? ExpectedPaymentDate { get; set; }
        public DateTime? ActualPaymentDate { get; set; }
        public string PaymentStatus { get; set; } = "Pending";
        public string InvoiceStatus { get; set; } = string.Empty;
        public DateTime? InvoiceDate { get; set; }
        public string AccountManagerName { get; set; } = string.Empty;
        public string ProjectManagerName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class OrderSelectItem
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Currency { get; set; } = string.Empty;
    }
}
