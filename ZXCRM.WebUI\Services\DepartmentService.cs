using ZXCRM.Service.DTOs;
using ZXCRM.WebUI.Models;

namespace ZXCRM.WebUI.Services
{
    public class DepartmentService : IDepartmentService
    {
        private readonly IApiService _apiService;
        private readonly ILogger<DepartmentService> _logger;

        public DepartmentService(IApiService apiService, ILogger<DepartmentService> logger)
        {
            _apiService = apiService;
            _logger = logger;
        }

        public async Task<ApiResponse<List<DepartmentDTO>>> GetDepartmentsAsync()
        {
            try
            {
                _logger.LogInformation("Getting departments list");

                var response = await _apiService.GetAsync<List<DepartmentDTO>>("api/department");

                if (response.Success)
                {
                    _logger.LogInformation("Successfully retrieved {Count} departments", response.Data?.Count ?? 0);
                }
                else
                {
                    _logger.LogWarning("Failed to retrieve departments: {Message}", response.Message);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting departments list");
                return new ApiResponse<List<DepartmentDTO>>
                {
                    Success = false,
                    Message = $"Failed to get departments: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<List<DepartmentDTO>>> GetDepartmentTreeAsync()
        {
            try
            {
                _logger.LogInformation("Getting department tree structure");

                var response = await _apiService.GetAsync<List<DepartmentDTO>>("api/department/tree");

                if (response.Success)
                {
                    _logger.LogInformation("Successfully retrieved department tree with {Count} root departments",
                        response.Data?.Count ?? 0);
                }
                else
                {
                    _logger.LogWarning("Failed to retrieve department tree: {Message}", response.Message);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting department tree");
                return new ApiResponse<List<DepartmentDTO>>
                {
                    Success = false,
                    Message = $"Failed to get department tree: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<DepartmentDTO>> GetDepartmentByIdAsync(int id)
        {
            try
            {
                _logger.LogInformation("Getting department by ID: {DepartmentId}", id);

                var response = await _apiService.GetAsync<DepartmentDTO>($"api/department/{id}");

                if (response.Success)
                {
                    _logger.LogInformation("Successfully retrieved department: {DepartmentId}", id);
                }
                else
                {
                    _logger.LogWarning("Failed to retrieve department {DepartmentId}: {Message}", id, response.Message);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting department by ID: {DepartmentId}", id);
                return new ApiResponse<DepartmentDTO>
                {
                    Success = false,
                    Message = $"Failed to get department: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<DepartmentDTO>> CreateDepartmentAsync(CreateDepartmentDTO request)
        {
            try
            {
                _logger.LogInformation("Creating new department: {DepartmentName}", request.Name);

                var response = await _apiService.PostAsync<DepartmentDTO>("api/department", request);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully created department: {DepartmentName}", request.Name);
                }
                else
                {
                    _logger.LogWarning("Failed to create department {DepartmentName}: {Message}", request.Name, response.Message);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating department: {DepartmentName}", request.Name);
                return new ApiResponse<DepartmentDTO>
                {
                    Success = false,
                    Message = $"Failed to create department: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<DepartmentDTO>> UpdateDepartmentAsync(int id, UpdateDepartmentDTO request)
        {
            try
            {
                _logger.LogInformation("Updating department: {DepartmentId}", id);

                var response = await _apiService.PutAsync<DepartmentDTO>($"api/department/{id}", request);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully updated department: {DepartmentId}", id);
                }
                else
                {
                    _logger.LogWarning("Failed to update department {DepartmentId}: {Message}", id, response.Message);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating department: {DepartmentId}", id);
                return new ApiResponse<DepartmentDTO>
                {
                    Success = false,
                    Message = $"Failed to update department: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<object>> DeleteDepartmentAsync(int id)
        {
            try
            {
                _logger.LogInformation("Deleting department: {DepartmentId}", id);

                var response = await _apiService.DeleteAsync<object>($"api/department/{id}");

                if (response.Success)
                {
                    _logger.LogInformation("Successfully deleted department: {DepartmentId}", id);
                }
                else
                {
                    _logger.LogWarning("Failed to delete department {DepartmentId}: {Message}", id, response.Message);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting department: {DepartmentId}", id);
                return new ApiResponse<object>
                {
                    Success = false,
                    Message = $"Failed to delete department: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<List<UserDTO>>> GetDepartmentUsersAsync(int departmentId)
        {
            try
            {
                _logger.LogInformation("Getting users for department: {DepartmentId}", departmentId);

                var response = await _apiService.GetAsync<List<UserDTO>>($"api/department/{departmentId}/users");

                if (response.Success)
                {
                    _logger.LogInformation("Successfully retrieved {Count} users for department: {DepartmentId}",
                        response.Data?.Count ?? 0, departmentId);
                }
                else
                {
                    _logger.LogWarning("Failed to retrieve users for department {DepartmentId}: {Message}",
                        departmentId, response.Message);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting users for department: {DepartmentId}", departmentId);
                return new ApiResponse<List<UserDTO>>
                {
                    Success = false,
                    Message = $"Failed to get department users: {ex.Message}"
                };
            }
        }
    }
}
