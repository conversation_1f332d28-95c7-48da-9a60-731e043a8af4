using System;

namespace ZXCRM.Service.DTOs
{
    public class OrderDTO
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string? ContactName { get; set; }
        public string? ContactPhone { get; set; }
        public int? OpportunityId { get; set; }
        public string? OpportunityName { get; set; }
        public decimal Amount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public decimal SettlementAmount { get; set; }
        public int AccountManagerId { get; set; }
        public string AccountManagerName { get; set; } = string.Empty;
        public int DepartmentId { get; set; }
        public string DepartmentName { get; set; } = string.Empty;
        public DateTime SignDate { get; set; }
        public string Status { get; set; } = string.Empty;
        public int? ProjectManagerId { get; set; }
        public string? ProjectManagerName { get; set; }
        public int? PerformanceDepartmentId { get; set; }
        public string? PerformanceDepartmentName { get; set; }
        public int CreatedById { get; set; }
        public string CreatedByName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }
}
