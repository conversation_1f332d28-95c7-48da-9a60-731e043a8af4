using Microsoft.AspNetCore.Mvc;
using ZXCRM.WebUI.Models.ViewModels;
using ZXCRM.WebUI.Services;

namespace ZXCRM.WebUI.Controllers
{
    public class DashboardController : BaseController
    {
        private readonly IDashboardService _dashboardService;
        private readonly ILogger<DashboardController> _logger;

        public DashboardController(IDashboardService dashboardService, ILogger<DashboardController> logger)
        {
            _dashboardService = dashboardService;
            _logger = logger;
        }

        public async Task<IActionResult> Index()
        {
            try
            {
                _logger.LogInformation("Loading dashboard for user: {Username}", GetCurrentUserName());

                var viewModel = new DashboardViewModel();

                // 获取仪表盘摘要数据
                var summaryResponse = await _dashboardService.GetDashboardSummaryAsync();
                if (summaryResponse.Success && summaryResponse.Data != null)
                {
                    viewModel.Summary = summaryResponse.Data;
                }
                else
                {
                    _logger.LogWarning("Failed to load dashboard summary: {Message}", summaryResponse.Message);
                    
                    // 使用模拟数据作为后备
                    viewModel.Summary = new DashboardSummaryViewModel
                    {
                        TotalOrders = 128,
                        TotalRevenue = 1256789.45m,
                        TotalCustomers = 89,
                        PendingOrders = 23,
                        CompletedOrders = 105,
                        TotalUsers = 24,
                        TotalInvoices = 98,
                        TotalPayments = 1156789.45m
                    };
                }

                // 获取最近活动数据
                var activitiesResponse = await _dashboardService.GetRecentActivitiesAsync();
                if (activitiesResponse.Success && activitiesResponse.Data != null)
                {
                    viewModel.RecentActivities = activitiesResponse.Data;
                }
                else
                {
                    _logger.LogWarning("Failed to load recent activities: {Message}", activitiesResponse.Message);
                    
                    // 使用模拟数据作为后备
                    viewModel.RecentActivities = new List<RecentActivityViewModel>
                    {
                        new RecentActivityViewModel
                        {
                            Id = 1,
                            Type = "Order",
                            Description = "新订单 #ORD-001 已创建",
                            CreatedAt = DateTime.Now.AddMinutes(-10),
                            UserName = "张三",
                            Icon = "fas fa-shopping-cart",
                            Color = "success"
                        },
                        new RecentActivityViewModel
                        {
                            Id = 2,
                            Type = "Payment",
                            Description = "收到付款 ¥5,000.00",
                            CreatedAt = DateTime.Now.AddMinutes(-25),
                            UserName = "李四",
                            Icon = "fas fa-money-bill",
                            Color = "info"
                        },
                        new RecentActivityViewModel
                        {
                            Id = 3,
                            Type = "User",
                            Description = "新用户 王五 已注册",
                            CreatedAt = DateTime.Now.AddHours(-1),
                            UserName = "管理员",
                            Icon = "fas fa-user-plus",
                            Color = "primary"
                        }
                    };
                }

                // 获取图表数据
                var chartResponse = await _dashboardService.GetChartDataAsync("monthly-revenue");
                if (chartResponse.Success && chartResponse.Data != null)
                {
                    viewModel.ChartData = chartResponse.Data;
                }
                else
                {
                    _logger.LogWarning("Failed to load chart data: {Message}", chartResponse.Message);
                    
                    // 使用模拟数据作为后备
                    viewModel.ChartData = new ChartDataViewModel
                    {
                        Labels = new List<string> { "1月", "2月", "3月", "4月", "5月", "6月" },
                        Data = new List<decimal> { 65000, 78000, 82000, 91000, 87000, 95000 },
                        ChartType = "line",
                        Title = "月度收入趋势"
                    };
                }

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading dashboard for user: {Username}", GetCurrentUserName());
                SetErrorMessage("加载仪表盘数据时发生错误");
                
                // 返回带有基本模拟数据的视图
                var fallbackViewModel = new DashboardViewModel
                {
                    Summary = new DashboardSummaryViewModel
                    {
                        TotalOrders = 0,
                        TotalRevenue = 0,
                        TotalCustomers = 0,
                        PendingOrders = 0,
                        CompletedOrders = 0,
                        TotalUsers = 0,
                        TotalInvoices = 0,
                        TotalPayments = 0
                    },
                    RecentActivities = new List<RecentActivityViewModel>(),
                    ChartData = new ChartDataViewModel()
                };
                
                return View(fallbackViewModel);
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetChartData(string chartType)
        {
            try
            {
                var response = await _dashboardService.GetChartDataAsync(chartType);
                
                if (response.Success)
                {
                    return Json(response.Data);
                }
                else
                {
                    return Json(new { success = false, message = response.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting chart data for type: {ChartType}", chartType);
                return Json(new { success = false, message = "获取图表数据失败" });
            }
        }
    }
}
