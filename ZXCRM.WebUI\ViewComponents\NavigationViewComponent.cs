using Microsoft.AspNetCore.Mvc;
using ZXCRM.WebUI.Services;

namespace ZXCRM.WebUI.ViewComponents
{
    public class NavigationViewComponent : ViewComponent
    {
        private readonly INavigationPermissionService _navigationPermissionService;
        private readonly ILogger<NavigationViewComponent> _logger;

        public NavigationViewComponent(
            INavigationPermissionService navigationPermissionService,
            ILogger<NavigationViewComponent> logger)
        {
            _navigationPermissionService = navigationPermissionService;
            _logger = logger;
        }

        public async Task<IViewComponentResult> InvokeAsync()
        {
            try
            {
                // 获取当前控制器和动作
                var currentController = ViewContext.RouteData.Values["controller"]?.ToString() ?? "";
                var currentAction = ViewContext.RouteData.Values["action"]?.ToString() ?? "";

                _logger.LogInformation("Generating navigation for controller: {Controller}, action: {Action}", currentController, currentAction);

                // 获取用户导航权限
                var userNavigation = await _navigationPermissionService.GetUserNavigationAsync(
                    HttpContext.User, 
                    currentController, 
                    currentAction);

                return View(userNavigation);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in NavigationViewComponent");
                
                // 返回空的导航（只有仪表盘）
                return View(new ZXCRM.WebUI.Models.UserNavigationViewModel
                {
                    VisibleNavigationItems = ZXCRM.WebUI.Models.NavigationPermissionManager.GetAllNavigationItems()
                        .Where(item => item.IsAlwaysVisible)
                        .ToList()
                });
            }
        }
    }
}
