using ZXCRM.Service.DTOs;
using ZXCRM.WebUI.Models;

namespace ZXCRM.WebUI.Services
{
    public interface IPaymentService
    {
        Task<ApiResponse<List<PaymentDTO>>> GetPaymentsAsync();
        Task<ApiResponse<PaymentDTO>> GetPaymentByIdAsync(int id);
        Task<ApiResponse<PaymentDTO>> CreatePaymentAsync(CreatePaymentDTO request);
        Task<ApiResponse<PaymentDTO>> UpdatePaymentAsync(int id, UpdatePaymentDTO request);
        Task<ApiResponse<object>> DeletePaymentAsync(int id);
        Task<ApiResponse<List<PaymentDTO>>> GetPaymentsByOrderIdAsync(int orderId);
    }

    public class PaymentService : IPaymentService
    {
        private readonly IApiService _apiService;
        private readonly ILogger<PaymentService> _logger;

        public PaymentService(IApiService apiService, ILogger<PaymentService> logger)
        {
            _apiService = apiService;
            _logger = logger;
        }

        public async Task<ApiResponse<List<PaymentDTO>>> GetPaymentsAsync()
        {
            try
            {
                _logger.LogInformation("Getting payments from API");
                var response = await _apiService.GetAsync<List<PaymentDTO>>("api/payment");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payments");
                return new ApiResponse<List<PaymentDTO>>
                {
                    Success = false,
                    Message = "获取款项列表失败",
                    Data = new List<PaymentDTO>()
                };
            }
        }

        public async Task<ApiResponse<PaymentDTO>> GetPaymentByIdAsync(int id)
        {
            try
            {
                _logger.LogInformation("Getting payment by ID: {PaymentId}", id);
                var response = await _apiService.GetAsync<PaymentDTO>($"api/payment/{id}");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payment by ID: {PaymentId}", id);
                return new ApiResponse<PaymentDTO>
                {
                    Success = false,
                    Message = "获取款项详情失败",
                    Data = null
                };
            }
        }

        public async Task<ApiResponse<PaymentDTO>> CreatePaymentAsync(CreatePaymentDTO request)
        {
            try
            {
                _logger.LogInformation("Creating payment for order: {OrderId}", request.OrderId);
                var response = await _apiService.PostAsync<PaymentDTO>("api/payment", request);
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating payment for order: {OrderId}", request.OrderId);
                return new ApiResponse<PaymentDTO>
                {
                    Success = false,
                    Message = "创建款项失败",
                    Data = null
                };
            }
        }

        public async Task<ApiResponse<PaymentDTO>> UpdatePaymentAsync(int id, UpdatePaymentDTO request)
        {
            try
            {
                _logger.LogInformation("Updating payment: {PaymentId}", id);
                var response = await _apiService.PutAsync<PaymentDTO>($"api/payment/{id}", request);
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating payment: {PaymentId}", id);
                return new ApiResponse<PaymentDTO>
                {
                    Success = false,
                    Message = "更新款项失败",
                    Data = null
                };
            }
        }

        public async Task<ApiResponse<object>> DeletePaymentAsync(int id)
        {
            try
            {
                _logger.LogInformation("Deleting payment: {PaymentId}", id);
                var response = await _apiService.DeleteAsync<object>($"api/payment/{id}");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting payment: {PaymentId}", id);
                return new ApiResponse<object>
                {
                    Success = false,
                    Message = "删除款项失败",
                    Data = null
                };
            }
        }

        public async Task<ApiResponse<List<PaymentDTO>>> GetPaymentsByOrderIdAsync(int orderId)
        {
            try
            {
                _logger.LogInformation("Getting payments by order: {OrderId}", orderId);
                var response = await _apiService.GetAsync<List<PaymentDTO>>($"api/payment/order/{orderId}");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payments by order: {OrderId}", orderId);
                return new ApiResponse<List<PaymentDTO>>
                {
                    Success = false,
                    Message = "获取订单款项失败",
                    Data = new List<PaymentDTO>()
                };
            }
        }
    }
}
