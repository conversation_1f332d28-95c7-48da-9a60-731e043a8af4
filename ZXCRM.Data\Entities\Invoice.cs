namespace ZXCRM.Data.Entities
{
    public class Invoice : BaseEntity
    {
        public int PaymentId { get; set; }
        public string Company { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public decimal TaxRate { get; set; }
        public decimal Amount { get; set; } // 新增发票金额字段
        public string Status { get; set; } = "Normal"; // 新增发票状态字段，默认为正常
        public string? Content { get; set; }
        public string? Code { get; set; }
        public string? ReceiverName { get; set; }
        public string? ReceiverPhone { get; set; }
        public string? MailingAddress { get; set; }
        public string? ApplicantPhone { get; set; }
        public string? CustomerEmail { get; set; }
        public int CreatedById { get; set; }

        // 导航属性
        public Payment Payment { get; set; } = null!;
        public User CreatedBy { get; set; } = null!;
    }
}
