using Newtonsoft.Json;
using System.Text;
using System.Diagnostics;
using ZXCRM.WebUI.Models;

namespace ZXCRM.WebUI.Services
{
    public class ApiService : IApiService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<ApiService> _logger;
        private readonly IApiPerformanceService? _performanceService;

        public ApiService(IHttpClientFactory httpClientFactory, ILogger<ApiService> logger, IApiPerformanceService? performanceService = null)
        {
            _httpClient = httpClientFactory.CreateClient("ZXCRM_API");
            _logger = logger;
            _performanceService = performanceService;
        }

        public async Task<ApiResponse<T>> GetAsync<T>(string endpoint)
        {
            var stopwatch = Stopwatch.StartNew();
            bool success = false;

            try
            {
                _logger.LogDebug("GET request to: {Endpoint}", endpoint);

                var response = await _httpClient.GetAsync(endpoint);
                var content = await response.Content.ReadAsStringAsync();

                _logger.LogDebug("Response status: {StatusCode} for {Endpoint}", response.StatusCode, endpoint);

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<T>>(content);
                    success = result?.Success ?? false;
                    return result ?? new ApiResponse<T> { Success = false, Message = "Failed to deserialize response" };
                }
                else
                {
                    return new ApiResponse<T>
                    {
                        Success = false,
                        Message = $"API call failed with status: {response.StatusCode}"
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GET request to {Endpoint}", endpoint);
                return new ApiResponse<T>
                {
                    Success = false,
                    Message = $"Request failed: {ex.Message}"
                };
            }
            finally
            {
                stopwatch.Stop();
                _performanceService?.RecordApiCall($"GET {endpoint}", stopwatch.Elapsed, success);

                if (stopwatch.ElapsedMilliseconds > 3000)
                {
                    _logger.LogWarning("Slow API call: GET {Endpoint} took {ElapsedMs}ms", endpoint, stopwatch.ElapsedMilliseconds);
                }
            }
        }

        public async Task<ApiResponse<T>> PostAsync<T>(string endpoint, object data)
        {
            try
            {
                _logger.LogInformation("POST request to: {Endpoint}", endpoint);

                var json = JsonConvert.SerializeObject(data);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(endpoint, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                _logger.LogInformation("Response status: {StatusCode}", response.StatusCode);

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<T>>(responseContent);
                    return result ?? new ApiResponse<T> { Success = false, Message = "Failed to deserialize response" };
                }
                else
                {
                    return new ApiResponse<T>
                    {
                        Success = false,
                        Message = $"API call failed with status: {response.StatusCode}"
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in POST request to {Endpoint}", endpoint);
                return new ApiResponse<T>
                {
                    Success = false,
                    Message = $"Request failed: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<T>> PutAsync<T>(string endpoint, object data)
        {
            try
            {
                _logger.LogInformation("PUT request to: {Endpoint}", endpoint);

                var json = JsonConvert.SerializeObject(data);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PutAsync(endpoint, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                _logger.LogInformation("Response status: {StatusCode}", response.StatusCode);

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<T>>(responseContent);
                    return result ?? new ApiResponse<T> { Success = false, Message = "Failed to deserialize response" };
                }
                else
                {
                    return new ApiResponse<T>
                    {
                        Success = false,
                        Message = $"API call failed with status: {response.StatusCode}"
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in PUT request to {Endpoint}", endpoint);
                return new ApiResponse<T>
                {
                    Success = false,
                    Message = $"Request failed: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<T>> DeleteAsync<T>(string endpoint)
        {
            try
            {
                _logger.LogInformation("DELETE request to: {Endpoint}", endpoint);

                var response = await _httpClient.DeleteAsync(endpoint);
                var content = await response.Content.ReadAsStringAsync();

                _logger.LogInformation("Response status: {StatusCode}", response.StatusCode);

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<T>>(content);
                    return result ?? new ApiResponse<T> { Success = false, Message = "Failed to deserialize response" };
                }
                else
                {
                    return new ApiResponse<T>
                    {
                        Success = false,
                        Message = $"API call failed with status: {response.StatusCode}"
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in DELETE request to {Endpoint}", endpoint);
                return new ApiResponse<T>
                {
                    Success = false,
                    Message = $"Request failed: {ex.Message}"
                };
            }
        }

        public void SetAuthToken(string token)
        {
            _httpClient.DefaultRequestHeaders.Authorization =
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
        }

        public void ClearAuthToken()
        {
            _httpClient.DefaultRequestHeaders.Authorization = null;
        }
    }
}
