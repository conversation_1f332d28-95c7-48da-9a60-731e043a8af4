{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["nc1uywmGfQ0uGTYVRQKqm+Ccwn4HumQKaPLsnWRr/Po=", "UoYmXk6MP13EafYr4kjUhp00o6hT4FRyl1Os8VaYPO8=", "StmO+3iSbRk5wZYUbJL4nj3jTVbRXR3oLEQNKnKUvQY="], "CachedAssets": {"nc1uywmGfQ0uGTYVRQKqm+Ccwn4HumQKaPLsnWRr/Po=": {"Identity": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\2mesmu78bt-c5jsywe0fz.gz", "SourceId": "ZXCRM.WebUI", "SourceType": "Discovered", "ContentRoot": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ZXCRM.WebUI", "RelativePath": "css/site#[.{fingerprint=c5jsywe0fz}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "07f12uybcj", "Integrity": "LF247tm+Ygw3W+aMphNfMTDK72I9bXMullzV3MzwHuY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\css\\site.css", "FileLength": 4180, "LastWriteTime": "2025-06-17T05:17:19.9750616+00:00"}, "UoYmXk6MP13EafYr4kjUhp00o6hT4FRyl1Os8VaYPO8=": {"Identity": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\ipjiihu8wq-ji9w67flmt.gz", "SourceId": "ZXCRM.WebUI", "SourceType": "Discovered", "ContentRoot": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ZXCRM.WebUI", "RelativePath": "img/placeholder#[.{fingerprint=ji9w67flmt}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\img\\placeholder.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "awksfbh0mk", "Integrity": "NhzAA6NBedqJytCK3ToxeUU3Dc1+TmFGhFNN8Ifz84A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\img\\placeholder.txt", "FileLength": 317, "LastWriteTime": "2025-06-17T05:17:19.9750616+00:00"}, "StmO+3iSbRk5wZYUbJL4nj3jTVbRXR3oLEQNKnKUvQY=": {"Identity": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\lr93ksuwtu-o400d3i4ax.gz", "SourceId": "ZXCRM.WebUI", "SourceType": "Discovered", "ContentRoot": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ZXCRM.WebUI", "RelativePath": "js/site#[.{fingerprint=o400d3i4ax}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bd2hdj<PERSON><PERSON>", "Integrity": "P+850Ht/tnJrgjQ/VToKj4GQMcP41kBRp8POlZiQsxM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\js\\site.js", "FileLength": 2316, "LastWriteTime": "2025-06-17T05:17:19.9750616+00:00"}}, "CachedCopyCandidates": {}}