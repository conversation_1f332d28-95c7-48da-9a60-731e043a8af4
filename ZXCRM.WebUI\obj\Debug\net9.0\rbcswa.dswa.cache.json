{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["HjfCvL5NTsH7x/NCYfsuv743dKK5ECq8FrjdQKCHaMk=", "oO0Vwy5ISLVNq+MPvQw6QuMZvAQocW7bqb5XDuNLVhE=", "KdAK+rVJijgf1SsI5W0NC+yKgdPMYb/0vdPZXuMzbJM="], "CachedAssets": {"HjfCvL5NTsH7x/NCYfsuv743dKK5ECq8FrjdQKCHaMk=": {"Identity": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\2mesmu78bt-c5jsywe0fz.gz", "SourceId": "ZXCRM.WebUI", "SourceType": "Discovered", "ContentRoot": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ZXCRM.WebUI", "RelativePath": "css/site#[.{fingerprint=c5jsywe0fz}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "swch23p1r3", "Integrity": "nFmdf21VeFw1p/39Jd1UhrlAPYNp5TjkmXhZvvAdOo8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\css\\site.css", "FileLength": 4147, "LastWriteTime": "2025-05-29T10:06:18.7446459+00:00"}, "oO0Vwy5ISLVNq+MPvQw6QuMZvAQocW7bqb5XDuNLVhE=": {"Identity": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\ipjiihu8wq-ji9w67flmt.gz", "SourceId": "ZXCRM.WebUI", "SourceType": "Discovered", "ContentRoot": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ZXCRM.WebUI", "RelativePath": "img/placeholder#[.{fingerprint=ji9w67flmt}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\img\\placeholder.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "awksfbh0mk", "Integrity": "NhzAA6NBedqJytCK3ToxeUU3Dc1+TmFGhFNN8Ifz84A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\img\\placeholder.txt", "FileLength": 317, "LastWriteTime": "2025-05-28T09:17:58.8404933+00:00"}, "KdAK+rVJijgf1SsI5W0NC+yKgdPMYb/0vdPZXuMzbJM=": {"Identity": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\lr93ksuwtu-o400d3i4ax.gz", "SourceId": "ZXCRM.WebUI", "SourceType": "Discovered", "ContentRoot": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ZXCRM.WebUI", "RelativePath": "js/site#[.{fingerprint=o400d3i4ax}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bd2hdj<PERSON><PERSON>", "Integrity": "P+850Ht/tnJrgjQ/VToKj4GQMcP41kBRp8POlZiQsxM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\js\\site.js", "FileLength": 2316, "LastWriteTime": "2025-05-28T09:17:58.8404933+00:00"}}, "CachedCopyCandidates": {}}