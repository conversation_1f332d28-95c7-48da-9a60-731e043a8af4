{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["xXSk143HJdocCQ0L3iymSaNRXfm6voOZ3IKOvlQRt+M=", "XCu0bcabq3LUnsdkzi1RTeSCeKi7LhxbKFc0K3DJI6c=", "RLcVB2Afb9HJD5gIQRSiHa4mpU9SV5AKom53G6XUn1I="], "CachedAssets": {"RLcVB2Afb9HJD5gIQRSiHa4mpU9SV5AKom53G6XUn1I=": {"Identity": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\lr93ksuwtu-o400d3i4ax.gz", "SourceId": "ZXCRM.WebUI", "SourceType": "Discovered", "ContentRoot": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ZXCRM.WebUI", "RelativePath": "js/site#[.{fingerprint=o400d3i4ax}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bd2hdj<PERSON><PERSON>", "Integrity": "P+850Ht/tnJrgjQ/VToKj4GQMcP41kBRp8POlZiQsxM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\js\\site.js", "FileLength": 2316, "LastWriteTime": "2025-06-05T08:15:56.8894676+00:00"}, "XCu0bcabq3LUnsdkzi1RTeSCeKi7LhxbKFc0K3DJI6c=": {"Identity": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\ipjiihu8wq-ji9w67flmt.gz", "SourceId": "ZXCRM.WebUI", "SourceType": "Discovered", "ContentRoot": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ZXCRM.WebUI", "RelativePath": "img/placeholder#[.{fingerprint=ji9w67flmt}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\img\\placeholder.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "awksfbh0mk", "Integrity": "NhzAA6NBedqJytCK3ToxeUU3Dc1+TmFGhFNN8Ifz84A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\img\\placeholder.txt", "FileLength": 317, "LastWriteTime": "2025-06-05T08:15:56.8894676+00:00"}, "xXSk143HJdocCQ0L3iymSaNRXfm6voOZ3IKOvlQRt+M=": {"Identity": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\2mesmu78bt-c5jsywe0fz.gz", "SourceId": "ZXCRM.WebUI", "SourceType": "Discovered", "ContentRoot": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ZXCRM.WebUI", "RelativePath": "css/site#[.{fingerprint=c5jsywe0fz}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "07f12uybcj", "Integrity": "LF247tm+Ygw3W+aMphNfMTDK72I9bXMullzV3MzwHuY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\css\\site.css", "FileLength": 4180, "LastWriteTime": "2025-06-05T08:15:56.8894676+00:00"}}, "CachedCopyCandidates": {}}