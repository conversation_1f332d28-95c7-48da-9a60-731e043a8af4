using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;

namespace ZXCRM.WebUI.Controllers
{
    [Authorize]
    public abstract class BaseController : Controller
    {
        protected void SetSuccessMessage(string message)
        {
            TempData["SuccessMessage"] = message;
        }

        protected void SetErrorMessage(string message)
        {
            TempData["ErrorMessage"] = message;
        }

        protected void SetWarningMessage(string message)
        {
            TempData["WarningMessage"] = message;
        }

        protected void SetInfoMessage(string message)
        {
            TempData["InfoMessage"] = message;
        }

        protected string GetCurrentUserId()
        {
            return User.FindFirst("UserId")?.Value ?? string.Empty;
        }

        protected string GetCurrentUserName()
        {
            return User.FindFirst("UserName")?.Value ?? string.Empty;
        }

        protected string GetCurrentUserRole()
        {
            return User.FindFirst("Role")?.Value ?? string.Empty;
        }

        protected bool IsAdmin()
        {
            return User.IsInRole("Admin");
        }

        protected bool IsUser()
        {
            return User.IsInRole("User");
        }
    }
}
