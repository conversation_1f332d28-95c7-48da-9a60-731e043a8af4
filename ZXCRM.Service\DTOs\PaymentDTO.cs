using System;

namespace ZXCRM.Service.DTOs
{
    public class PaymentDTO
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public int OrderId { get; set; }
        public string OrderName { get; set; } = string.Empty;
        public string OrderCode { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string PaymentType { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public decimal SettlementAmount { get; set; }
        public DateTime? ExpectedPaymentDate { get; set; }
        public DateTime? ActualPaymentDate { get; set; }
        public string PaymentStatus { get; set; } = "Pending";
        public string InvoiceStatus { get; set; } = string.Empty;
        public DateTime? InvoiceDate { get; set; }
        public string AccountManagerName { get; set; } = string.Empty;
        public string ProjectManagerName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }
}
