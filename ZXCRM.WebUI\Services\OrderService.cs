using ZXCRM.Service.DTOs;
using ZXCRM.WebUI.Models;

namespace ZXCRM.WebUI.Services
{
    public class OrderService : IOrderService
    {
        private readonly IApiService _apiService;
        private readonly ILogger<OrderService> _logger;

        public OrderService(IApiService apiService, ILogger<OrderService> logger)
        {
            _apiService = apiService;
            _logger = logger;
        }

        public async Task<ApiResponse<List<OrderDTO>>> GetOrdersAsync()
        {
            try
            {
                _logger.LogInformation("Getting orders list");

                var response = await _apiService.GetAsync<List<OrderDTO>>("api/order");

                if (response.Success)
                {
                    _logger.LogInformation("Successfully retrieved {Count} orders", response.Data?.Count ?? 0);
                }
                else
                {
                    _logger.LogWarning("Failed to retrieve orders: {Message}", response.Message);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting orders list");
                return new ApiResponse<List<OrderDTO>>
                {
                    Success = false,
                    Message = $"Failed to get orders: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<OrderDTO>> GetOrderByIdAsync(int id)
        {
            try
            {
                _logger.LogInformation("Getting order by ID: {OrderId}", id);

                var response = await _apiService.GetAsync<OrderDTO>($"api/order/{id}");

                if (response.Success)
                {
                    _logger.LogInformation("Successfully retrieved order: {OrderId}", id);
                }
                else
                {
                    _logger.LogWarning("Failed to retrieve order {OrderId}: {Message}", id, response.Message);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting order by ID: {OrderId}", id);
                return new ApiResponse<OrderDTO>
                {
                    Success = false,
                    Message = $"Failed to get order: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<OrderDTO>> CreateOrderAsync(CreateOrderDTO request)
        {
            try
            {
                _logger.LogInformation("Creating new order for customer: {CustomerName}", request.CustomerName);

                var response = await _apiService.PostAsync<OrderDTO>("api/order", request);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully created order for customer: {CustomerName}", request.CustomerName);
                }
                else
                {
                    _logger.LogWarning("Failed to create order for customer {CustomerName}: {Message}",
                        request.CustomerName, response.Message);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating order for customer: {CustomerName}", request.CustomerName);
                return new ApiResponse<OrderDTO>
                {
                    Success = false,
                    Message = $"Failed to create order: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<OrderDTO>> UpdateOrderAsync(int id, UpdateOrderDTO request)
        {
            try
            {
                _logger.LogInformation("Updating order: {OrderId}", id);

                var response = await _apiService.PutAsync<OrderDTO>($"api/order/{id}", request);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully updated order: {OrderId}", id);
                }
                else
                {
                    _logger.LogWarning("Failed to update order {OrderId}: {Message}", id, response.Message);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating order: {OrderId}", id);
                return new ApiResponse<OrderDTO>
                {
                    Success = false,
                    Message = $"Failed to update order: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<object>> DeleteOrderAsync(int id)
        {
            try
            {
                _logger.LogInformation("Deleting order: {OrderId}", id);

                var response = await _apiService.DeleteAsync<object>($"api/order/{id}");

                if (response.Success)
                {
                    _logger.LogInformation("Successfully deleted order: {OrderId}", id);
                }
                else
                {
                    _logger.LogWarning("Failed to delete order {OrderId}: {Message}", id, response.Message);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting order: {OrderId}", id);
                return new ApiResponse<object>
                {
                    Success = false,
                    Message = $"Failed to delete order: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<List<OrderDTO>>> GetOrdersByStatusAsync(string status)
        {
            try
            {
                _logger.LogInformation("Getting orders by status: {Status}", status);

                var response = await _apiService.GetAsync<List<OrderDTO>>($"api/order/status/{status}");

                if (response.Success)
                {
                    _logger.LogInformation("Successfully retrieved {Count} orders with status: {Status}",
                        response.Data?.Count ?? 0, status);
                }
                else
                {
                    _logger.LogWarning("Failed to retrieve orders with status {Status}: {Message}", status, response.Message);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting orders by status: {Status}", status);
                return new ApiResponse<List<OrderDTO>>
                {
                    Success = false,
                    Message = $"Failed to get orders by status: {ex.Message}"
                };
            }
        }
    }
}
