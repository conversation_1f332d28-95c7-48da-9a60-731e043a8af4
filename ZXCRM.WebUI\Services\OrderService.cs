using ZXCRM.Service.DTOs;
using ZXCRM.WebUI.Models;

namespace ZXCRM.WebUI.Services
{
    public class OrderService : IOrderService
    {
        private readonly IApiService _apiService;
        private readonly ILogger<OrderService> _logger;

        public OrderService(IApiService apiService, ILogger<OrderService> logger)
        {
            _apiService = apiService;
            _logger = logger;
        }

        public async Task<ApiResponse<List<OrderDTO>>> GetOrdersAsync()
        {
            try
            {
                _logger.LogInformation("=== WebUI: 开始调用API获取订单列表 ===");

                var response = await _apiService.GetAsync<List<OrderDTO>>("api/order");

                _logger.LogInformation("WebUI: API响应 Success={Success}, Message={Message}, DataCount={Count}",
                    response.Success, response.Message ?? "NULL", response.Data?.Count ?? 0);

                // 记录第一个订单的WebUI层数据
                if (response.Success && response.Data != null && response.Data.Any())
                {
                    var firstOrder = response.Data.First();
                    _logger.LogInformation("WebUI: 第一个订单数据: ID={Id}, Name={Name}, Code={Code}, Amount={Amount}, Currency={Currency}, Status={Status}",
                        firstOrder.Id, firstOrder.Name ?? "NULL", firstOrder.Code ?? "NULL", firstOrder.Amount, firstOrder.Currency ?? "NULL", firstOrder.Status ?? "NULL");
                }
                else if (!response.Success)
                {
                    _logger.LogWarning("WebUI: API调用失败: {Message}", response.Message);
                }
                else if (response.Data == null || !response.Data.Any())
                {
                    _logger.LogWarning("WebUI: API返回空数据");
                }

                _logger.LogInformation("=== WebUI: 订单列表API调用完成 ===");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "WebUI: Error getting orders list");
                return new ApiResponse<List<OrderDTO>>
                {
                    Success = false,
                    Message = $"Failed to get orders: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<OrderDTO>> GetOrderByIdAsync(int id)
        {
            try
            {
                _logger.LogInformation("=== WebUI: 开始调用API获取订单详情 ID={Id} ===", id);

                var response = await _apiService.GetAsync<OrderDTO>($"api/order/{id}");

                _logger.LogInformation("WebUI: API响应 Success={Success}, Message={Message}",
                    response.Success, response.Message ?? "NULL");

                if (response.Success && response.Data != null)
                {
                    _logger.LogInformation("WebUI: 订单详情数据: ID={Id}, Name={Name}, Code={Code}, Amount={Amount}, Currency={Currency}, Status={Status}",
                        response.Data.Id, response.Data.Name ?? "NULL", response.Data.Code ?? "NULL", response.Data.Amount, response.Data.Currency ?? "NULL", response.Data.Status ?? "NULL");
                }
                else if (!response.Success)
                {
                    _logger.LogWarning("WebUI: API调用失败: {Message}", response.Message);
                }
                else
                {
                    _logger.LogWarning("WebUI: API返回空数据");
                }

                _logger.LogInformation("=== WebUI: 订单详情API调用完成 ===");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "WebUI: Error getting order by ID: {OrderId}", id);
                return new ApiResponse<OrderDTO>
                {
                    Success = false,
                    Message = $"Failed to get order: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<OrderDTO>> CreateOrderAsync(CreateOrderDTO request)
        {
            try
            {
                _logger.LogInformation("Creating new order for customer: {CustomerName}", request.CustomerName);

                var response = await _apiService.PostAsync<OrderDTO>("api/order", request);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully created order for customer: {CustomerName}", request.CustomerName);
                }
                else
                {
                    _logger.LogWarning("Failed to create order for customer {CustomerName}: {Message}",
                        request.CustomerName, response.Message);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating order for customer: {CustomerName}", request.CustomerName);
                return new ApiResponse<OrderDTO>
                {
                    Success = false,
                    Message = $"Failed to create order: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<OrderDTO>> UpdateOrderAsync(int id, UpdateOrderDTO request)
        {
            try
            {
                _logger.LogInformation("Updating order: {OrderId}", id);

                var response = await _apiService.PutAsync<OrderDTO>($"api/order/{id}", request);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully updated order: {OrderId}", id);
                }
                else
                {
                    _logger.LogWarning("Failed to update order {OrderId}: {Message}", id, response.Message);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating order: {OrderId}", id);
                return new ApiResponse<OrderDTO>
                {
                    Success = false,
                    Message = $"Failed to update order: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<object>> DeleteOrderAsync(int id)
        {
            try
            {
                _logger.LogInformation("Deleting order: {OrderId}", id);

                var response = await _apiService.DeleteAsync<object>($"api/order/{id}");

                if (response.Success)
                {
                    _logger.LogInformation("Successfully deleted order: {OrderId}", id);
                }
                else
                {
                    _logger.LogWarning("Failed to delete order {OrderId}: {Message}", id, response.Message);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting order: {OrderId}", id);
                return new ApiResponse<object>
                {
                    Success = false,
                    Message = $"Failed to delete order: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<List<OrderDTO>>> GetOrdersByStatusAsync(string status)
        {
            try
            {
                _logger.LogInformation("Getting orders by status: {Status}", status);

                var response = await _apiService.GetAsync<List<OrderDTO>>($"api/order/status/{status}");

                if (response.Success)
                {
                    _logger.LogInformation("Successfully retrieved {Count} orders with status: {Status}",
                        response.Data?.Count ?? 0, status);
                }
                else
                {
                    _logger.LogWarning("Failed to retrieve orders with status {Status}: {Message}", status, response.Message);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting orders by status: {Status}", status);
                return new ApiResponse<List<OrderDTO>>
                {
                    Success = false,
                    Message = $"Failed to get orders by status: {ex.Message}"
                };
            }
        }
    }
}
