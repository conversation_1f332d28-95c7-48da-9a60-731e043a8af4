@model ZXCRM.WebUI.Models.ViewModels.ReportDashboardViewModel
@{
    ViewData["Title"] = "综合仪表盘";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">综合仪表盘</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a asp-controller="Dashboard" asp-action="Index">首页</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Report" asp-action="Index">报表统计</a></li>
                    <li class="breadcrumb-item active">综合仪表盘</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <!-- 查询条件 -->
        <div class="card collapsed-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-filter"></i> 查询条件
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body" style="display: none;">
                <form method="get" asp-action="Dashboard">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label asp-for="Query.StartDate">开始日期</label>
                                <input asp-for="Query.StartDate" type="date" class="form-control" />
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label asp-for="Query.EndDate">结束日期</label>
                                <input asp-for="Query.EndDate" type="date" class="form-control" />
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary btn-block">
                                        <i class="fas fa-search"></i> 查询
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        @if (Model.Stats != null)
        {
            <!-- 关键指标 -->
            <div class="row">
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3>@Model.Stats.TotalOpportunities</h3>
                            <p>总商机数</p>
                            <div class="progress">
                                <div class="progress-bar" style="width: @Model.Stats.OpportunityWinRate%"></div>
                            </div>
                            <span class="progress-description">
                                成交率: @Model.Stats.OpportunityWinRate.ToString("F1")%
                            </span>
                        </div>
                        <div class="icon">
                            <i class="fas fa-seedling"></i>
                        </div>
                        <a asp-action="Opportunity" class="small-box-footer">
                            查看详情 <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>

                <div class="col-lg-3 col-6">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3>@Model.Stats.TotalOrders</h3>
                            <p>总订单数</p>
                            <div class="progress">
                                <div class="progress-bar bg-success" style="width: 70%"></div>
                            </div>
                            <span class="progress-description">
                                本月: @Model.Stats.MonthlyOrderAmount.ToString("N0")
                            </span>
                        </div>
                        <div class="icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <a asp-action="Order" class="small-box-footer">
                            查看详情 <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>

                <div class="col-lg-3 col-6">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3>@Model.Stats.TotalPayments</h3>
                            <p>总款项数</p>
                            <div class="progress">
                                <div class="progress-bar bg-warning" style="width: 60%"></div>
                            </div>
                            <span class="progress-description">
                                本月: @Model.Stats.MonthlyPaymentAmount.ToString("N0")
                            </span>
                        </div>
                        <div class="icon">
                            <i class="fas fa-money-bill"></i>
                        </div>
                        <a asp-action="Payment" class="small-box-footer">
                            查看详情 <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>

                <div class="col-lg-3 col-6">
                    <div class="small-box bg-danger">
                        <div class="inner">
                            <h3>@Model.Stats.TotalInvoices</h3>
                            <p>总发票数</p>
                            <div class="progress">
                                <div class="progress-bar bg-danger" style="width: 80%"></div>
                            </div>
                            <span class="progress-description">
                                本月: @Model.Stats.MonthlyInvoiceAmount.ToString("N0")
                            </span>
                        </div>
                        <div class="icon">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                        <a asp-action="Invoice" class="small-box-footer">
                            查看详情 <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 金额统计卡片 -->
            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-dollar-sign"></i> 订单金额统计
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <div class="description-block border-right">
                                        <span class="description-percentage text-success">
                                            <i class="fas fa-caret-up"></i> 总金额
                                        </span>
                                        <h5 class="description-header">@Model.Stats.TotalOrderAmount.ToString("N0")</h5>
                                        <span class="description-text">订单总金额</span>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="description-block">
                                        <span class="description-percentage text-warning">
                                            <i class="fas fa-caret-left"></i> 本月
                                        </span>
                                        <h5 class="description-header">@Model.Stats.MonthlyOrderAmount.ToString("N0")</h5>
                                        <span class="description-text">本月订单</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-coins"></i> 回款金额统计
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <div class="description-block border-right">
                                        <span class="description-percentage text-success">
                                            <i class="fas fa-caret-up"></i> 总金额
                                        </span>
                                        <h5 class="description-header">@Model.Stats.TotalPaymentAmount.ToString("N0")</h5>
                                        <span class="description-text">回款总金额</span>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="description-block">
                                        <span class="description-percentage text-warning">
                                            <i class="fas fa-caret-left"></i> 本月
                                        </span>
                                        <h5 class="description-header">@Model.Stats.MonthlyPaymentAmount.ToString("N0")</h5>
                                        <span class="description-text">本月回款</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-receipt"></i> 发票金额统计
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <div class="description-block border-right">
                                        <span class="description-percentage text-success">
                                            <i class="fas fa-caret-up"></i> 总金额
                                        </span>
                                        <h5 class="description-header">@Model.Stats.TotalInvoiceAmount.ToString("N0")</h5>
                                        <span class="description-text">发票总金额</span>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="description-block">
                                        <span class="description-percentage text-warning">
                                            <i class="fas fa-caret-left"></i> 本月
                                        </span>
                                        <h5 class="description-header">@Model.Stats.MonthlyInvoiceAmount.ToString("N0")</h5>
                                        <span class="description-text">本月发票</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-chart-line"></i> 趋势图表
                            </h3>
                            <div class="card-tools">
                                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                    <i class="fas fa-minus"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h5><i class="icon fas fa-info"></i> 提示</h5>
                                图表功能正在开发中，将使用ECharts实现数据可视化。
                                <br>
                                <strong>计划功能：</strong>
                                <ul class="mb-0">
                                    <li>商机转化趋势图</li>
                                    <li>订单金额月度对比</li>
                                    <li>回款率统计图</li>
                                    <li>发票开票趋势</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
        else
        {
            <div class="alert alert-warning">
                <h4><i class="icon fas fa-exclamation-triangle"></i> 提示</h4>
                暂无统计数据，请检查查询条件或联系管理员。
            </div>
        }

        <!-- 快捷导航 -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-compass"></i> 快捷导航
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-2">
                                <a asp-action="Opportunity" class="btn btn-app">
                                    <i class="fas fa-seedling"></i> 商机报表
                                </a>
                            </div>
                            <div class="col-md-2">
                                <a asp-action="Order" class="btn btn-app">
                                    <i class="fas fa-shopping-cart"></i> 订单报表
                                </a>
                            </div>
                            <div class="col-md-2">
                                <a asp-action="Payment" class="btn btn-app">
                                    <i class="fas fa-money-bill"></i> 款项报表
                                </a>
                            </div>
                            <div class="col-md-2">
                                <a asp-action="Invoice" class="btn btn-app">
                                    <i class="fas fa-file-invoice"></i> 发票报表
                                </a>
                            </div>
                            <div class="col-md-2">
                                <a asp-controller="Report" asp-action="Index" class="btn btn-app">
                                    <i class="fas fa-home"></i> 返回首页
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        $(document).ready(function() {
            // 显示提示消息
            @if (TempData["SuccessMessage"] != null)
            {
                <text>
                toastr.success('@TempData["SuccessMessage"]');
                </text>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <text>
                toastr.error('@TempData["ErrorMessage"]');
                </text>
            }
        });
    </script>
}
