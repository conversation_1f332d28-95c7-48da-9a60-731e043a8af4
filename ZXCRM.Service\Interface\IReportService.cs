using ZXCRM.Service.DTOs;

namespace ZXCRM.Service.Interface
{
    public interface IReportService
    {
        // 仪表盘统计
        Task<DashboardStatsDTO> GetDashboardStatsAsync(ReportQueryDTO? query = null);
        
        // 商机统计报表
        Task<OpportunityReportDTO> GetOpportunityReportAsync(ReportQueryDTO? query = null);
        
        // 订单统计报表
        Task<OrderReportDTO> GetOrderReportAsync(ReportQueryDTO? query = null);
        
        // 款项统计报表
        Task<PaymentReportDTO> GetPaymentReportAsync(ReportQueryDTO? query = null);
        
        // 发票统计报表
        Task<InvoiceReportDTO> GetInvoiceReportAsync(ReportQueryDTO? query = null);
        
        // 导出报表数据
        Task<byte[]> ExportReportAsync(string reportType, ReportQueryDTO? query = null);
    }
}
