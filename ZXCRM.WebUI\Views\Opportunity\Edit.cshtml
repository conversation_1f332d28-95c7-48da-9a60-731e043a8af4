@model ZXCRM.WebUI.Models.ViewModels.EditOpportunityViewModel
@{
    ViewData["Title"] = "编辑商机";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">编辑商机</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a asp-controller="Dashboard" asp-action="Index">首页</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Opportunity" asp-action="Index">商机管理</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Opportunity" asp-action="Details" asp-route-id="@Model.Id">商机详情</a></li>
                    <li class="breadcrumb-item active">编辑商机</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-edit"></i> 编辑商机信息
                        </h3>
                        <div class="card-tools">
                            <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info btn-sm">
                                <i class="fas fa-eye"></i> 查看详情
                            </a>
                        </div>
                    </div>
                    <form asp-action="Edit" method="post">
                        <input asp-for="Id" type="hidden" />
                        <div class="card-body">
                            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <!-- 商机名称 -->
                                    <div class="form-group">
                                        <label asp-for="Name" class="form-label required"></label>
                                        <input asp-for="Name" class="form-control" placeholder="请输入商机名称" />
                                        <span asp-validation-for="Name" class="text-danger"></span>
                                    </div>

                                    <!-- 客户名称 -->
                                    <div class="form-group">
                                        <label asp-for="CustomerName" class="form-label required"></label>
                                        <input asp-for="CustomerName" class="form-control" placeholder="请输入客户名称" />
                                        <span asp-validation-for="CustomerName" class="text-danger"></span>
                                    </div>

                                    <!-- 联系人 -->
                                    <div class="form-group">
                                        <label asp-for="ContactName" class="form-label"></label>
                                        <input asp-for="ContactName" class="form-control" placeholder="请输入联系人姓名" />
                                        <span asp-validation-for="ContactName" class="text-danger"></span>
                                    </div>

                                    <!-- 联系电话 -->
                                    <div class="form-group">
                                        <label asp-for="ContactPhone" class="form-label"></label>
                                        <input asp-for="ContactPhone" class="form-control" placeholder="请输入联系电话" />
                                        <span asp-validation-for="ContactPhone" class="text-danger"></span>
                                    </div>

                                    <!-- 商机状态 -->
                                    <div class="form-group">
                                        <label asp-for="Status" class="form-label required"></label>
                                        <select asp-for="Status" class="form-control">
                                            @foreach (var status in ZXCRM.WebUI.Models.ViewModels.OpportunityStatus.GetAllStatuses())
                                            {
                                                <option value="@status">@status</option>
                                            }
                                        </select>
                                        <span asp-validation-for="Status" class="text-danger"></span>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <!-- 商机内容 -->
                                    <div class="form-group">
                                        <label asp-for="Content" class="form-label"></label>
                                        <textarea asp-for="Content" class="form-control" rows="10" placeholder="请详细描述商机内容、需求、预期等信息..."></textarea>
                                        <span asp-validation-for="Content" class="text-danger"></span>
                                        <small class="form-text text-muted">
                                            <span class="char-count">@(Model.Content?.Length ?? 0)/1000</span> 字符
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card-footer">
                            <div class="row">
                                <div class="col-md-6">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> 保存更改
                                    </button>
                                    <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-secondary ml-2">
                                        <i class="fas fa-times"></i> 取消
                                    </a>
                                </div>
                                <div class="col-md-6 text-right">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i> 
                                        标有 <span class="text-danger">*</span> 的字段为必填项
                                    </small>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="col-md-4">
                <!-- 编辑说明 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-info-circle"></i> 编辑说明
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> 注意事项</h6>
                            <ul class="mb-0">
                                <li>修改商机状态会影响相关统计</li>
                                <li>客户信息变更请谨慎操作</li>
                                <li>商机内容建议保持详细和准确</li>
                                <li>状态变更会记录在系统日志中</li>
                            </ul>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb"></i> 提示</h6>
                            <p class="mb-0">
                                及时更新商机信息有助于团队协作和客户跟进。
                                建议定期回顾和更新商机状态。
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 快捷操作 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-bolt"></i> 快捷操作
                        </h3>
                    </div>
                    <div class="card-body">
                        <button type="button" class="btn btn-outline-success btn-sm btn-block" onclick="markAsWon()">
                            <i class="fas fa-trophy"></i> 标记为成交
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm btn-block" onclick="moveToNegotiation()">
                            <i class="fas fa-handshake"></i> 进入谈判阶段
                        </button>
                        <button type="button" class="btn btn-outline-warning btn-sm btn-block" onclick="moveToProposal()">
                            <i class="fas fa-file-alt"></i> 进入方案阶段
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm btn-block" onclick="markAsLost()">
                            <i class="fas fa-times-circle"></i> 标记为失败
                        </button>
                    </div>
                </div>

                <!-- 状态流转 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-exchange-alt"></i> 状态流转
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="status-flow">
                            <div class="status-item @(Model.Status == "潜在" ? "active" : "")">
                                <span class="badge badge-secondary">
                                    <i class="fas fa-seedling"></i> 潜在
                                </span>
                            </div>
                            <div class="flow-arrow">↓</div>
                            <div class="status-item @(Model.Status == "合格" ? "active" : "")">
                                <span class="badge badge-info">
                                    <i class="fas fa-check-circle"></i> 合格
                                </span>
                            </div>
                            <div class="flow-arrow">↓</div>
                            <div class="status-item @(Model.Status == "方案" ? "active" : "")">
                                <span class="badge badge-warning">
                                    <i class="fas fa-file-alt"></i> 方案
                                </span>
                            </div>
                            <div class="flow-arrow">↓</div>
                            <div class="status-item @(Model.Status == "谈判" ? "active" : "")">
                                <span class="badge badge-primary">
                                    <i class="fas fa-handshake"></i> 谈判
                                </span>
                            </div>
                            <div class="flow-arrow">↓</div>
                            <div class="status-item @(Model.Status == "成交" ? "active" : "")">
                                <span class="badge badge-success">
                                    <i class="fas fa-trophy"></i> 成交
                                </span>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                当前状态：
                                <span class="badge badge-@(ZXCRM.WebUI.Models.ViewModels.OpportunityStatus.GetStatusColor(Model.Status))">
                                    @Model.Status
                                </span>
                            </small>
                        </div>
                    </div>
                </div>

                <!-- 操作历史 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-history"></i> 操作历史
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <div class="time-label">
                                <span class="bg-info">最近操作</span>
                            </div>
                            <div>
                                <i class="fas fa-edit bg-blue"></i>
                                <div class="timeline-item">
                                    <span class="time"><i class="fas fa-clock"></i> 正在编辑</span>
                                    <h3 class="timeline-header">编辑商机信息</h3>
                                    <div class="timeline-body">
                                        当前正在编辑商机信息，请确认所有字段填写正确。
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        function markAsWon() {
            $('#Status').val('成交');
            toastr.success('已标记为成交状态');
        }

        function moveToNegotiation() {
            $('#Status').val('谈判');
            toastr.info('已设置为谈判状态');
        }

        function moveToProposal() {
            $('#Status').val('方案');
            toastr.info('已设置为方案状态');
        }

        function markAsLost() {
            if (confirm('确定要标记为失败吗？')) {
                $('#Status').val('失败');
                toastr.warning('已标记为失败状态');
            }
        }

        // 表单验证增强
        $(document).ready(function() {
            // 联系电话格式验证
            $('#ContactPhone').on('input', function() {
                const phone = $(this).val();
                const phoneRegex = /^1[3-9]\d{9}$|^\d{3,4}-\d{7,8}$/;
                if (phone && !phoneRegex.test(phone.replace(/\s|-/g, ''))) {
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });

            // 字符计数
            $('#Content').on('input', function() {
                const length = $(this).val().length;
                const maxLength = 1000;
                const remaining = maxLength - length;
                
                let countText = `${length}/${maxLength}`;
                if (remaining < 100) {
                    countText = `<span class="text-warning">${countText}</span>`;
                }
                if (remaining < 0) {
                    countText = `<span class="text-danger">${countText}</span>`;
                }
                
                $('.char-count').html(countText);
            });

            // 状态变更提醒
            $('#Status').on('change', function() {
                const newStatus = $(this).val();
                const oldStatus = '@Model.Status';
                
                if (newStatus !== oldStatus) {
                    toastr.info(`状态已变更为：${newStatus}`);
                    
                    // 根据状态变更给出建议
                    if (newStatus === '成交') {
                        toastr.success('恭喜！商机成交后可以创建订单');
                    } else if (newStatus === '失败') {
                        toastr.warning('请在商机内容中记录失败原因');
                    }
                }
            });

            // 初始化字符计数
            $('#Content').trigger('input');
        });
    </script>
}

@section Styles {
    <style>
        .required::after {
            content: " *";
            color: red;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .is-invalid {
            border-color: #dc3545;
        }

        .status-flow {
            text-align: center;
        }

        .status-item {
            margin: 5px 0;
        }

        .status-item.active .badge {
            font-size: 1.1em;
            padding: 0.6em 0.8em;
        }

        .flow-arrow {
            color: #6c757d;
            font-size: 1.2em;
            margin: 5px 0;
        }

        .timeline {
            position: relative;
            margin: 0 0 30px 0;
            padding: 0;
            list-style: none;
        }

        .timeline:before {
            content: '';
            position: absolute;
            top: 0;
            left: 25px;
            height: 100%;
            width: 2px;
            background: #dee2e6;
        }

        .timeline > div {
            position: relative;
            margin-right: 10px;
            margin-bottom: 15px;
        }

        .timeline > div > .timeline-item {
            box-shadow: 0 1px 1px rgba(0,0,0,0.1);
            border-radius: 3px;
            margin-top: 0;
            background: #fff;
            color: #444;
            margin-left: 60px;
            margin-right: 15px;
            padding: 5px;
            position: relative;
        }

        .timeline > div > .fas {
            width: 30px;
            height: 30px;
            font-size: 15px;
            line-height: 30px;
            position: absolute;
            color: #666;
            background: #f4f4f4;
            border-radius: 50%;
            text-align: center;
            left: 18px;
            top: 0;
        }
    </style>
}
