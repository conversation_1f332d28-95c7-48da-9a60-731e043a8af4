@model ZXCRM.WebUI.Models.ViewModels.PaymentListViewModel
@using ZXCRM.WebUI.Helpers
@{
    ViewData["Title"] = "款项管理";
}

<!-- 数据页面容器 -->
<div class="data-page-container">
    <!-- 查询区域（可收缩） -->
    <div class="query-section" id="querySection">
        <div class="query-header">
            <div class="query-title">
                <i class="fas fa-filter"></i>
                <span>筛选条件</span>
                <span class="badge badge-info ml-2">共 @Model.TotalCount 个款项</span>
            </div>
            <button type="button" class="query-toggle" onclick="toggleQuerySection()">
                <i class="fas fa-chevron-up" id="queryToggleIcon"></i>
            </button>
        </div>
        <div class="query-content" id="queryContent">
            <form asp-action="Index" method="get" class="query-form">
                <div class="query-row">
                    <div class="query-item query-item-search">
                        <label>搜索:</label>
                        <div class="input-group input-group-sm">
                            <input type="text" name="searchTerm" value="@Model.SearchTerm" class="form-control" placeholder="款项名称、编号、订单名称...">
                            <div class="input-group-append">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                                @if (!string.IsNullOrEmpty(Model.SearchTerm))
                                {
                                    <a asp-action="Index" class="btn btn-outline-danger" title="清除">
                                        <i class="fas fa-times"></i>
                                    </a>
                                }
                            </div>
                        </div>
                    </div>
                    <div class="query-item">
                        <label>类型:</label>
                        <select name="paymentType" class="form-control form-control-sm">
                            <option value="">全部类型</option>
                            <option value="首付" selected="@(Model.PaymentType == "首付")">首付</option>
                            <option value="2付" selected="@(Model.PaymentType == "2付")">2付</option>
                            <option value="3付" selected="@(Model.PaymentType == "3付")">3付</option>
                            <option value="4付" selected="@(Model.PaymentType == "4付")">4付</option>
                            <option value="5付" selected="@(Model.PaymentType == "5付")">5付</option>
                            <option value="尾款" selected="@(Model.PaymentType == "尾款")">尾款</option>
                            <option value="全款" selected="@(Model.PaymentType == "全款")">全款</option>
                        </select>
                    </div>
                    <div class="query-item">
                        <label>开票:</label>
                        <select name="invoiceStatus" class="form-control form-control-sm">
                            <option value="">全部状态</option>
                            <option value="未开票" selected="@(Model.InvoiceStatus == "未开票")">未开票</option>
                            <option value="已开票" selected="@(Model.InvoiceStatus == "已开票")">已开票</option>
                            <option value="已寄出" selected="@(Model.InvoiceStatus == "已寄出")">已寄出</option>
                        </select>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
        <div class="action-left">
            <div class="btn-group btn-group-sm" role="group">
                <a asp-action="Create" class="btn btn-primary">
                    <i class="fas fa-plus"></i> 新增
                </a>
                <button type="button" class="btn btn-secondary" id="batchOperationBtn" disabled>
                    <i class="fas fa-cogs"></i> 批量操作
                </button>
            </div>
        </div>
        <div class="action-right">
            <div class="view-options">
                <span class="text-muted">每页:</span>
                <select class="form-control form-control-sm d-inline-block" style="width: auto;" onchange="changePageSize(this.value)">
                    <option value="25" selected="@(Model.PageSize == 25)">25</option>
                    <option value="50" selected="@(Model.PageSize == 50)">50</option>
                    <option value="100" selected="@(Model.PageSize == 100)">100</option>
                </select>
            </div>
        </div>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
        @if (Model.Payments.Any())
        {
            <div class="table-container">
                <table class="table table-compact">
                    <thead class="table-header-fixed">
                        <tr>
                            <th width="35">
                                <div class="form-check form-check-sm">
                                    <input type="checkbox" class="form-check-input" id="selectAll">
                                    <label class="form-check-label" for="selectAll"></label>
                                </div>
                            </th>
                            <th width="160">款项信息</th>
                            <th width="140">关联订单</th>
                            <th width="80">类型</th>
                            <th width="100">金额</th>
                            <th width="100">回款状态</th>
                            <th width="80">开票状态</th>
                            <th width="100">客户经理</th>
                            <th width="90">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var payment in Model.Payments)
                        {
                            <tr class="table-row">
                                <td>
                                    <div class="form-check form-check-sm">
                                        <input type="checkbox" class="form-check-input payment-checkbox" id="<EMAIL>" value="@payment.Id">
                                        <label class="form-check-label" for="<EMAIL>"></label>
                                    </div>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 140px;" title="@payment.Name">
                                        <strong>@payment.Name</strong>
                                    </div>
                                    <small class="text-muted">@payment.Code</small>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 120px;" title="@payment.OrderName">
                                        <strong class="text-info">@payment.OrderName</strong>
                                    </div>
                                    <small class="text-muted">@payment.CustomerName</small>
                                </td>
                                <td>
                                    <span class="badge badge-secondary badge-xs">@payment.PaymentType</span>
                                </td>
                                <td>
                                    <span class="badge badge-success badge-xs">
                                        @EnumHelper.FormatAmount(payment.Amount, payment.Currency)
                                    </span>
                                </td>
                                <td>
                                    <span class="badge <EMAIL>(payment.PaymentStatus) badge-xs">
                                        @if (payment.PaymentStatus == "Pending")
                                        {
                                            <i class="fas fa-clock"></i>
                                        }
                                        else if (payment.PaymentStatus == "Partial")
                                        {
                                            <i class="fas fa-adjust"></i>
                                        }
                                        else if (payment.PaymentStatus == "Completed")
                                        {
                                            <i class="fas fa-check"></i>
                                        }
                                        else if (payment.PaymentStatus == "Overdue")
                                        {
                                            <i class="fas fa-exclamation-triangle"></i>
                                        }
                                        else
                                        {
                                            <i class="fas fa-question"></i>
                                        }
                                        @EnumHelper.GetPaymentStatusText(payment.PaymentStatus)
                                    </span>
                                </td>
                                <td>
                                    @switch (payment.InvoiceStatus)
                                    {
                                        case "未开票":
                                            <span class="badge badge-secondary badge-xs">
                                                <i class="fas fa-file"></i>
                                            </span>
                                            break;
                                        case "已开票":
                                            <span class="badge badge-info badge-xs">
                                                <i class="fas fa-file-invoice"></i>
                                            </span>
                                            break;
                                        case "已寄出":
                                            <span class="badge badge-success badge-xs">
                                                <i class="fas fa-paper-plane"></i>
                                            </span>
                                            break;
                                        default:
                                            <span class="badge badge-light badge-xs">?</span>
                                            break;
                                    }
                                </td>
                                <td class="text-nowrap">@payment.AccountManagerName</td>
                                <td>
                                    <div class="btn-group btn-group-xs" role="group">
                                        <a asp-action="Details" asp-route-id="@payment.Id" class="btn btn-outline-info btn-xs" title="查看">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a asp-action="Edit" asp-route-id="@payment.Id" class="btn btn-outline-warning btn-xs" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-danger btn-xs" title="删除"
                                                onclick="confirmDelete(@payment.Id, '@payment.Name')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="empty-state">
                <div class="empty-state-content">
                    <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无款项数据</h5>
                    @if (!string.IsNullOrEmpty(Model.SearchTerm) || !string.IsNullOrEmpty(Model.PaymentType) || !string.IsNullOrEmpty(Model.InvoiceStatus))
                    {
                        <p class="text-muted">没有找到符合条件的款项</p>
                        <a asp-action="Index" class="btn btn-secondary">清除筛选</a>
                    }
                    else
                    {
                        <a asp-action="Create" class="btn btn-primary">创建第一个款项</a>
                    }
                </div>
            </div>
        }
    </div>

    <!-- 分页栏 -->
    @if (Model.TotalCount > 0)
    {
        <div class="pagination-bar">
            <div class="pagination-info">
                <small class="text-muted">
                    第 @((Model.PageIndex - 1) * Model.PageSize + 1)-@Math.Min(Model.PageIndex * Model.PageSize, Model.TotalCount) 条，共 @Model.TotalCount 条
                </small>
            </div>
            @if (Model.TotalCount > Model.PageSize)
            {
                var totalPages = (int)Math.Ceiling((double)Model.TotalCount / Model.PageSize);
                var startPage = Math.Max(1, Model.PageIndex - 2);
                var endPage = Math.Min(totalPages, Model.PageIndex + 2);
                <nav class="pagination-nav">
                    <ul class="pagination pagination-sm m-0">
                        @if (Model.PageIndex > 1)
                        {
                            <li class="page-item">
                                <a class="page-link" asp-action="Index" asp-route-pageIndex="@(Model.PageIndex - 1)" asp-route-searchTerm="@Model.SearchTerm" asp-route-paymentType="@Model.PaymentType" asp-route-invoiceStatus="@Model.InvoiceStatus" asp-route-pageSize="@Model.PageSize">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        }

                        @for (int i = startPage; i <= endPage; i++)
                        {
                            <li class="page-item @(i == Model.PageIndex ? "active" : "")">
                                <a class="page-link" asp-action="Index" asp-route-pageIndex="@i" asp-route-searchTerm="@Model.SearchTerm" asp-route-paymentType="@Model.PaymentType" asp-route-invoiceStatus="@Model.InvoiceStatus" asp-route-pageSize="@Model.PageSize">@i</a>
                            </li>
                        }

                        @if (Model.PageIndex < totalPages)
                        {
                            <li class="page-item">
                                <a class="page-link" asp-action="Index" asp-route-pageIndex="@(Model.PageIndex + 1)" asp-route-searchTerm="@Model.SearchTerm" asp-route-paymentType="@Model.PaymentType" asp-route-invoiceStatus="@Model.InvoiceStatus" asp-route-pageSize="@Model.PageSize">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        }
                    </ul>
                </nav>
            }
        </div>
    }
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>您确定要删除款项 <strong id="deletePaymentName"></strong> 吗？</p>
                <p class="text-danger"><small>删除款项前请确保该款项下没有发票记录！</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(paymentId, paymentName) {
            $('#deletePaymentName').text(paymentName);
            $('#deleteForm').attr('action', '@Url.Action("Delete")/' + paymentId);
            $('#deleteModal').modal('show');
        }

        // 查询区收缩切换
        function toggleQuerySection() {
            const queryContent = document.getElementById('queryContent');
            const queryToggleIcon = document.getElementById('queryToggleIcon');
            const isCollapsed = queryContent.style.display === 'none';

            if (isCollapsed) {
                queryContent.style.display = 'block';
                queryToggleIcon.className = 'fas fa-chevron-up';
                localStorage.setItem('querySection_collapsed', 'false');
            } else {
                queryContent.style.display = 'none';
                queryToggleIcon.className = 'fas fa-chevron-down';
                localStorage.setItem('querySection_collapsed', 'true');
            }
        }

        // 分页大小切换
        function changePageSize(pageSize) {
            var url = new URL(window.location);
            url.searchParams.set('pageSize', pageSize);
            url.searchParams.set('pageIndex', '1');
            window.location.href = url.toString();
        }

        $(document).ready(function() {
            // 恢复查询区收缩状态
            const isCollapsed = localStorage.getItem('querySection_collapsed') === 'true';
            if (isCollapsed) {
                document.getElementById('queryContent').style.display = 'none';
                document.getElementById('queryToggleIcon').className = 'fas fa-chevron-down';
            }

            // 全选/取消全选
            $('#selectAll').change(function() {
                $('.payment-checkbox').prop('checked', this.checked);
                updateBatchOperationButton();
            });

            // 单个复选框变化
            $('.payment-checkbox').change(function() {
                var totalCheckboxes = $('.payment-checkbox').length;
                var checkedCheckboxes = $('.payment-checkbox:checked').length;

                $('#selectAll').prop('checked', totalCheckboxes === checkedCheckboxes);
                updateBatchOperationButton();
            });

            // 更新批量操作按钮状态
            function updateBatchOperationButton() {
                var checkedCount = $('.payment-checkbox:checked').length;
                $('#batchOperationBtn').prop('disabled', checkedCount === 0);

                if (checkedCount > 0) {
                    $('#batchOperationBtn').html('<i class="fas fa-cogs"></i> 批量操作 (' + checkedCount + ')');
                } else {
                    $('#batchOperationBtn').html('<i class="fas fa-cogs"></i> 批量操作');
                }
            }

            // 批量操作点击事件
            $('#batchOperationBtn').click(function() {
                var selectedIds = $('.payment-checkbox:checked').map(function() {
                    return this.value;
                }).get();

                if (selectedIds.length > 0) {
                    alert('选中了 ' + selectedIds.length + ' 个款项\nID: ' + selectedIds.join(', '));
                }
            });

            // 表格行点击选择
            $('tbody tr').click(function(e) {
                if (e.target.type !== 'checkbox' &&
                    !$(e.target).closest('.btn').length &&
                    !$(e.target).closest('a').length &&
                    !$(e.target).closest('.badge').length) {
                    var checkbox = $(this).find('.payment-checkbox');
                    checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
                }
            });

            // 表格行悬停效果
            $('tbody tr').hover(
                function() {
                    $(this).addClass('table-row-hover');
                },
                function() {
                    $(this).removeClass('table-row-hover');
                }
            );
        });

        // 显示提示消息
        @if (TempData["SuccessMessage"] != null)
        {
            <text>
            toastr.success('@TempData["SuccessMessage"]');
            </text>
        }

        @if (TempData["ErrorMessage"] != null)
        {
            <text>
            toastr.error('@TempData["ErrorMessage"]');
            </text>
        }
    </script>
}
