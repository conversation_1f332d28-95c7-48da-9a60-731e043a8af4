namespace ZXCRM.Service.DTOs
{
    // 仪表盘统计数据
    public class DashboardStatsDTO
    {
        public int TotalOpportunities { get; set; }
        public int WonOpportunities { get; set; }
        public decimal OpportunityWinRate { get; set; }
        
        public int TotalOrders { get; set; }
        public decimal TotalOrderAmount { get; set; }
        public decimal MonthlyOrderAmount { get; set; }
        
        public int TotalPayments { get; set; }
        public decimal TotalPaymentAmount { get; set; }
        public decimal MonthlyPaymentAmount { get; set; }
        
        public int TotalInvoices { get; set; }
        public decimal TotalInvoiceAmount { get; set; }
        public decimal MonthlyInvoiceAmount { get; set; }
    }

    // 商机统计报表
    public class OpportunityReportDTO
    {
        public List<OpportunityStatusStatsDTO> StatusStats { get; set; } = new();
        public List<MonthlyOpportunityStatsDTO> MonthlyStats { get; set; } = new();
        public List<UserOpportunityStatsDTO> UserStats { get; set; } = new();
        public decimal OverallWinRate { get; set; }
        public int TotalOpportunities { get; set; }
    }

    public class OpportunityStatusStatsDTO
    {
        public string Status { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal Percentage { get; set; }
    }

    public class MonthlyOpportunityStatsDTO
    {
        public string Month { get; set; } = string.Empty;
        public int TotalCount { get; set; }
        public int WonCount { get; set; }
        public decimal WinRate { get; set; }
    }

    public class UserOpportunityStatsDTO
    {
        public string UserName { get; set; } = string.Empty;
        public int TotalCount { get; set; }
        public int WonCount { get; set; }
        public decimal WinRate { get; set; }
    }

    // 订单统计报表
    public class OrderReportDTO
    {
        public List<MonthlyOrderStatsDTO> MonthlyStats { get; set; } = new();
        public List<CurrencyOrderStatsDTO> CurrencyStats { get; set; } = new();
        public List<DepartmentOrderStatsDTO> DepartmentStats { get; set; } = new();
        public List<UserOrderStatsDTO> UserStats { get; set; } = new();
        public decimal TotalAmount { get; set; }
        public int TotalCount { get; set; }
    }

    public class MonthlyOrderStatsDTO
    {
        public string Month { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal Amount { get; set; }
        public decimal AverageAmount { get; set; }
    }

    public class CurrencyOrderStatsDTO
    {
        public string Currency { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal Amount { get; set; }
        public decimal Percentage { get; set; }
    }

    public class DepartmentOrderStatsDTO
    {
        public string DepartmentName { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal Amount { get; set; }
        public decimal Percentage { get; set; }
    }

    public class UserOrderStatsDTO
    {
        public string UserName { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal Amount { get; set; }
        public decimal AverageAmount { get; set; }
    }

    // 款项统计报表
    public class PaymentReportDTO
    {
        public List<MonthlyPaymentStatsDTO> MonthlyStats { get; set; } = new();
        public List<PaymentTypeStatsDTO> TypeStats { get; set; } = new();
        public List<CurrencyPaymentStatsDTO> CurrencyStats { get; set; } = new();
        public decimal TotalAmount { get; set; }
        public int TotalCount { get; set; }
        public decimal CollectionRate { get; set; }
    }

    public class MonthlyPaymentStatsDTO
    {
        public string Month { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal Amount { get; set; }
        public decimal CollectionRate { get; set; }
    }

    public class PaymentTypeStatsDTO
    {
        public string Type { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal Amount { get; set; }
        public decimal Percentage { get; set; }
    }

    public class CurrencyPaymentStatsDTO
    {
        public string Currency { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal Amount { get; set; }
        public decimal Percentage { get; set; }
    }

    // 发票统计报表
    public class InvoiceReportDTO
    {
        public List<MonthlyInvoiceStatsDTO> MonthlyStats { get; set; } = new();
        public List<InvoiceTypeStatsDTO> TypeStats { get; set; } = new();
        public List<InvoiceCompanyStatsDTO> CompanyStats { get; set; } = new();
        public List<TaxRateStatsDTO> TaxRateStats { get; set; } = new();
        public decimal TotalAmount { get; set; }
        public int TotalCount { get; set; }
        public decimal TotalTaxAmount { get; set; }
    }

    public class MonthlyInvoiceStatsDTO
    {
        public string Month { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal Amount { get; set; }
        public decimal TaxAmount { get; set; }
    }

    public class InvoiceTypeStatsDTO
    {
        public string Type { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal Amount { get; set; }
        public decimal Percentage { get; set; }
    }

    public class InvoiceCompanyStatsDTO
    {
        public string Company { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal Amount { get; set; }
        public decimal Percentage { get; set; }
    }

    public class TaxRateStatsDTO
    {
        public decimal TaxRate { get; set; }
        public int Count { get; set; }
        public decimal Amount { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal Percentage { get; set; }
    }

    // 报表查询参数
    public class ReportQueryDTO
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int? DepartmentId { get; set; }
        public int? UserId { get; set; }
        public string? Currency { get; set; }
        public string? Status { get; set; }
    }
}
