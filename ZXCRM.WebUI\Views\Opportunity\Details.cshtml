@model ZXCRM.WebUI.Models.ViewModels.OpportunityDetailViewModel
@{
    ViewData["Title"] = "商机详情";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">商机详情</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a asp-controller="Dashboard" asp-action="Index">首页</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Opportunity" asp-action="Index">商机管理</a></li>
                    <li class="breadcrumb-item active">商机详情</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <!-- 基本信息 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-seedling"></i> 商机基本信息
                        </h3>
                        <div class="card-tools">
                            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning btn-sm">
                                <i class="fas fa-edit"></i> 编辑
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">商机名称:</dt>
                                    <dd class="col-sm-8"><strong>@Model.Name</strong></dd>
                                    
                                    <dt class="col-sm-4">客户名称:</dt>
                                    <dd class="col-sm-8">
                                        <span class="badge bg-info badge-lg text-white">@Model.CustomerName</span>
                                    </dd>

                                    <dt class="col-sm-4">商机状态:</dt>
                                    <dd class="col-sm-8">
                                        <span class="badge bg-@(ZXCRM.WebUI.Models.ViewModels.OpportunityStatus.GetStatusColor(Model.Status)) badge-lg text-white">
                                            <i class="@(ZXCRM.WebUI.Models.ViewModels.OpportunityStatus.GetStatusIcon(Model.Status))"></i>
                                            @Model.Status
                                        </span>
                                    </dd>

                                    <dt class="col-sm-4">创建人:</dt>
                                    <dd class="col-sm-8">
                                        <span class="badge bg-primary text-white">@Model.CreatedByName</span>
                                    </dd>
                                </dl>
                            </div>
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">联系人:</dt>
                                    <dd class="col-sm-8">
                                        @if (!string.IsNullOrEmpty(Model.ContactName))
                                        {
                                            @Model.ContactName
                                        }
                                        else
                                        {
                                            <span class="text-muted">未设置</span>
                                        }
                                    </dd>
                                    
                                    <dt class="col-sm-4">联系电话:</dt>
                                    <dd class="col-sm-8">
                                        @if (!string.IsNullOrEmpty(Model.ContactPhone))
                                        {
                                            <a href="tel:@Model.ContactPhone">@Model.ContactPhone</a>
                                        }
                                        else
                                        {
                                            <span class="text-muted">未设置</span>
                                        }
                                    </dd>
                                    
                                    <dt class="col-sm-4">创建时间:</dt>
                                    <dd class="col-sm-8">@Model.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss")</dd>
                                    
                                    <dt class="col-sm-4">最后更新:</dt>
                                    <dd class="col-sm-8">@Model.UpdatedAt.ToString("yyyy-MM-dd HH:mm:ss")</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 商机内容 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-file-alt"></i> 商机内容
                        </h3>
                    </div>
                    <div class="card-body">
                        @if (!string.IsNullOrEmpty(Model.Content))
                        {
                            <div class="content-text">
                                @Html.Raw(Model.Content.Replace("\n", "<br>"))
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-3">
                                <i class="fas fa-file-alt fa-2x text-muted mb-2"></i>
                                <p class="text-muted">暂无商机内容描述</p>
                                <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-plus"></i> 添加商机内容
                                </a>
                            </div>
                        }
                    </div>
                </div>

                <!-- 关联订单 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-shopping-cart"></i> 关联订单
                        </h3>
                        <div class="card-tools">
                            @if (Model.Status == "成交" && !Model.RelatedOrders.Any())
                            {
                                <a asp-controller="Order" asp-action="Create" asp-route-opportunityId="@Model.Id" class="btn btn-success btn-sm">
                                    <i class="fas fa-plus"></i> 创建订单
                                </a>
                            }
                        </div>
                    </div>
                    <div class="card-body">
                        @if (Model.RelatedOrders.Any())
                        {
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>订单名称</th>
                                            <th>订单编号</th>
                                            <th>订单金额</th>
                                            <th>订单状态</th>
                                            <th>创建时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var order in Model.RelatedOrders)
                                        {
                                            <tr>
                                                <td>
                                                    <a asp-controller="Order" asp-action="Details" asp-route-id="@order.Id" class="text-decoration-none">
                                                        <strong>@order.Name</strong>
                                                    </a>
                                                </td>
                                                <td><code>@order.Code</code></td>
                                                <td>
                                                    <span class="badge bg-success text-white">
                                                        @order.Amount.ToString("N2") @order.Currency
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info text-white">@order.Status</span>
                                                </td>
                                                <td>@order.CreatedAt.ToString("yyyy-MM-dd")</td>
                                                <td>
                                                    <a asp-controller="Order" asp-action="Details" asp-route-id="@order.Id" class="btn btn-info btn-xs">
                                                        <i class="fas fa-eye"></i> 查看
                                                    </a>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-3">
                                <i class="fas fa-shopping-cart fa-2x text-muted mb-2"></i>
                                <p class="text-muted">暂无关联订单</p>
                                @if (Model.Status == "成交")
                                {
                                    <a asp-controller="Order" asp-action="Create" asp-route-opportunityId="@Model.Id" class="btn btn-outline-success btn-sm">
                                        <i class="fas fa-plus"></i> 创建订单
                                    </a>
                                }
                                else
                                {
                                    <small class="text-muted">商机成交后可创建订单</small>
                                }
                            </div>
                        }
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <!-- 操作面板 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-tools"></i> 操作
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="btn-group-vertical btn-block">
                            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning">
                                <i class="fas fa-edit"></i> 编辑商机
                            </a>
                            @if (!Model.RelatedOrders.Any())
                            {
                                <button type="button" class="btn btn-danger" onclick="confirmDelete(@Model.Id, '@Model.Name')">
                                    <i class="fas fa-trash"></i> 删除商机
                                </button>
                            }
                            else
                            {
                                <button type="button" class="btn btn-secondary" disabled title="已有关联订单，不可删除">
                                    <i class="fas fa-lock"></i> 不可删除
                                </button>
                            }
                            <div class="dropdown-divider"></div>
                            @if (Model.Status != "成交" && Model.Status != "失败" && Model.Status != "取消")
                            {
                                <button type="button" class="btn btn-success" onclick="markAsWon()">
                                    <i class="fas fa-trophy"></i> 标记成交
                                </button>
                                <button type="button" class="btn btn-outline-danger" onclick="markAsLost()">
                                    <i class="fas fa-times-circle"></i> 标记失败
                                </button>
                            }
                            @if (Model.Status == "成交" && !Model.RelatedOrders.Any())
                            {
                                <a asp-controller="Order" asp-action="Create" asp-route-opportunityId="@Model.Id" class="btn btn-primary">
                                    <i class="fas fa-shopping-cart"></i> 创建订单
                                </a>
                            }
                        </div>
                    </div>
                </div>

                <!-- 商机进度 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-line"></i> 商机进度
                        </h3>
                    </div>
                    <div class="card-body">
                        @{
                            var progressPercentage = Model.Status switch
                            {
                                "潜在" => 10,
                                "合格" => 25,
                                "方案" => 50,
                                "谈判" => 75,
                                "成交" => 100,
                                "失败" => 0,
                                "取消" => 0,
                                _ => 0
                            };
                            var progressClass = Model.Status switch
                            {
                                "成交" => "bg-success",
                                "失败" or "取消" => "bg-danger",
                                _ => "bg-primary"
                            };
                        }
                        
                        <div class="progress mb-3">
                            <div class="progress-bar @progressClass" role="progressbar" style="width: @progressPercentage%" 
                                 aria-valuenow="@progressPercentage" aria-valuemin="0" aria-valuemax="100">
                                @progressPercentage%
                            </div>
                        </div>
                        
                        <div class="timeline">
                            <div class="time-label">
                                <span class="bg-info">商机历程</span>
                            </div>
                            <div>
                                <i class="fas fa-seedling bg-green"></i>
                                <div class="timeline-item">
                                    <span class="time"><i class="fas fa-clock"></i> @Model.CreatedAt.ToString("MM-dd HH:mm")</span>
                                    <h3 class="timeline-header">商机创建</h3>
                                    <div class="timeline-body">
                                        商机"@Model.Name"已创建，当前状态：@Model.Status
                                    </div>
                                </div>
                            </div>
                            @if (Model.UpdatedAt != Model.CreatedAt)
                            {
                                <div>
                                    <i class="fas fa-edit bg-blue"></i>
                                    <div class="timeline-item">
                                        <span class="time"><i class="fas fa-clock"></i> @Model.UpdatedAt.ToString("MM-dd HH:mm")</span>
                                        <h3 class="timeline-header">最近更新</h3>
                                        <div class="timeline-body">
                                            商机信息已更新
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>

                <!-- 快捷导航 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-compass"></i> 快捷导航
                        </h3>
                    </div>
                    <div class="card-body">
                        <a asp-action="Index" class="btn btn-outline-primary btn-block">
                            <i class="fas fa-list"></i> 返回商机列表
                        </a>
                        <a asp-action="Create" class="btn btn-outline-success btn-block">
                            <i class="fas fa-plus"></i> 新增商机
                        </a>
                        @if (Model.RelatedOrders.Any())
                        {
                            <a asp-controller="Order" asp-action="Index" class="btn btn-outline-info btn-block">
                                <i class="fas fa-shopping-cart"></i> 查看所有订单
                            </a>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>您确定要删除商机 <strong id="deleteOpportunityName"></strong> 吗？</p>
                <p class="text-danger"><small>删除商机前请确保该商机下没有关联订单！</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(opportunityId, opportunityName) {
            $('#deleteOpportunityName').text(opportunityName);
            $('#deleteForm').attr('action', '@Url.Action("Delete")/' + opportunityId);
            $('#deleteModal').modal('show');
        }

        function markAsWon() {
            if (confirm('确定要标记此商机为成交吗？')) {
                // 这里可以添加AJAX调用来更新商机状态
                toastr.info('功能开发中...');
            }
        }

        function markAsLost() {
            if (confirm('确定要标记此商机为失败吗？')) {
                // 这里可以添加AJAX调用来更新商机状态
                toastr.info('功能开发中...');
            }
        }
    </script>
}

@section Styles {
    <style>
        .badge-lg {
            font-size: 0.9em;
            padding: 0.5em 0.75em;
        }
        
        .content-text {
            line-height: 1.6;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }

        .timeline {
            position: relative;
            margin: 0 0 30px 0;
            padding: 0;
            list-style: none;
        }

        .timeline:before {
            content: '';
            position: absolute;
            top: 0;
            left: 25px;
            height: 100%;
            width: 2px;
            background: #dee2e6;
        }

        .timeline > div {
            position: relative;
            margin-right: 10px;
            margin-bottom: 15px;
        }

        .timeline > div > .timeline-item {
            box-shadow: 0 1px 1px rgba(0,0,0,0.1);
            border-radius: 3px;
            margin-top: 0;
            background: #fff;
            color: #444;
            margin-left: 60px;
            margin-right: 15px;
            padding: 10px;
            position: relative;
        }

        .timeline > div > .fas {
            width: 30px;
            height: 30px;
            font-size: 15px;
            line-height: 30px;
            position: absolute;
            color: #666;
            background: #f4f4f4;
            border-radius: 50%;
            text-align: center;
            left: 18px;
            top: 0;
        }
    </style>
}
