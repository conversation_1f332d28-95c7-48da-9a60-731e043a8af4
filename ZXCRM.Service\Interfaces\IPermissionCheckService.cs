using ZXCRM.Service.DTOs;

namespace ZXCRM.Service.Interfaces
{
    public interface IPermissionCheckService
    {
        /// <summary>
        /// 检查用户是否有特定权限
        /// </summary>
        Task<bool> HasPermissionAsync(int userId, string moduleType, string permissionCode);

        /// <summary>
        /// 检查用户是否有模块访问权限（至少有一个权限）
        /// </summary>
        Task<bool> HasModuleAccessAsync(int userId, string moduleType);

        /// <summary>
        /// 获取用户有权限的模块列表
        /// </summary>
        Task<List<string>> GetUserModulesAsync(int userId);

        /// <summary>
        /// 获取用户的完整权限映射
        /// </summary>
        Task<Dictionary<string, List<string>>> GetUserPermissionsAsync(int userId);

        /// <summary>
        /// 判断是否是系统模块
        /// </summary>
        bool IsSystemModule(string moduleType);

        /// <summary>
        /// 判断是否是业务模块
        /// </summary>
        bool IsBusinessModule(string moduleType);

        /// <summary>
        /// 获取用户权限概览
        /// </summary>
        Task<UserPermissionOverviewDto> GetUserPermissionOverviewAsync(int userId);
    }
}
