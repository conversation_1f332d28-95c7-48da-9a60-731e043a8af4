@model ZXCRM.WebUI.Models.ViewModels.CreateUserViewModel
@{
    ViewData["Title"] = "新增用户";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">新增用户</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a asp-controller="Dashboard" asp-action="Index">首页</a></li>
                    <li class="breadcrumb-item"><a asp-controller="User" asp-action="Index">用户管理</a></li>
                    <li class="breadcrumb-item active">新增用户</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-user-plus"></i> 用户信息
                        </h3>
                    </div>
                    <form asp-action="Create" method="post">
                        <div class="card-body">
                            <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>

                            <div class="row">
                                <div class="col-md-6">
                                    <!-- 用户名 -->
                                    <div class="form-group">
                                        <label asp-for="Username" class="form-label required"></label>
                                        <input asp-for="Username" class="form-control" placeholder="请输入用户名" />
                                        <span asp-validation-for="Username" class="text-danger"></span>
                                        <small class="form-text text-muted">用户名用于登录，不能重复</small>
                                    </div>

                                    <!-- 姓名 -->
                                    <div class="form-group">
                                        <label asp-for="Name" class="form-label required"></label>
                                        <input asp-for="Name" class="form-control" placeholder="请输入真实姓名" />
                                        <span asp-validation-for="Name" class="text-danger"></span>
                                    </div>

                                    <!-- 性别 -->
                                    <div class="form-group">
                                        <label asp-for="Gender" class="form-label"></label>
                                        <select asp-for="Gender" class="form-control">
                                            <option value="">请选择性别</option>
                                            <option value="男">男</option>
                                            <option value="女">女</option>
                                        </select>
                                        <span asp-validation-for="Gender" class="text-danger"></span>
                                    </div>

                                    <!-- 邮箱 -->
                                    <div class="form-group">
                                        <label asp-for="Email" class="form-label"></label>
                                        <input asp-for="Email" class="form-control" placeholder="请输入邮箱地址" />
                                        <span asp-validation-for="Email" class="text-danger"></span>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <!-- 电话 -->
                                    <div class="form-group">
                                        <label asp-for="Phone" class="form-label"></label>
                                        <input asp-for="Phone" class="form-control" placeholder="请输入手机号码" />
                                        <span asp-validation-for="Phone" class="text-danger"></span>
                                    </div>

                                    <!-- 部门 -->
                                    <div class="form-group">
                                        <label asp-for="DepartmentId" class="form-label required"></label>
                                        <select asp-for="DepartmentId" class="form-control">
                                            <option value="">请选择部门</option>
                                            @foreach (var dept in Model.Departments)
                                            {
                                                <option value="@dept.Id">@dept.Name</option>
                                            }
                                        </select>
                                        <span asp-validation-for="DepartmentId" class="text-danger"></span>
                                    </div>

                                    <!-- 角色 -->
                                    <div class="form-group">
                                        <label asp-for="Role" class="form-label required"></label>
                                        <select asp-for="Role" class="form-control">
                                            <option value="">请选择角色</option>
                                            @foreach (var role in Model.Roles)
                                            {
                                                <option value="@role.Value">@role.Text</option>
                                            }
                                        </select>
                                        <span asp-validation-for="Role" class="text-danger"></span>
                                    </div>

                                    <!-- 状态 -->
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                                            <label asp-for="IsActive" class="form-check-label"></label>
                                        </div>
                                        <span asp-validation-for="IsActive" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <div class="row">
                                <div class="col-md-6">
                                    <!-- 密码 -->
                                    <div class="form-group">
                                        <label asp-for="Password" class="form-label required"></label>
                                        <div class="input-group">
                                            <input asp-for="Password" class="form-control" type="password" placeholder="请输入密码" />
                                            <div class="input-group-append">
                                                <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('Password')">
                                                    <i class="fas fa-eye" id="Password-eye"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <span asp-validation-for="Password" class="text-danger"></span>
                                        <small class="form-text text-muted">密码长度至少6位</small>
                                        <div id="passwordStrength" class="mt-1" style="display: none;">
                                            <div class="progress" style="height: 5px;">
                                                <div id="strengthBar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                                            </div>
                                            <small id="strengthText" class="form-text"></small>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <!-- 确认密码 -->
                                    <div class="form-group">
                                        <label asp-for="ConfirmPassword" class="form-label required"></label>
                                        <div class="input-group">
                                            <input asp-for="ConfirmPassword" class="form-control" type="password" placeholder="请再次输入密码" />
                                            <div class="input-group-append">
                                                <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('ConfirmPassword')">
                                                    <i class="fas fa-eye" id="ConfirmPassword-eye"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card-footer">
                            <div class="row">
                                <div class="col-md-6">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> 保存
                                    </button>
                                    <a asp-action="Index" class="btn btn-secondary ml-2">
                                        <i class="fas fa-times"></i> 取消
                                    </a>
                                </div>
                                <div class="col-md-6 text-right">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        标有 <span class="text-danger">*</span> 的字段为必填项
                                    </small>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="col-md-4">
                <!-- 帮助信息 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-question-circle"></i> 填写说明
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb"></i> 提示</h6>
                            <ul class="mb-0">
                                <li>用户名必须唯一，建议使用英文或数字</li>
                                <li>密码长度至少6位，建议包含字母和数字</li>
                                <li>邮箱地址用于系统通知和密码重置</li>
                                <li>部门信息影响用户的数据访问权限</li>
                                <li>新用户默认为激活状态</li>
                            </ul>
                        </div>

                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> 注意</h6>
                            <p class="mb-0">
                                创建用户后，系统会自动分配基础查询权限。
                                如需更多权限，请联系系统管理员。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        function togglePassword(fieldId) {
            const field = document.getElementById(fieldId);
            const eye = document.getElementById(fieldId + '-eye');

            if (field.type === 'password') {
                field.type = 'text';
                eye.classList.remove('fa-eye');
                eye.classList.add('fa-eye-slash');
            } else {
                field.type = 'password';
                eye.classList.remove('fa-eye-slash');
                eye.classList.add('fa-eye');
            }
        }

        // 表单验证增强
        $(document).ready(function() {
            // 用户名实时验证
            $('#Username').on('blur', function() {
                const username = $(this).val();
                if (username.length > 0 && username.length < 3) {
                    $(this).addClass('is-invalid');
                    $(this).next('.text-danger').text('用户名长度至少3位');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });

            // 密码强度提示
            $('#Password').on('input', function() {
                const password = $(this).val();
                const strength = checkPasswordStrength(password);
                updatePasswordStrength(strength);
            });
        });

        function checkPasswordStrength(password) {
            let strength = 0;
            if (password.length >= 6) strength++;
            if (password.match(/[a-z]/)) strength++;
            if (password.match(/[A-Z]/)) strength++;
            if (password.match(/[0-9]/)) strength++;
            if (password.match(/[^a-zA-Z0-9]/)) strength++;
            return strength;
        }

        function updatePasswordStrength(strength) {
            const strengthText = ['很弱', '弱', '一般', '强', '很强'];
            const strengthColor = ['bg-danger', 'bg-warning', 'bg-info', 'bg-success', 'bg-success'];
            const textColor = ['text-danger', 'text-warning', 'text-info', 'text-success', 'text-success'];

            const $strengthDiv = $('#passwordStrength');
            const $strengthBar = $('#strengthBar');
            const $strengthText = $('#strengthText');

            if (strength > 0) {
                $strengthDiv.show();

                const text = strengthText[strength - 1];
                const barColor = strengthColor[strength - 1];
                const textColorClass = textColor[strength - 1];
                const percentage = (strength / 5) * 100;

                $strengthBar.removeClass('bg-danger bg-warning bg-info bg-success')
                           .addClass(barColor)
                           .css('width', percentage + '%');

                $strengthText.removeClass('text-danger text-warning text-info text-success')
                            .addClass(textColorClass)
                            .text('密码强度: ' + text);
            } else {
                $strengthDiv.hide();
            }
        }
    </script>
}

@section Styles {
    <style>
        .required::after {
            content: " *";
            color: red;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .is-invalid {
            border-color: #dc3545;
        }
    </style>
}
