using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ZXCRM.Service.DTOs;
using ZXCRM.Service.Interface;
using System.Security.Claims;

namespace ZXCRM.WebAPI.Controllers
{
    [Authorize]
    public class ReportController : BaseController
    {
        private readonly IReportService _reportService;
        private readonly ILogger<ReportController> _logger;

        public ReportController(IReportService reportService, ILogger<ReportController> logger)
        {
            _reportService = reportService;
            _logger = logger;
        }

        [HttpGet("dashboard")]
        public async Task<IActionResult> GetDashboardStats([FromQuery] ReportQueryDTO? query = null)
        {
            try
            {
                _logger.LogInformation("Getting dashboard statistics");
                var stats = await _reportService.GetDashboardStatsAsync(query);
                return Success(stats, "获取仪表盘统计成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dashboard statistics");
                return Failure("获取仪表盘统计失败");
            }
        }

        [HttpGet("opportunity")]
        public async Task<IActionResult> GetOpportunityReport([FromQuery] ReportQueryDTO? query = null)
        {
            try
            {
                _logger.LogInformation("Getting opportunity report");
                
                // 如果没有指定用户ID，默认查询当前用户的数据
                if (query?.UserId == null)
                {
                    var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                    if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
                    {
                        if (query == null) query = new ReportQueryDTO();
                        query.UserId = userId;
                    }
                }
                
                var report = await _reportService.GetOpportunityReportAsync(query);
                return Success(report, "获取商机报表成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting opportunity report");
                return Failure("获取商机报表失败");
            }
        }

        [HttpGet("order")]
        public async Task<IActionResult> GetOrderReport([FromQuery] ReportQueryDTO? query = null)
        {
            try
            {
                _logger.LogInformation("Getting order report");
                var report = await _reportService.GetOrderReportAsync(query);
                return Success(report, "获取订单报表成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting order report");
                return Failure("获取订单报表失败");
            }
        }

        [HttpGet("payment")]
        public async Task<IActionResult> GetPaymentReport([FromQuery] ReportQueryDTO? query = null)
        {
            try
            {
                _logger.LogInformation("Getting payment report");
                var report = await _reportService.GetPaymentReportAsync(query);
                return Success(report, "获取款项报表成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payment report");
                return Failure("获取款项报表失败");
            }
        }

        [HttpGet("invoice")]
        public async Task<IActionResult> GetInvoiceReport([FromQuery] ReportQueryDTO? query = null)
        {
            try
            {
                _logger.LogInformation("Getting invoice report");
                var report = await _reportService.GetInvoiceReportAsync(query);
                return Success(report, "获取发票报表成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoice report");
                return Failure("获取发票报表失败");
            }
        }

        [HttpGet("export/{reportType}")]
        public async Task<IActionResult> ExportReport(string reportType, [FromQuery] ReportQueryDTO? query = null)
        {
            try
            {
                _logger.LogInformation("Exporting report: {ReportType}", reportType);
                
                var data = await _reportService.ExportReportAsync(reportType, query);
                
                if (data.Length == 0)
                {
                    return Failure("报表导出功能暂未实现");
                }
                
                var fileName = $"{reportType}_report_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                return File(data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting report: {ReportType}", reportType);
                return Failure("导出报表失败");
            }
        }

        [HttpGet("opportunity/all")]
        public async Task<IActionResult> GetAllOpportunityReport([FromQuery] ReportQueryDTO? query = null)
        {
            try
            {
                _logger.LogInformation("Getting all opportunity report (admin)");
                
                // 管理员可以查看所有用户的商机报表
                // 这里可以添加权限检查
                
                var report = await _reportService.GetOpportunityReportAsync(query);
                return Success(report, "获取所有商机报表成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all opportunity report");
                return Failure("获取所有商机报表失败");
            }
        }

        [HttpGet("summary")]
        public async Task<IActionResult> GetReportSummary([FromQuery] ReportQueryDTO? query = null)
        {
            try
            {
                _logger.LogInformation("Getting report summary");
                
                // 获取综合统计数据
                var dashboardStats = await _reportService.GetDashboardStatsAsync(query);
                var opportunityReport = await _reportService.GetOpportunityReportAsync(query);
                var orderReport = await _reportService.GetOrderReportAsync(query);
                
                var summary = new
                {
                    Dashboard = dashboardStats,
                    OpportunityOverview = new
                    {
                        opportunityReport.TotalOpportunities,
                        opportunityReport.OverallWinRate,
                        TopStatus = opportunityReport.StatusStats.FirstOrDefault()
                    },
                    OrderOverview = new
                    {
                        orderReport.TotalCount,
                        orderReport.TotalAmount,
                        TopCurrency = orderReport.CurrencyStats.FirstOrDefault()
                    }
                };
                
                return Success(summary, "获取报表摘要成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting report summary");
                return Failure("获取报表摘要失败");
            }
        }

        [HttpGet("trends")]
        public async Task<IActionResult> GetTrends([FromQuery] ReportQueryDTO? query = null)
        {
            try
            {
                _logger.LogInformation("Getting trends data");
                
                // 设置默认时间范围为最近12个月
                if (query == null)
                {
                    query = new ReportQueryDTO
                    {
                        StartDate = DateTime.Now.AddMonths(-12),
                        EndDate = DateTime.Now
                    };
                }
                
                var opportunityReport = await _reportService.GetOpportunityReportAsync(query);
                var orderReport = await _reportService.GetOrderReportAsync(query);
                var paymentReport = await _reportService.GetPaymentReportAsync(query);
                
                var trends = new
                {
                    OpportunityTrends = opportunityReport.MonthlyStats,
                    OrderTrends = orderReport.MonthlyStats,
                    PaymentTrends = paymentReport.MonthlyStats
                };
                
                return Success(trends, "获取趋势数据成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting trends data");
                return Failure("获取趋势数据失败");
            }
        }
    }
}
