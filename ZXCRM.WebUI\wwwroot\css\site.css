/* ZXCRM 新布局样式 */

/* 全局重置 */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Source Sans Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f8f9fa;
}

/* 主容器 */
.app-container {
    display: flex;
    height: 100vh;
    overflow: hidden;
}

/* 左侧导航栏 */
.sidebar-navigation {
    width: 180px;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    color: white;
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    z-index: 1000;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

/* Logo区域 */
.sidebar-header {
    padding: 15px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: bold;
    color: white;
}

.logo i {
    margin-right: 8px;
    color: #3498db;
}

.mobile-menu-close {
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    padding: 5px;
}

/* 用户信息区域 */
.sidebar-user {
    padding: 15px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    display: flex;
    align-items: center;
}

.user-avatar {
    width: 40px;
    height: 40px;
    margin-right: 10px;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.user-info {
    flex: 1;
}

.user-name {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 5px;
    color: white;
}

.user-actions {
    display: flex;
    gap: 5px;
}

.user-action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: rgba(255,255,255,0.1);
    border-radius: 4px;
    color: white;
    text-decoration: none;
    font-size: 12px;
    transition: background-color 0.2s;
}

.user-action-btn:hover {
    background: rgba(255,255,255,0.2);
    color: white;
    text-decoration: none;
}

/* Tab区域 */
.sidebar-tabs {
    padding: 10px 15px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.tab-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 5px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 13px;
}

.tab-item:hover {
    background: rgba(255,255,255,0.1);
}

.tab-item.active {
    background: #3498db;
    color: white;
}

.tab-item.hidden {
    display: none;
}

.tab-item i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
}

/* 导航菜单区域 */
.sidebar-menu {
    flex: 1;
    overflow-y: auto;
    padding: 10px 0;
}

.menu-group {
    display: none;
}

.menu-group.active {
    display: block;
}

.menu-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.menu-item {
    margin-bottom: 2px;
}

.menu-link {
    display: flex;
    align-items: center;
    padding: 10px 20px;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    font-size: 14px;
    transition: all 0.2s;
    border-left: 3px solid transparent;
}

.menu-link:hover {
    background: rgba(255,255,255,0.1);
    color: white;
    text-decoration: none;
    border-left-color: #3498db;
}

.menu-item.active .menu-link {
    background: rgba(52, 152, 219, 0.2);
    color: white;
    border-left-color: #3498db;
}

.menu-link i {
    margin-right: 12px;
    width: 18px;
    text-align: center;
    font-size: 16px;
}

/* 右侧内容区域 */
.main-content {
    flex: 1;
    margin-left: 180px;
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
    background: white;
}

/* 移动端菜单按钮区域 */
.mobile-header {
    padding: 10px 15px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    flex-shrink: 0;
}

.mobile-menu-toggle {
    background: none;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 8px 12px;
    color: #6c757d;
    cursor: pointer;
    font-size: 16px;
}

.mobile-menu-toggle:hover {
    background: #e9ecef;
    color: #495057;
}

/* 数据展示区域 */
.content-body {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;
}

/* 数据表格容器 */
.data-table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

/* 数据表格 */
.data-table {
    width: 100%;
    font-size: 14px;
    margin-bottom: 0;
}

.data-table thead th {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    padding: 12px 15px;
    color: #495057;
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table tbody tr {
    height: 45px;
    border-bottom: 1px solid #dee2e6;
    transition: background-color 0.2s;
}

.data-table tbody tr:hover {
    background-color: #f8f9fa;
}

.data-table tbody td {
    padding: 8px 15px;
    vertical-align: middle;
}

/* Buttons */
.btn {
    border-radius: .25rem;
    box-shadow: none;
    border: 1px solid transparent;
}

.btn:focus {
    box-shadow: none;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #004085;
}

/* Forms */
.form-control {
    border-radius: .25rem;
    box-shadow: none;
    border: 1px solid #ced4da;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* Tables */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
}

/* Alerts */
.alert {
    border-radius: .25rem;
    border: none;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* Media objects for activity list */
.media {
    display: flex;
    align-items: flex-start;
}

.media-object {
    flex-shrink: 0;
}

.media-body {
    flex: 1;
}

/* Breadcrumb */
.breadcrumb {
    background-color: transparent;
    margin-bottom: 0;
    padding: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: #6c757d;
}

/* Content wrapper */
.content-wrapper {
    background-color: #f4f6f9;
    min-height: calc(100vh - 57px);
}

/* Footer */
.main-footer {
    background-color: #fff;
    border-top: 1px solid #dee2e6;
    color: #869099;
    padding: 1rem;
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
    .small-box .icon > i {
        font-size: 50px;
        right: 10px;
        top: 10px;
    }

    .info-box {
        text-align: center;
        flex-direction: column;
    }

    .info-box .info-box-icon {
        width: 100%;
        margin-bottom: .5rem;
    }

    .info-box .info-box-content {
        margin-left: 0;
    }
}

/* Loading spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Custom utilities */
.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.shadow-sm {
    box-shadow: 0 .125rem .25rem rgba(0,0,0,.075) !important;
}

.shadow {
    box-shadow: 0 .5rem 1rem rgba(0,0,0,.15) !important;
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0,0,0,.175) !important;
}

/* 新布局移动端响应式样式 */
@media (max-width: 768px) {
    .sidebar-navigation {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar-navigation.show {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .mobile-menu-toggle {
        display: block !important;
    }

    .content-header {
        padding: 10px 15px;
    }

    .content-body {
        padding: 15px;
    }

    .page-title {
        font-size: 20px;
    }

    /* 移动端表格优化 */
    .data-table {
        font-size: 12px;
    }

    .data-table thead th,
    .data-table tbody td {
        padding: 6px 8px;
    }

    .data-table tbody tr {
        height: 40px;
    }

    /* 移动端用户信息区域 */
    .sidebar-user {
        padding: 12px 15px;
    }

    .user-avatar {
        width: 35px;
        height: 35px;
    }

    .user-name {
        font-size: 13px;
    }

    /* 移动端菜单项 */
    .menu-link {
        padding: 12px 20px;
        font-size: 15px;
    }

    .menu-link i {
        font-size: 18px;
    }
}

/* 侧边栏遮罩层 */
.sidebar-open::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    z-index: 999;
}

@media (min-width: 769px) {
    .sidebar-open::before {
        display: none;
    }

    .mobile-menu-toggle {
        display: none !important;
    }
}

/* 数据页面深度优化样式 */
.data-page-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #f8f9fa;
}

/* 查询区域样式 */
.query-section {
    background: white;
    border-bottom: 1px solid #dee2e6;
    flex-shrink: 0;
}

.query-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 15px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    cursor: pointer;
}

.query-title {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    color: #495057;
}

.query-title i {
    margin-right: 8px;
    color: #6c757d;
}

.query-toggle {
    background: none;
    border: none;
    color: #6c757d;
    font-size: 14px;
    cursor: pointer;
    padding: 4px;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.query-toggle:hover {
    background: #e9ecef;
}

.query-content {
    padding: 12px 15px;
}

.query-form {
    margin: 0;
}

.query-row {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.query-item {
    display: flex;
    align-items: center;
    gap: 6px;
}

.query-item label {
    font-size: 13px;
    color: #495057;
    margin: 0;
    white-space: nowrap;
}

.query-item-search {
    flex: 1;
    min-width: 200px;
}

/* 操作栏样式 */
.action-bar {
    background: white;
    padding: 8px 15px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.action-left {
    display: flex;
    align-items: center;
}

.action-right {
    display: flex;
    align-items: center;
}

.view-options {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
}

/* 表格区域样式 */
.table-section {
    flex: 1;
    overflow: hidden;
    background: white;
}

.table-container {
    height: 100%;
    overflow-y: auto;
}

/* 紧凑表格样式 */
.table-compact {
    margin: 0;
    font-size: 13px;
}

.table-compact thead th {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    padding: 8px 10px;
    color: #495057;
    position: sticky;
    top: 0;
    z-index: 10;
    font-size: 12px;
}

.table-compact tbody .table-row {
    height: 32px;
    border-bottom: 1px solid #dee2e6;
    transition: background-color 0.15s ease;
}

.table-compact tbody .table-row:hover {
    background-color: #f8f9fa;
    cursor: pointer;
}

.table-compact tbody td {
    padding: 4px 10px;
    vertical-align: middle;
    font-size: 12px;
    line-height: 1.2;
}

/* 分页栏样式 */
.pagination-bar {
    background: white;
    padding: 8px 15px;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.pagination-info {
    font-size: 12px;
}

.pagination-nav .pagination {
    margin: 0;
}

.pagination-nav .page-link {
    padding: 4px 8px;
    font-size: 12px;
}

/* 超小按钮和徽章样式 */
.btn-xs {
    padding: 1px 4px;
    font-size: 10px;
    line-height: 1.2;
    border-radius: 2px;
}

.btn-group-xs .btn {
    padding: 1px 3px;
    font-size: 10px;
}

.badge-xs {
    font-size: 9px;
    padding: 1px 4px;
}

.form-check-sm {
    font-size: 12px;
}

.form-check-sm .form-check-input {
    width: 14px;
    height: 14px;
}

.form-check-sm .form-check-label {
    padding-left: 18px;
}

/* 表格固定表头 */
.table-header-fixed {
    position: sticky;
    top: 0;
    z-index: 10;
}

/* 文本处理 */
.text-nowrap {
    white-space: nowrap;
}

/* 空状态优化 */
.empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;
    background: white;
}

.empty-state-content {
    text-align: center;
    padding: 30px;
}

/* 数据展示优化样式 */
.empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 400px;
    background: white;
}

.empty-state-content {
    text-align: center;
    padding: 40px;
}

/* 分页控件样式 */
.pagination-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.page-size-selector {
    display: flex;
    align-items: center;
    gap: 8px;
}

.page-size-selector::before {
    content: "每页显示:";
    font-size: 14px;
    color: #6c757d;
}

/* 紧凑按钮样式 */
.btn-xs {
    padding: 2px 6px;
    font-size: 11px;
    line-height: 1.2;
    border-radius: 3px;
}

/* 小徽章样式 */
.badge-sm {
    font-size: 10px;
    padding: 2px 6px;
}

/* 表格行悬停效果 */
.table-row-hover {
    background-color: #f8f9fa !important;
    cursor: pointer;
}

/* 工具栏样式优化 */
.toolbar-left {
    display: flex;
    align-items: center;
}

.toolbar-right {
    display: flex;
    align-items: center;
}

.search-form {
    margin: 0;
}

.data-stats {
    display: flex;
    align-items: center;
}

/* 表格优化 */
.data-table tbody tr {
    transition: background-color 0.15s ease;
}

.data-table .text-truncate {
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 深度优化移动端响应式 */
@media (max-width: 768px) {
    .data-page-container {
        height: calc(100vh - 50px);
    }

    .query-row {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .query-item {
        flex-direction: column;
        align-items: stretch;
        gap: 4px;
    }

    .query-item label {
        font-size: 12px;
    }

    .action-bar {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
        padding: 10px 15px;
    }

    .action-left,
    .action-right {
        justify-content: center;
    }

    .table-compact {
        font-size: 11px;
    }

    .table-compact thead th {
        padding: 6px 4px;
        font-size: 10px;
    }

    .table-compact tbody .table-row {
        height: 28px;
    }

    .table-compact tbody td {
        padding: 2px 4px;
        font-size: 10px;
    }

    .btn-xs {
        padding: 1px 2px;
        font-size: 8px;
    }

    .badge-xs {
        font-size: 8px;
        padding: 1px 3px;
    }

    .pagination-bar {
        flex-direction: column;
        gap: 8px;
        text-align: center;
        padding: 10px 15px;
    }

    .pagination-nav .page-link {
        padding: 3px 6px;
        font-size: 11px;
    }

    .view-options {
        font-size: 12px;
    }
}

/* 报表页面专用样式 */
.dashboard-section {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
    background: #f8f9fa;
}

.stats-cards {
    margin-bottom: 20px;
}

.stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.stats-card {
    background: linear-gradient(135deg, var(--bg-color), var(--bg-color-dark));
    border-radius: 8px;
    padding: 20px;
    color: white;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.stats-card.bg-info {
    --bg-color: #17a2b8;
    --bg-color-dark: #138496;
}

.stats-card.bg-success {
    --bg-color: #28a745;
    --bg-color-dark: #1e7e34;
}

.stats-card.bg-warning {
    --bg-color: #ffc107;
    --bg-color-dark: #e0a800;
    color: #212529;
}

.stats-card.bg-danger {
    --bg-color: #dc3545;
    --bg-color-dark: #c82333;
}

.stats-content {
    position: relative;
    z-index: 2;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: bold;
    line-height: 1;
    margin-bottom: 5px;
}

.stats-label {
    font-size: 1rem;
    opacity: 0.9;
}

.stats-icon {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 3rem;
    opacity: 0.3;
    z-index: 1;
}

.stats-footer {
    display: block;
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px solid rgba(255,255,255,0.2);
    color: rgba(255,255,255,0.9);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.2s ease;
}

.stats-footer:hover {
    color: white;
    text-decoration: none;
}

.info-boxes {
    margin-bottom: 20px;
}

.info-box-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.info-box {
    background: white;
    border-radius: 6px;
    padding: 15px;
    display: flex;
    align-items: center;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: box-shadow 0.2s ease;
}

.info-box:hover {
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
}

.info-box-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-right: 15px;
    flex-shrink: 0;
}

.info-box-content {
    flex: 1;
}

.info-box-text {
    display: block;
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 5px;
}

.info-box-number {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: #495057;
}

.report-navigation {
    background: white;
    border-radius: 6px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.section-header {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #dee2e6;
}

.section-header h5 {
    margin: 0;
    color: #495057;
    font-weight: 600;
}

.report-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.report-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.report-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 4px rgba(0,123,255,0.1);
}

.report-icon {
    width: 50px;
    height: 50px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    margin-right: 15px;
    flex-shrink: 0;
}

.report-content {
    flex: 1;
}

.report-name {
    font-weight: 600;
    margin-bottom: 8px;
    color: #495057;
}
