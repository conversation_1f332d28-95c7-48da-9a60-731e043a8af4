-- =============================================
-- ZXCRM系统 - Admin超级管理员权限初始化脚本
-- 创建日期: 2024年
-- 说明: 初始化admin账号的完整权限，确保系统至少有一个超级管理员
-- 使用方法: 在系统正式使用前执行此脚本
-- =============================================

USE [ZXCRM]
GO

-- 开始事务
BEGIN TRANSACTION InitializeAdminPermissions

BEGIN TRY
    PRINT '开始初始化Admin超级管理员权限...'
    
    -- =============================================
    -- 第一步：确保基础权限类型存在
    -- =============================================
    PRINT '1. 检查并创建基础权限类型...'
    
    -- 插入基础权限类型（如果不存在）
    IF NOT EXISTS (SELECT 1 FROM [Permissions] WHERE [Code] = 'Query')
    BEGIN
        INSERT INTO [Permissions] ([Name], [Code], [Description])
        VALUES ('查询', 'Query', '查看和浏览数据的权限')
        PRINT '   - 已创建查询权限'
    END
    
    IF NOT EXISTS (SELECT 1 FROM [Permissions] WHERE [Code] = 'Create')
    BEGIN
        INSERT INTO [Permissions] ([Name], [Code], [Description])
        VALUES ('新建', 'Create', '创建新数据的权限')
        PRINT '   - 已创建新建权限'
    END
    
    IF NOT EXISTS (SELECT 1 FROM [Permissions] WHERE [Code] = 'Edit')
    BEGIN
        INSERT INTO [Permissions] ([Name], [Code], [Description])
        VALUES ('修改', 'Edit', '编辑和更新数据的权限')
        PRINT '   - 已创建修改权限'
    END
    
    IF NOT EXISTS (SELECT 1 FROM [Permissions] WHERE [Code] = 'Delete')
    BEGIN
        INSERT INTO [Permissions] ([Name], [Code], [Description])
        VALUES ('删除', 'Delete', '删除数据的权限')
        PRINT '   - 已创建删除权限'
    END

    -- =============================================
    -- 第二步：确保admin用户存在
    -- =============================================
    PRINT '2. 检查并创建admin用户...'
    
    DECLARE @AdminUserId INT
    DECLARE @DefaultDepartmentId INT
    
    -- 获取默认部门ID（如果没有部门，创建一个系统部门）
    SELECT TOP 1 @DefaultDepartmentId = [Id] FROM [Departments] WHERE [Name] = '系统管理部'
    
    IF @DefaultDepartmentId IS NULL
    BEGIN
        INSERT INTO [Departments] ([Name], [Code], [Status], [CreatedAt], [UpdatedAt])
        VALUES ('系统管理部', 'SYSTEM', 'Active', GETDATE(), GETDATE())
        SET @DefaultDepartmentId = SCOPE_IDENTITY()
        PRINT '   - 已创建系统管理部门'
    END
    
    -- 检查admin用户是否存在
    SELECT @AdminUserId = [Id] FROM [Users] WHERE [Username] = 'admin'
    
    IF @AdminUserId IS NULL
    BEGIN
        -- 创建admin用户（密码需要根据实际加密方式调整）
        INSERT INTO [Users] ([Username], [Password], [Name], [DepartmentId], [Role], [Status], [CreatedAt], [UpdatedAt])
        VALUES (
            'admin', 
            'AQAAAAEAACcQAAAAEK8VJJzOzMqLjKvQjKvQjKvQjKvQjKvQjKvQjKvQjKvQjKvQjKvQjKvQjKvQjKvQ==', -- 默认密码: Admin123!（请在首次登录后修改）
            '系统管理员', 
            @DefaultDepartmentId, 
            'SuperAdmin', 
            'Active', 
            GETDATE(), 
            GETDATE()
        )
        SET @AdminUserId = SCOPE_IDENTITY()
        PRINT '   - 已创建admin用户账号'
        PRINT '   - 默认密码: Admin123! （请在首次登录后立即修改）'
    END
    ELSE
    BEGIN
        PRINT '   - admin用户已存在，ID: ' + CAST(@AdminUserId AS VARCHAR(10))
        
        -- 确保admin用户角色正确
        UPDATE [Users] 
        SET [Role] = 'SuperAdmin', [Status] = 'Active'
        WHERE [Id] = @AdminUserId
        PRINT '   - 已更新admin用户角色为SuperAdmin'
    END

    -- =============================================
    -- 第三步：为admin分配完整权限
    -- =============================================
    PRINT '3. 为admin分配完整权限...'
    
    -- 定义所有模块
    DECLARE @Modules TABLE (ModuleName NVARCHAR(50))
    INSERT INTO @Modules VALUES 
        ('用户管理'),
        ('部门管理'), 
        ('权限管理'),
        ('商机管理'),
        ('订单管理'),
        ('款项管理'),
        ('发票管理'),
        ('报表统计')
    
    -- 获取所有权限ID
    DECLARE @QueryPermissionId INT, @CreatePermissionId INT, @EditPermissionId INT, @DeletePermissionId INT
    
    SELECT @QueryPermissionId = [Id] FROM [Permissions] WHERE [Code] = 'Query'
    SELECT @CreatePermissionId = [Id] FROM [Permissions] WHERE [Code] = 'Create'
    SELECT @EditPermissionId = [Id] FROM [Permissions] WHERE [Code] = 'Edit'
    SELECT @DeletePermissionId = [Id] FROM [Permissions] WHERE [Code] = 'Delete'
    
    -- 清除admin现有权限（重新初始化）
    DELETE FROM [UserPermissions] WHERE [UserId] = @AdminUserId
    PRINT '   - 已清除admin现有权限'
    
    -- 为每个模块分配所有权限
    DECLARE @ModuleName NVARCHAR(50)
    DECLARE module_cursor CURSOR FOR SELECT ModuleName FROM @Modules
    
    OPEN module_cursor
    FETCH NEXT FROM module_cursor INTO @ModuleName
    
    WHILE @@FETCH_STATUS = 0
    BEGIN
        -- 分配查询权限
        INSERT INTO [UserPermissions] ([UserId], [PermissionId], [ModuleType])
        VALUES (@AdminUserId, @QueryPermissionId, @ModuleName)
        
        -- 分配新建权限
        INSERT INTO [UserPermissions] ([UserId], [PermissionId], [ModuleType])
        VALUES (@AdminUserId, @CreatePermissionId, @ModuleName)
        
        -- 分配修改权限
        INSERT INTO [UserPermissions] ([UserId], [PermissionId], [ModuleType])
        VALUES (@AdminUserId, @EditPermissionId, @ModuleName)
        
        -- 分配删除权限
        INSERT INTO [UserPermissions] ([UserId], [PermissionId], [ModuleType])
        VALUES (@AdminUserId, @DeletePermissionId, @ModuleName)
        
        PRINT '   - 已为模块 [' + @ModuleName + '] 分配完整权限'
        
        FETCH NEXT FROM module_cursor INTO @ModuleName
    END
    
    CLOSE module_cursor
    DEALLOCATE module_cursor

    -- =============================================
    -- 第四步：验证权限分配结果
    -- =============================================
    PRINT '4. 验证权限分配结果...'
    
    DECLARE @TotalPermissions INT
    SELECT @TotalPermissions = COUNT(*) FROM [UserPermissions] WHERE [UserId] = @AdminUserId
    
    PRINT '   - admin用户总权限数: ' + CAST(@TotalPermissions AS VARCHAR(10))
    PRINT '   - 预期权限数: 32 (8个模块 × 4种权限)'
    
    IF @TotalPermissions = 32
    BEGIN
        PRINT '   ✓ 权限分配验证成功！'
    END
    ELSE
    BEGIN
        PRINT '   ⚠ 权限分配数量异常，请检查！'
    END

    -- =============================================
    -- 第五步：创建审计日志记录
    -- =============================================
    PRINT '5. 记录初始化操作日志...'
    
    -- 如果有审计日志表，记录此次初始化操作
    IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[AuditLogs]') AND type in (N'U'))
    BEGIN
        INSERT INTO [AuditLogs] ([UserId], [Action], [Details], [Timestamp], [IpAddress])
        VALUES (
            @AdminUserId,
            'SYSTEM_INIT',
            'Admin超级管理员权限初始化完成，分配了所有模块的完整权限',
            GETDATE(),
            'SYSTEM'
        )
        PRINT '   - 已记录初始化审计日志'
    END

    -- =============================================
    -- 提交事务
    -- =============================================
    COMMIT TRANSACTION InitializeAdminPermissions
    
    PRINT ''
    PRINT '=========================================='
    PRINT 'Admin超级管理员权限初始化完成！'
    PRINT '=========================================='
    PRINT '用户名: admin'
    PRINT '默认密码: Admin123!'
    PRINT '角色: SuperAdmin'
    PRINT '权限: 所有模块的完整权限'
    PRINT ''
    PRINT '⚠ 重要提醒:'
    PRINT '1. 请在首次登录后立即修改默认密码'
    PRINT '2. 建议启用双因素认证'
    PRINT '3. 定期检查和审计admin账号的使用情况'
    PRINT '=========================================='

END TRY
BEGIN CATCH
    -- 回滚事务
    ROLLBACK TRANSACTION InitializeAdminPermissions
    
    PRINT ''
    PRINT '=========================================='
    PRINT '❌ Admin权限初始化失败！'
    PRINT '=========================================='
    PRINT '错误信息: ' + ERROR_MESSAGE()
    PRINT '错误行号: ' + CAST(ERROR_LINE() AS VARCHAR(10))
    PRINT '请检查错误并重新执行脚本'
    PRINT '=========================================='
    
    -- 重新抛出错误
    
END CATCH

GO
