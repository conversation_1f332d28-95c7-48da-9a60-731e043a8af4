[{"ContainingType": "ZXCRM.WebAPI.Controllers.AuthController", "Method": "GetCurrentUser", "RelativePath": "api/Auth/current-user", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "loginDto", "Type": "ZXCRM.Service.DTOs.LoginDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.AuthController", "Method": "ValidateToken", "RelativePath": "api/Auth/validate-token", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.DepartmentController", "Method": "GetAllDepartments", "RelativePath": "api/Department", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.DepartmentController", "Method": "CreateDepartment", "RelativePath": "api/Department", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDepartmentDto", "Type": "ZXCRM.Service.DTOs.CreateDepartmentDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.DepartmentController", "Method": "GetDepartmentById", "RelativePath": "api/Department/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.DepartmentController", "Method": "UpdateDepartment", "RelativePath": "api/Department/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateDepartmentDto", "Type": "ZXCRM.Service.DTOs.UpdateDepartmentDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.DepartmentController", "Method": "DeleteDepartment", "RelativePath": "api/Department/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.DepartmentController", "Method": "GetDepartmentUsers", "RelativePath": "api/Department/{id}/users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.DepartmentController", "Method": "GetDepartmentTree", "RelativePath": "api/Department/tree", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.InvoiceController", "Method": "GetInvoices", "RelativePath": "api/Invoice", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.InvoiceController", "Method": "CreateInvoice", "RelativePath": "api/Invoice", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createInvoiceDto", "Type": "ZXCRM.Service.DTOs.CreateInvoiceDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.InvoiceController", "Method": "GetInvoice", "RelativePath": "api/Invoice/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.InvoiceController", "Method": "UpdateInvoice", "RelativePath": "api/Invoice/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateInvoiceDto", "Type": "ZXCRM.Service.DTOs.UpdateInvoiceDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.InvoiceController", "Method": "DeleteInvoice", "RelativePath": "api/Invoice/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.InvoiceController", "Method": "GetInvoicesByPayment", "RelativePath": "api/Invoice/payment/{paymentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "paymentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.InvoiceController", "Method": "GetValidInvoiceAmountByPayment", "RelativePath": "api/Invoice/payment/{paymentId}/amount", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "paymentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.OpportunityController", "Method": "GetOpportunities", "RelativePath": "api/Opportunity", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.OpportunityController", "Method": "CreateOpportunity", "RelativePath": "api/Opportunity", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createOpportunityDto", "Type": "ZXCRM.Service.DTOs.CreateOpportunityDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.OpportunityController", "Method": "GetOpportunity", "RelativePath": "api/Opportunity/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.OpportunityController", "Method": "UpdateOpportunity", "RelativePath": "api/Opportunity/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateOpportunityDto", "Type": "ZXCRM.Service.DTOs.UpdateOpportunityDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.OpportunityController", "Method": "DeleteOpportunity", "RelativePath": "api/Opportunity/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.OpportunityController", "Method": "GetAllOpportunities", "RelativePath": "api/Opportunity/all", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.OpportunityController", "Method": "GetOpportunitiesByUser", "RelativePath": "api/Opportunity/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.OrderController", "Method": "GetOrders", "RelativePath": "api/Order", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.OrderController", "Method": "CreateOrder", "RelativePath": "api/Order", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createOrderDto", "Type": "ZXCRM.Service.DTOs.CreateOrderDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.OrderController", "Method": "GetOrder", "RelativePath": "api/Order/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.OrderController", "Method": "UpdateOrder", "RelativePath": "api/Order/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateOrderDto", "Type": "ZXCRM.Service.DTOs.UpdateOrderDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.OrderController", "Method": "DeleteOrder", "RelativePath": "api/Order/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.OrderController", "Method": "GetOrdersByUser", "RelativePath": "api/Order/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.PaymentController", "Method": "GetPayments", "RelativePath": "api/Payment", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.PaymentController", "Method": "CreatePayment", "RelativePath": "api/Payment", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createPaymentDto", "Type": "ZXCRM.Service.DTOs.CreatePaymentDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.PaymentController", "Method": "GetPayment", "RelativePath": "api/Payment/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.PaymentController", "Method": "UpdatePayment", "RelativePath": "api/Payment/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updatePaymentDto", "Type": "ZXCRM.Service.DTOs.UpdatePaymentDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.PaymentController", "Method": "DeletePayment", "RelativePath": "api/Payment/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.PaymentController", "Method": "GetPaymentsByOrder", "RelativePath": "api/Payment/order/{orderId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.PermissionController", "Method": "GetAllPermissions", "RelativePath": "api/Permission", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.PermissionController", "Method": "CreatePermission", "RelativePath": "api/Permission", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "permissionDto", "Type": "ZXCRM.Service.DTOs.PermissionDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.PermissionController", "Method": "GetPermissionById", "RelativePath": "api/Permission/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.PermissionController", "Method": "UpdatePermission", "RelativePath": "api/Permission/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "permissionDto", "Type": "ZXCRM.Service.DTOs.PermissionDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.PermissionController", "Method": "DeletePermission", "RelativePath": "api/Permission/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.ReportController", "Method": "GetDashboardStats", "RelativePath": "api/Report/dashboard", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DepartmentId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UserId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.ReportController", "Method": "ExportReport", "RelativePath": "api/Report/export/{reportType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "reportType", "Type": "System.String", "IsRequired": true}, {"Name": "StartDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DepartmentId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UserId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.ReportController", "Method": "GetInvoiceReport", "RelativePath": "api/Report/invoice", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DepartmentId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UserId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.ReportController", "Method": "GetOpportunityReport", "RelativePath": "api/Report/opportunity", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DepartmentId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UserId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.ReportController", "Method": "GetAllOpportunityReport", "RelativePath": "api/Report/opportunity/all", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DepartmentId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UserId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.ReportController", "Method": "GetOrderReport", "RelativePath": "api/Report/order", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DepartmentId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UserId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.ReportController", "Method": "GetPaymentReport", "RelativePath": "api/Report/payment", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DepartmentId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UserId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.ReportController", "Method": "GetReportSummary", "RelativePath": "api/Report/summary", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DepartmentId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UserId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.ReportController", "Method": "GetTrends", "RelativePath": "api/Report/trends", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DepartmentId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UserId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.TestController", "Method": "Get", "RelativePath": "api/Test", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.TestApiController", "Method": "Get", "RelativePath": "api/TestApi", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.TestApiController", "Method": "Echo", "RelativePath": "api/TestApi/echo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "data", "Type": "System.Object", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.UserController", "Method": "GetAllUsers", "RelativePath": "api/User", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.UserController", "Method": "CreateUser", "RelativePath": "api/User", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createUserDto", "Type": "ZXCRM.Service.DTOs.CreateUserDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.UserController", "Method": "GetUserById", "RelativePath": "api/User/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.UserController", "Method": "UpdateUser", "RelativePath": "api/User/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateUserDto", "Type": "ZXCRM.Service.DTOs.UpdateUserDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.UserController", "Method": "DeleteUser", "RelativePath": "api/User/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.UserController", "Method": "CheckPermission", "RelativePath": "api/User/check-permission", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "moduleType", "Type": "System.String", "IsRequired": false}, {"Name": "permissionCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.UserPermissionController", "Method": "GetUserPermissions", "RelativePath": "api/UserPermission/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ZXCRM.WebAPI.Controllers.UserPermissionController", "Method": "UpdateUserPermissions", "RelativePath": "api/UserPermission/user/{userId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}, {"Name": "permissions", "Type": "System.Collections.Generic.List`1[[ZXCRM.Service.DTOs.UserPermissionDto, ZXCRM.Service, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}]