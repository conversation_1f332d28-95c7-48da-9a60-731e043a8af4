using Microsoft.AspNetCore.Mvc;
using ZXCRM.Service.Interfaces;
using ZXCRM.Service.DTOs;

namespace ZXCRM.WebAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class PermissionCheckController : ControllerBase
    {
        private readonly IPermissionCheckService _permissionCheckService;
        private readonly ILogger<PermissionCheckController> _logger;

        public PermissionCheckController(
            IPermissionCheckService permissionCheckService,
            ILogger<PermissionCheckController> logger)
        {
            _permissionCheckService = permissionCheckService;
            _logger = logger;
        }

        /// <summary>
        /// 检查用户是否有特定权限
        /// </summary>
        [HttpGet("user/{userId}/permission")]
        public async Task<IActionResult> HasPermission(int userId, [FromQuery] string moduleType, [FromQuery] string permissionCode)
        {
            try
            {
                if (string.IsNullOrEmpty(moduleType) || string.IsNullOrEmpty(permissionCode))
                {
                    return BadRequest("ModuleType and PermissionCode are required");
                }

                var hasPermission = await _permissionCheckService.HasPermissionAsync(userId, moduleType, permissionCode);
                
                return Ok(new { 
                    UserId = userId,
                    ModuleType = moduleType,
                    PermissionCode = permissionCode,
                    HasPermission = hasPermission 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking permission for user {UserId}: {ModuleType}.{PermissionCode}", 
                    userId, moduleType, permissionCode);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 检查用户是否有模块访问权限
        /// </summary>
        [HttpGet("user/{userId}/module/{moduleType}/access")]
        public async Task<IActionResult> HasModuleAccess(int userId, string moduleType)
        {
            try
            {
                if (string.IsNullOrEmpty(moduleType))
                {
                    return BadRequest("ModuleType is required");
                }

                var hasAccess = await _permissionCheckService.HasModuleAccessAsync(userId, moduleType);
                
                return Ok(new { 
                    UserId = userId,
                    ModuleType = moduleType,
                    HasAccess = hasAccess 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking module access for user {UserId}: {ModuleType}", 
                    userId, moduleType);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 获取用户有权限的模块列表
        /// </summary>
        [HttpGet("user/{userId}/modules")]
        public async Task<IActionResult> GetUserModules(int userId)
        {
            try
            {
                var modules = await _permissionCheckService.GetUserModulesAsync(userId);
                
                return Ok(new { 
                    UserId = userId,
                    Modules = modules 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user modules for user {UserId}", userId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 获取用户的完整权限映射
        /// </summary>
        [HttpGet("user/{userId}/permissions")]
        public async Task<IActionResult> GetUserPermissions(int userId)
        {
            try
            {
                var permissions = await _permissionCheckService.GetUserPermissionsAsync(userId);
                
                return Ok(new { 
                    UserId = userId,
                    Permissions = permissions 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user permissions for user {UserId}", userId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 获取用户权限概览
        /// </summary>
        [HttpGet("user/{userId}/overview")]
        public async Task<IActionResult> GetUserPermissionOverview(int userId)
        {
            try
            {
                var overview = await _permissionCheckService.GetUserPermissionOverviewAsync(userId);
                
                return Ok(overview);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "User {UserId} not found", userId);
                return NotFound($"User with ID {userId} not found");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user permission overview for user {UserId}", userId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 批量检查用户权限
        /// </summary>
        [HttpPost("user/{userId}/check-batch")]
        public async Task<IActionResult> CheckPermissionsBatch(int userId, [FromBody] List<PermissionCheckRequest> requests)
        {
            try
            {
                if (requests == null || !requests.Any())
                {
                    return BadRequest("Permission check requests are required");
                }

                var results = new List<PermissionCheckResult>();

                foreach (var request in requests)
                {
                    var hasPermission = await _permissionCheckService.HasPermissionAsync(
                        userId, request.ModuleType, request.PermissionCode);

                    results.Add(new PermissionCheckResult
                    {
                        ModuleType = request.ModuleType,
                        PermissionCode = request.PermissionCode,
                        HasPermission = hasPermission
                    });
                }

                return Ok(new { 
                    UserId = userId,
                    Results = results 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking batch permissions for user {UserId}", userId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 检查模块类型
        /// </summary>
        [HttpGet("module/{moduleType}/type")]
        public IActionResult GetModuleType(string moduleType)
        {
            try
            {
                if (string.IsNullOrEmpty(moduleType))
                {
                    return BadRequest("ModuleType is required");
                }

                var isSystemModule = _permissionCheckService.IsSystemModule(moduleType);
                var isBusinessModule = _permissionCheckService.IsBusinessModule(moduleType);

                return Ok(new { 
                    ModuleType = moduleType,
                    IsSystemModule = isSystemModule,
                    IsBusinessModule = isBusinessModule 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking module type for {ModuleType}", moduleType);
                return StatusCode(500, "Internal server error");
            }
        }
    }

    /// <summary>
    /// 权限检查请求
    /// </summary>
    public class PermissionCheckRequest
    {
        public string ModuleType { get; set; } = string.Empty;
        public string PermissionCode { get; set; } = string.Empty;
    }

    /// <summary>
    /// 权限检查结果
    /// </summary>
    public class PermissionCheckResult
    {
        public string ModuleType { get; set; } = string.Empty;
        public string PermissionCode { get; set; } = string.Empty;
        public bool HasPermission { get; set; }
    }
}
