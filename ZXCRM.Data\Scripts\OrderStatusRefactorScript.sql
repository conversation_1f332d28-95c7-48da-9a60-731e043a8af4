-- =============================================
-- 订单状态重构脚本
-- 创建时间: 2024年12月
-- 描述: 将订单状态从旧的中文状态重构为新的英文枚举状态
-- 新状态: New(新建), InProgress(进行中), Completed(已完成), Cancelled(已取消)
-- =============================================

-- 1. 检查当前订单状态分布
PRINT '=== 当前订单状态分布 ===';
SELECT 
    Status,
    COUNT(*) AS Count,
    CASE 
        WHEN Status IS NULL OR Status = '' THEN '❌ 状态为空'
        WHEN Status IN ('草稿', 'Draft') THEN '🔄 需要更新为New'
        WHEN Status IN ('待确认', 'Confirmed') THEN '🔄 需要更新为InProgress'
        WHEN Status IN ('进行中', 'InProgress') THEN '✅ 已是新状态'
        WHEN Status IN ('已完成', 'Completed') THEN '✅ 已是新状态'
        WHEN Status IN ('已取消', 'Cancelled') THEN '✅ 已是新状态'
        ELSE '⚠️ 未知状态'
    END AS StatusCheck
FROM Orders
GROUP BY Status
ORDER BY Count DESC;

-- 2. 开始状态重构
PRINT '=== 开始订单状态重构 ===';

BEGIN TRANSACTION;

-- 更新空状态或NULL状态
UPDATE Orders 
SET Status = CASE 
    WHEN SignDate IS NOT NULL AND SignDate <= GETDATE() THEN 'InProgress'
    ELSE 'New'
END
WHERE Status IS NULL OR Status = '';

PRINT '✅ 已更新空状态订单';

-- 更新旧的中文状态
UPDATE Orders 
SET Status = 'New'
WHERE Status IN ('草稿', 'Draft', '待确认');

PRINT '✅ 已更新草稿和待确认状态为New';

UPDATE Orders 
SET Status = 'InProgress'
WHERE Status IN ('确认', 'Confirmed', '已确认', '执行中');

PRINT '✅ 已更新确认状态为InProgress';

UPDATE Orders 
SET Status = 'Completed'
WHERE Status IN ('完成', '已完成');

PRINT '✅ 已更新完成状态为Completed';

UPDATE Orders 
SET Status = 'Cancelled'
WHERE Status IN ('取消', '已取消');

PRINT '✅ 已更新取消状态为Cancelled';

-- 处理其他可能的状态
UPDATE Orders 
SET Status = 'InProgress'
WHERE Status NOT IN ('New', 'InProgress', 'Completed', 'Cancelled') 
  AND Status IS NOT NULL 
  AND Status != '';

PRINT '✅ 已处理其他未知状态';

COMMIT TRANSACTION;

-- 3. 验证重构结果
PRINT '=== 重构结果验证 ===';

SELECT 
    Status,
    COUNT(*) AS Count,
    CASE 
        WHEN Status = 'New' THEN '✅ 新建状态'
        WHEN Status = 'InProgress' THEN '✅ 进行中状态'
        WHEN Status = 'Completed' THEN '✅ 已完成状态'
        WHEN Status = 'Cancelled' THEN '✅ 已取消状态'
        ELSE '❌ 异常状态'
    END AS StatusValidation
FROM Orders
GROUP BY Status
ORDER BY 
    CASE Status
        WHEN 'New' THEN 1
        WHEN 'InProgress' THEN 2
        WHEN 'Completed' THEN 3
        WHEN 'Cancelled' THEN 4
        ELSE 5
    END;

-- 4. 显示重构统计
PRINT '=== 重构统计信息 ===';

SELECT 
    COUNT(*) AS TotalOrders,
    SUM(CASE WHEN Status = 'New' THEN 1 ELSE 0 END) AS NewCount,
    SUM(CASE WHEN Status = 'InProgress' THEN 1 ELSE 0 END) AS InProgressCount,
    SUM(CASE WHEN Status = 'Completed' THEN 1 ELSE 0 END) AS CompletedCount,
    SUM(CASE WHEN Status = 'Cancelled' THEN 1 ELSE 0 END) AS CancelledCount,
    SUM(CASE WHEN Status NOT IN ('New', 'InProgress', 'Completed', 'Cancelled') THEN 1 ELSE 0 END) AS InvalidCount
FROM Orders;

-- 5. 检查是否有异常数据
IF EXISTS (SELECT 1 FROM Orders WHERE Status NOT IN ('New', 'InProgress', 'Completed', 'Cancelled'))
BEGIN
    PRINT '⚠️ 发现异常状态数据:';
    SELECT Id, Name, Status, SignDate, CreatedAt
    FROM Orders 
    WHERE Status NOT IN ('New', 'InProgress', 'Completed', 'Cancelled');
END
ELSE
BEGIN
    PRINT '🎉 所有订单状态已成功重构为新的枚举值！';
END

-- 6. 提供后续建议
PRINT '=== 后续操作建议 ===';
PRINT '1. 重启应用程序以确保缓存清除';
PRINT '2. 测试订单列表页面的状态显示';
PRINT '3. 测试订单创建和编辑表单的状态选择';
PRINT '4. 验证订单详情页面的状态显示';
PRINT '5. 检查相关报表和统计功能';

PRINT '🎯 订单状态重构脚本执行完成！';
