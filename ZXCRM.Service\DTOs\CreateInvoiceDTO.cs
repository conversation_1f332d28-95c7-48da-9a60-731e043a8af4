using System.ComponentModel.DataAnnotations;

namespace ZXCRM.Service.DTOs
{
    public class CreateInvoiceDTO
    {
        [Required(ErrorMessage = "款项ID不能为空")]
        public int PaymentId { get; set; }

        [Required(ErrorMessage = "发票公司不能为空")]
        [StringLength(50, ErrorMessage = "发票公司长度不能超过50个字符")]
        public string Company { get; set; } = string.Empty;

        [Required(ErrorMessage = "发票类型不能为空")]
        [StringLength(50, ErrorMessage = "发票类型长度不能超过50个字符")]
        public string Type { get; set; } = string.Empty;

        [Required(ErrorMessage = "发票税率不能为空")]
        [Range(0, 100, ErrorMessage = "发票税率必须在0-100之间")]
        public decimal TaxRate { get; set; }

        [Required(ErrorMessage = "发票金额不能为空")]
        [Range(0.01, double.MaxValue, ErrorMessage = "发票金额必须大于0")]
        public decimal Amount { get; set; } // 新增发票金额字段

        public string Status { get; set; } = "Normal"; // 新增发票状态字段，默认为正常

        [StringLength(200, ErrorMessage = "发票内容长度不能超过200个字符")]
        public string? Content { get; set; }

        [StringLength(50, ErrorMessage = "发票代号长度不能超过50个字符")]
        public string? Code { get; set; }

        [StringLength(50, ErrorMessage = "收件人长度不能超过50个字符")]
        public string? ReceiverName { get; set; }

        [StringLength(20, ErrorMessage = "收件人电话长度不能超过20个字符")]
        public string? ReceiverPhone { get; set; }

        [StringLength(200, ErrorMessage = "邮寄地址长度不能超过200个字符")]
        public string? MailingAddress { get; set; }

        [StringLength(20, ErrorMessage = "申请人电话长度不能超过20个字符")]
        public string? ApplicantPhone { get; set; }

        [StringLength(100, ErrorMessage = "客户电子邮件长度不能超过100个字符")]
        [EmailAddress(ErrorMessage = "客户电子邮件格式不正确")]
        public string? CustomerEmail { get; set; }

        public int CreatedById { get; set; }
    }
}
