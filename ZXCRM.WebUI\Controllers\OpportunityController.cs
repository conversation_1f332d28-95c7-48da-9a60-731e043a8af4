using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ZXCRM.Service.DTOs;
using ZXCRM.WebUI.Services;
using ZXCRM.WebUI.Models.ViewModels;
using System.Security.Claims;

namespace ZXCRM.WebUI.Controllers
{
    [Authorize]
    public class OpportunityController : Controller
    {
        private readonly IOpportunityService _opportunityService;
        private readonly IOrderService _orderService;
        private readonly ILogger<OpportunityController> _logger;

        public OpportunityController(
            IOpportunityService opportunityService,
            IOrderService orderService,
            ILogger<OpportunityController> logger)
        {
            _opportunityService = opportunityService;
            _orderService = orderService;
            _logger = logger;
        }

        // GET: Opportunity
        public async Task<IActionResult> Index(string searchTerm = "", string status = "", int pageIndex = 1, int pageSize = 25)
        {
            try
            {
                // 验证分页参数
                pageIndex = Math.Max(1, pageIndex);
                pageSize = Math.Max(1, Math.Min(100, pageSize)); // 限制最大100条每页

                _logger.LogInformation("Getting opportunities list. SearchTerm: {SearchTerm}, Status: {Status}, PageIndex: {PageIndex}, PageSize: {PageSize}",
                    searchTerm, status, pageIndex, pageSize);

                var response = await _opportunityService.GetOpportunitiesAsync();

                if (!response.Success || response.Data == null)
                {
                    _logger.LogWarning("Failed to get opportunities: {Message}", response.Message);
                    TempData["ErrorMessage"] = response.Message ?? "获取商机列表失败";
                    return View(new OpportunityListViewModel());
                }

                // 应用搜索和筛选
                var filteredOpportunities = response.Data.AsQueryable();

                if (!string.IsNullOrEmpty(searchTerm))
                {
                    filteredOpportunities = filteredOpportunities.Where(o =>
                        o.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        o.CustomerName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        (!string.IsNullOrEmpty(o.ContactName) && o.ContactName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) ||
                        (!string.IsNullOrEmpty(o.Content) && o.Content.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)));
                }

                if (!string.IsNullOrEmpty(status))
                {
                    filteredOpportunities = filteredOpportunities.Where(o => o.Status == status);
                }

                // 应用分页
                var totalCount = filteredOpportunities.Count();
                var opportunities = filteredOpportunities
                    .Skip((pageIndex - 1) * pageSize)
                    .Take(pageSize)
                    .Select(o => new OpportunityItemViewModel
                    {
                        Id = o.Id,
                        Name = o.Name,
                        CustomerName = o.CustomerName,
                        ContactName = o.ContactName,
                        ContactPhone = o.ContactPhone,
                        Content = o.Content,
                        Status = o.Status,
                        CreatedByName = o.CreatedByName,
                        CreatedAt = o.CreatedAt,
                        UpdatedAt = o.UpdatedAt,
                        OrderCount = 0 // TODO: 获取关联订单数量
                    })
                    .ToList();

                var viewModel = new OpportunityListViewModel
                {
                    Opportunities = opportunities,
                    TotalCount = totalCount,
                    PageIndex = pageIndex,
                    PageSize = pageSize,
                    SearchTerm = searchTerm,
                    Status = status
                };

                _logger.LogInformation("Successfully retrieved {Count} opportunities", opportunities.Count);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting opportunities list");
                TempData["ErrorMessage"] = "获取商机列表时发生错误";
                return View(new OpportunityListViewModel());
            }
        }

        // GET: Opportunity/Details/5
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                _logger.LogInformation("Getting opportunity details for ID: {OpportunityId}", id);

                var response = await _opportunityService.GetOpportunityByIdAsync(id);

                if (!response.Success || response.Data == null)
                {
                    _logger.LogWarning("Opportunity not found: {OpportunityId}", id);
                    TempData["ErrorMessage"] = response.Message ?? "商机不存在";
                    return RedirectToAction(nameof(Index));
                }

                // 获取关联订单
                var relatedOrders = new List<RelatedOrderViewModel>();
                try
                {
                    var ordersResponse = await _orderService.GetOrdersAsync();
                    if (ordersResponse.Success && ordersResponse.Data != null)
                    {
                        relatedOrders = ordersResponse.Data
                            .Where(order => order.OpportunityId == id)
                            .Select(order => new RelatedOrderViewModel
                            {
                                Id = order.Id,
                                Name = order.Name,
                                Code = order.Code,
                                Amount = order.Amount,
                                Currency = order.Currency,
                                Status = order.Status,
                                CreatedAt = order.CreatedAt
                            })
                            .ToList();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error getting related orders for opportunity: {OpportunityId}", id);
                }

                var viewModel = new OpportunityDetailViewModel
                {
                    Id = response.Data.Id,
                    Name = response.Data.Name,
                    CustomerName = response.Data.CustomerName,
                    ContactName = response.Data.ContactName,
                    ContactPhone = response.Data.ContactPhone,
                    Content = response.Data.Content,
                    Status = response.Data.Status,
                    CreatedByName = response.Data.CreatedByName,
                    CreatedAt = response.Data.CreatedAt,
                    UpdatedAt = response.Data.UpdatedAt,
                    RelatedOrders = relatedOrders
                };

                _logger.LogInformation("Successfully retrieved opportunity details for ID: {OpportunityId}", id);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting opportunity details for ID: {OpportunityId}", id);
                TempData["ErrorMessage"] = "获取商机详情时发生错误";
                return RedirectToAction(nameof(Index));
            }
        }

        // GET: Opportunity/Create
        public IActionResult Create()
        {
            var viewModel = new CreateOpportunityViewModel();
            return View(viewModel);
        }

        // POST: Opportunity/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CreateOpportunityViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return View(model);
                }

                _logger.LogInformation("Creating new opportunity: {OpportunityName}", model.Name);

                var createOpportunityDto = new CreateOpportunityDTO
                {
                    Name = model.Name,
                    CustomerName = model.CustomerName,
                    ContactName = model.ContactName,
                    ContactPhone = model.ContactPhone,
                    Content = model.Content,
                    Status = model.Status,
                    CreatedById = 0 // 将在API中设置为当前用户
                };

                var response = await _opportunityService.CreateOpportunityAsync(createOpportunityDto);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully created opportunity: {OpportunityName}", model.Name);
                    TempData["SuccessMessage"] = "商机创建成功";
                    return RedirectToAction(nameof(Index));
                }
                else
                {
                    _logger.LogWarning("Failed to create opportunity: {OpportunityName}. Message: {Message}", model.Name, response.Message);
                    ModelState.AddModelError(string.Empty, response.Message ?? "创建商机失败");
                    return View(model);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating opportunity: {OpportunityName}", model.Name);
                ModelState.AddModelError(string.Empty, "创建商机时发生错误");
                return View(model);
            }
        }

        // GET: Opportunity/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            try
            {
                _logger.LogInformation("Loading edit opportunity page for ID: {OpportunityId}", id);

                var response = await _opportunityService.GetOpportunityByIdAsync(id);

                if (!response.Success || response.Data == null)
                {
                    _logger.LogWarning("Opportunity not found for edit: {OpportunityId}", id);
                    TempData["ErrorMessage"] = response.Message ?? "商机不存在";
                    return RedirectToAction(nameof(Index));
                }

                var viewModel = new EditOpportunityViewModel
                {
                    Id = response.Data.Id,
                    Name = response.Data.Name,
                    CustomerName = response.Data.CustomerName,
                    ContactName = response.Data.ContactName,
                    ContactPhone = response.Data.ContactPhone,
                    Content = response.Data.Content,
                    Status = response.Data.Status
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading edit opportunity page for ID: {OpportunityId}", id);
                TempData["ErrorMessage"] = "加载编辑商机页面时发生错误";
                return RedirectToAction(nameof(Index));
            }
        }

        // POST: Opportunity/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, EditOpportunityViewModel model)
        {
            try
            {
                if (id != model.Id)
                {
                    return BadRequest();
                }

                if (!ModelState.IsValid)
                {
                    return View(model);
                }

                _logger.LogInformation("Updating opportunity: {OpportunityId}", id);

                var updateOpportunityDto = new UpdateOpportunityDTO
                {
                    Id = model.Id,
                    Name = model.Name,
                    CustomerName = model.CustomerName,
                    ContactName = model.ContactName,
                    ContactPhone = model.ContactPhone,
                    Content = model.Content,
                    Status = model.Status
                };

                var response = await _opportunityService.UpdateOpportunityAsync(id, updateOpportunityDto);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully updated opportunity: {OpportunityId}", id);
                    TempData["SuccessMessage"] = "商机更新成功";
                    return RedirectToAction(nameof(Details), new { id = id });
                }
                else
                {
                    _logger.LogWarning("Failed to update opportunity: {OpportunityId}. Message: {Message}", id, response.Message);
                    ModelState.AddModelError(string.Empty, response.Message ?? "更新商机失败");
                    return View(model);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating opportunity: {OpportunityId}", id);
                ModelState.AddModelError(string.Empty, "更新商机时发生错误");
                return View(model);
            }
        }

        // POST: Opportunity/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                _logger.LogInformation("Deleting opportunity: {OpportunityId}", id);

                var response = await _opportunityService.DeleteOpportunityAsync(id);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully deleted opportunity: {OpportunityId}", id);
                    TempData["SuccessMessage"] = "商机删除成功";
                }
                else
                {
                    _logger.LogWarning("Failed to delete opportunity: {OpportunityId}. Message: {Message}", id, response.Message);
                    TempData["ErrorMessage"] = response.Message ?? "删除商机失败";
                }

                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting opportunity: {OpportunityId}", id);
                TempData["ErrorMessage"] = "删除商机时发生错误";
                return RedirectToAction(nameof(Index));
            }
        }
    }
}
