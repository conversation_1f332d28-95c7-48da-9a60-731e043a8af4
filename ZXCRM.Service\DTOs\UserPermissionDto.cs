using System;

namespace ZXCRM.Service.DTOs
{
    public class UserPermissionDto
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public int PermissionId { get; set; }
        public string ModuleType { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class UserPermissionOverviewDto
    {
        public int UserId { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Role { get; set; } = string.Empty;
        public bool IsSuperAdmin { get; set; }
        public List<string> BusinessModules { get; set; } = new();
        public List<string> SystemModules { get; set; } = new();
        public Dictionary<string, List<string>> ModulePermissions { get; set; } = new();
        public bool HasBusinessAccess { get; set; }
        public bool HasSystemAccess { get; set; }
    }
}