@model ZXCRM.WebUI.Models.ViewModels.PaymentDetailViewModel
@{
    ViewData["Title"] = "款项详情";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">款项详情</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a asp-controller="Dashboard" asp-action="Index">首页</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Payment" asp-action="Index">款项管理</a></li>
                    <li class="breadcrumb-item active">款项详情</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <!-- 基本信息 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-info-circle"></i> 基本信息
                        </h3>
                        <div class="card-tools">
                            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning btn-sm">
                                <i class="fas fa-edit"></i> 编辑
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">款项名称:</dt>
                                    <dd class="col-sm-8">@Model.Name</dd>
                                    
                                    <dt class="col-sm-4">款项编号:</dt>
                                    <dd class="col-sm-8"><code>@Model.Code</code></dd>
                                    
                                    <dt class="col-sm-4">款项类型:</dt>
                                    <dd class="col-sm-8">
                                        <span class="badge badge-secondary badge-lg">@Model.PaymentType</span>
                                    </dd>
                                    
                                    <dt class="col-sm-4">关联订单:</dt>
                                    <dd class="col-sm-8">
                                        <a asp-controller="Order" asp-action="Details" asp-route-id="@Model.OrderId" class="text-decoration-none">
                                            <strong>@Model.OrderName</strong>
                                        </a>
                                        <br><small class="text-muted">@Model.OrderCode</small>
                                    </dd>
                                    
                                    <dt class="col-sm-4">客户名称:</dt>
                                    <dd class="col-sm-8">@Model.CustomerName</dd>
                                </dl>
                            </div>
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">款项金额:</dt>
                                    <dd class="col-sm-8">
                                        <span class="badge badge-success badge-lg">
                                            @Model.Amount.ToString("N2") @Model.Currency
                                        </span>
                                    </dd>
                                    
                                    <dt class="col-sm-4">结算金额:</dt>
                                    <dd class="col-sm-8">
                                        <span class="badge badge-info badge-lg">
                                            @Model.SettlementAmount.ToString("N2") @Model.Currency
                                        </span>
                                    </dd>
                                    
                                    <dt class="col-sm-4">预期回款:</dt>
                                    <dd class="col-sm-8">
                                        @if (Model.ExpectedPaymentDate.HasValue)
                                        {
                                            @Model.ExpectedPaymentDate.Value.ToString("yyyy年MM月dd日")
                                        }
                                        else
                                        {
                                            <span class="text-muted">未设定</span>
                                        }
                                    </dd>
                                    
                                    <dt class="col-sm-4">实际回款:</dt>
                                    <dd class="col-sm-8">
                                        @if (Model.ActualPaymentDate.HasValue)
                                        {
                                            <span class="badge badge-success">
                                                <i class="fas fa-check"></i> @Model.ActualPaymentDate.Value.ToString("yyyy年MM月dd日")
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="badge badge-warning">
                                                <i class="fas fa-clock"></i> 待回款
                                            </span>
                                        }
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 开票信息 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-file-invoice"></i> 开票信息
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">开票状态:</dt>
                                    <dd class="col-sm-8">
                                        @switch (Model.InvoiceStatus)
                                        {
                                            case "未开票":
                                                <span class="badge badge-secondary badge-lg">
                                                    <i class="fas fa-file"></i> @Model.InvoiceStatus
                                                </span>
                                                break;
                                            case "已开票":
                                                <span class="badge badge-info badge-lg">
                                                    <i class="fas fa-file-invoice"></i> @Model.InvoiceStatus
                                                </span>
                                                break;
                                            case "已寄出":
                                                <span class="badge badge-success badge-lg">
                                                    <i class="fas fa-paper-plane"></i> @Model.InvoiceStatus
                                                </span>
                                                break;
                                            default:
                                                <span class="badge badge-light badge-lg">@Model.InvoiceStatus</span>
                                                break;
                                        }
                                    </dd>
                                    
                                    <dt class="col-sm-4">开票日期:</dt>
                                    <dd class="col-sm-8">
                                        @if (Model.InvoiceDate.HasValue)
                                        {
                                            @Model.InvoiceDate.Value.ToString("yyyy年MM月dd日")
                                        }
                                        else
                                        {
                                            <span class="text-muted">未开票</span>
                                        }
                                    </dd>
                                </dl>
                            </div>
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">发票记录:</dt>
                                    <dd class="col-sm-8">
                                        <a href="#" class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-file-invoice"></i> 查看发票
                                        </a>
                                        <a href="#" class="btn btn-outline-success btn-sm">
                                            <i class="fas fa-plus"></i> 新增发票
                                        </a>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 人员信息 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-users"></i> 人员信息
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">客户经理:</dt>
                                    <dd class="col-sm-8">
                                        <span class="badge badge-primary">@Model.AccountManagerName</span>
                                    </dd>
                                </dl>
                            </div>
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">项目经理:</dt>
                                    <dd class="col-sm-8">
                                        @if (!string.IsNullOrEmpty(Model.ProjectManagerName))
                                        {
                                            <span class="badge badge-info">@Model.ProjectManagerName</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">未指定</span>
                                        }
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统信息 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-cog"></i> 系统信息
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">创建时间:</dt>
                                    <dd class="col-sm-8">@Model.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss")</dd>
                                </dl>
                            </div>
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">最后更新:</dt>
                                    <dd class="col-sm-8">@Model.UpdatedAt.ToString("yyyy-MM-dd HH:mm:ss")</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <!-- 操作面板 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-tools"></i> 操作
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="btn-group-vertical btn-block">
                            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning">
                                <i class="fas fa-edit"></i> 编辑款项
                            </a>
                            <button type="button" class="btn btn-danger" onclick="confirmDelete(@Model.Id, '@Model.Name')">
                                <i class="fas fa-trash"></i> 删除款项
                            </button>
                            <div class="dropdown-divider"></div>
                            @if (Model.InvoiceStatus == "未开票")
                            {
                                <button type="button" class="btn btn-info" onclick="markAsInvoiced()">
                                    <i class="fas fa-file-invoice"></i> 标记已开票
                                </button>
                            }
                            @if (!Model.ActualPaymentDate.HasValue)
                            {
                                <button type="button" class="btn btn-success" onclick="markAsPaid()">
                                    <i class="fas fa-check"></i> 标记已回款
                                </button>
                            }
                        </div>
                    </div>
                </div>

                <!-- 回款进度 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-line"></i> 回款进度
                        </h3>
                    </div>
                    <div class="card-body">
                        @{
                            var paymentProgress = Model.ActualPaymentDate.HasValue ? 100 : 0;
                            var progressClass = paymentProgress == 100 ? "bg-success" : "bg-warning";
                        }
                        
                        <div class="progress mb-3">
                            <div class="progress-bar @progressClass" role="progressbar" style="width: @paymentProgress%" 
                                 aria-valuenow="@paymentProgress" aria-valuemin="0" aria-valuemax="100">
                                @paymentProgress%
                            </div>
                        </div>
                        
                        <div class="info-box">
                            <span class="info-box-icon bg-info"><i class="fas fa-money-bill"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">款项金额</span>
                                <span class="info-box-number">@Model.Amount.ToString("N2") @Model.Currency</span>
                            </div>
                        </div>
                        
                        @if (Model.ExpectedPaymentDate.HasValue && !Model.ActualPaymentDate.HasValue)
                        {
                            var daysRemaining = (Model.ExpectedPaymentDate.Value - DateTime.Now).Days;
                            <div class="alert @(daysRemaining < 0 ? "alert-danger" : daysRemaining <= 7 ? "alert-warning" : "alert-info")">
                                <h6>
                                    <i class="fas fa-calendar-alt"></i> 回款提醒
                                </h6>
                                @if (daysRemaining < 0)
                                {
                                    <p class="mb-0">已逾期 @Math.Abs(daysRemaining) 天</p>
                                }
                                else if (daysRemaining == 0)
                                {
                                    <p class="mb-0">今天是预期回款日</p>
                                }
                                else
                                {
                                    <p class="mb-0">距离预期回款还有 @daysRemaining 天</p>
                                }
                            </div>
                        }
                    </div>
                </div>

                <!-- 快捷导航 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-compass"></i> 快捷导航
                        </h3>
                    </div>
                    <div class="card-body">
                        <a asp-action="Index" class="btn btn-outline-primary btn-block">
                            <i class="fas fa-list"></i> 返回款项列表
                        </a>
                        <a asp-action="Create" class="btn btn-outline-success btn-block">
                            <i class="fas fa-plus"></i> 新增款项
                        </a>
                        <a asp-controller="Order" asp-action="Details" asp-route-id="@Model.OrderId" class="btn btn-outline-info btn-block">
                            <i class="fas fa-file-invoice"></i> 查看关联订单
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>您确定要删除款项 <strong id="deletePaymentName"></strong> 吗？</p>
                <p class="text-danger"><small>删除款项前请确保该款项下没有发票记录！</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(paymentId, paymentName) {
            $('#deletePaymentName').text(paymentName);
            $('#deleteForm').attr('action', '@Url.Action("Delete")/' + paymentId);
            $('#deleteModal').modal('show');
        }

        function markAsInvoiced() {
            if (confirm('确定要标记为已开票吗？')) {
                // 这里可以添加AJAX调用来更新开票状态
                toastr.info('功能开发中...');
            }
        }

        function markAsPaid() {
            if (confirm('确定要标记为已回款吗？')) {
                // 这里可以添加AJAX调用来更新回款状态
                toastr.info('功能开发中...');
            }
        }
    </script>
}

@section Styles {
    <style>
        .badge-lg {
            font-size: 0.9em;
            padding: 0.5em 0.75em;
        }
        
        .info-box {
            margin-bottom: 1rem;
        }
    </style>
}
