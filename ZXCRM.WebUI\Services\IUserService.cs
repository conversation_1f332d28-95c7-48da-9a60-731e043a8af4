using ZXCRM.Service.DTOs;
using ZXCRM.WebUI.Models;

namespace ZXCRM.WebUI.Services
{
    public interface IUserService
    {
        Task<ApiResponse<List<UserDTO>>> GetUsersAsync();
        Task<ApiResponse<UserDTO>> GetUserByIdAsync(int id);
        Task<ApiResponse<UserDTO>> CreateUserAsync(CreateUserD<PERSON> request);
        Task<ApiResponse<UserDTO>> UpdateUserAsync(int id, UpdateUserDTO request);
        Task<ApiResponse<object>> DeleteUserAsync(int id);
        Task<ApiResponse<List<DepartmentDTO>>> GetDepartmentsAsync();
        Task<bool> CheckUserPermissionAsync(int userId, string moduleType, string permissionCode);
    }
}
