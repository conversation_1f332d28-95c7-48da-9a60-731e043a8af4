using ZXCRM.WebUI.Services;
using System.Security.Claims;

namespace ZXCRM.WebUI.Middleware
{
    public class PermissionMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<PermissionMiddleware> _logger;

        // 定义需要权限检查的路由映射
        private readonly Dictionary<string, (string Module, string Permission)> _routePermissions = new()
        {
            // 商机管理
            { "/opportunity", ("商机管理", "Query") },
            { "/opportunity/index", ("商机管理", "Query") },
            { "/opportunity/details", ("商机管理", "Query") },
            { "/opportunity/create", ("商机管理", "Create") },
            { "/opportunity/edit", ("商机管理", "Edit") },
            { "/opportunity/delete", ("商机管理", "Delete") },

            // 订单管理
            { "/order", ("订单管理", "Query") },
            { "/order/index", ("订单管理", "Query") },
            { "/order/details", ("订单管理", "Query") },
            { "/order/create", ("订单管理", "Create") },
            { "/order/edit", ("订单管理", "Edit") },
            { "/order/delete", ("订单管理", "Delete") },

            // 款项管理
            { "/payment", ("款项管理", "Query") },
            { "/payment/index", ("款项管理", "Query") },
            { "/payment/details", ("款项管理", "Query") },
            { "/payment/create", ("款项管理", "Create") },
            { "/payment/edit", ("款项管理", "Edit") },
            { "/payment/delete", ("款项管理", "Delete") },

            // 发票管理
            { "/invoice", ("发票管理", "Query") },
            { "/invoice/index", ("发票管理", "Query") },
            { "/invoice/details", ("发票管理", "Query") },
            { "/invoice/create", ("发票管理", "Create") },
            { "/invoice/edit", ("发票管理", "Edit") },
            { "/invoice/delete", ("发票管理", "Delete") },

            // 报表统计
            { "/report", ("报表统计", "Query") },
            { "/report/index", ("报表统计", "Query") },

            // 用户管理
            { "/user", ("用户管理", "Query") },
            { "/user/index", ("用户管理", "Query") },
            { "/user/details", ("用户管理", "Query") },
            { "/user/create", ("用户管理", "Create") },
            { "/user/edit", ("用户管理", "Edit") },
            { "/user/delete", ("用户管理", "Delete") },

            // 部门管理
            { "/department", ("部门管理", "Query") },
            { "/department/index", ("部门管理", "Query") },
            { "/department/details", ("部门管理", "Query") },
            { "/department/create", ("部门管理", "Create") },
            { "/department/edit", ("部门管理", "Edit") },
            { "/department/delete", ("部门管理", "Delete") },

            // 权限管理
            { "/permission", ("权限管理", "Query") },
            { "/permission/index", ("权限管理", "Query") },
            { "/permission/userpermissions", ("权限管理", "Edit") },
            { "/permission/details", ("权限管理", "Query") },
            { "/permission/create", ("权限管理", "Create") },
            { "/permission/edit", ("权限管理", "Edit") },
            { "/permission/delete", ("权限管理", "Delete") }
        };

        // 不需要权限检查的路径
        private readonly string[] _excludedPaths = {
            "/",
            "/home",
            "/home/<USER>",
            "/account/login",
            "/account/logout",
            "/account/accessdenied",
            "/error",
            "/debug"
        };

        public PermissionMiddleware(RequestDelegate next, ILogger<PermissionMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // 检查是否需要权限验证
            if (ShouldCheckPermission(context))
            {
                var permissionService = context.RequestServices.GetRequiredService<IPermissionCheckService>();
                
                if (!await CheckPermissionAsync(context, permissionService))
                {
                    await HandleAccessDeniedAsync(context);
                    return;
                }
            }

            await _next(context);
        }

        private bool ShouldCheckPermission(HttpContext context)
        {
            // 检查用户是否已登录
            if (!context.User.Identity?.IsAuthenticated == true)
            {
                return false;
            }

            var path = context.Request.Path.Value?.ToLowerInvariant() ?? "";

            // 排除不需要检查的路径
            if (_excludedPaths.Any(excluded => path.StartsWith(excluded)))
            {
                return false;
            }

            // 排除静态资源
            if (path.StartsWith("/css") || path.StartsWith("/js") || path.StartsWith("/images") || 
                path.StartsWith("/lib") || path.StartsWith("/favicon"))
            {
                return false;
            }

            // 排除API路径（API有自己的权限检查）
            if (path.StartsWith("/api"))
            {
                return false;
            }

            return true;
        }

        private async Task<bool> CheckPermissionAsync(HttpContext context, IPermissionCheckService permissionService)
        {
            try
            {
                var userId = context.User.GetUserId();
                if (userId == 0)
                {
                    _logger.LogWarning("User ID not found in claims");
                    return false;
                }

                var path = context.Request.Path.Value?.ToLowerInvariant() ?? "";
                
                // 查找匹配的路由权限
                var matchedRoute = _routePermissions.FirstOrDefault(kvp => 
                    path.StartsWith(kvp.Key) || path == kvp.Key);

                if (matchedRoute.Key != null)
                {
                    var (module, permission) = matchedRoute.Value;
                    var hasPermission = await permissionService.HasPermissionAsync(userId, module, permission);

                    _logger.LogDebug("Permission check for user {UserId} on {Path}: {Module}.{Permission} = {HasPermission}", 
                        userId, path, module, permission, hasPermission);

                    if (!hasPermission)
                    {
                        // 对于业务模块，允许访问但在页面中显示权限不足
                        if (permissionService.IsBusinessModule(module))
                        {
                            context.Items["PermissionDenied"] = true;
                            context.Items["RequiredModule"] = module;
                            context.Items["RequiredPermission"] = permission;
                            return true; // 允许访问，但标记权限不足
                        }
                        
                        // 对于系统模块，直接拒绝访问
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking permission for path {Path}", context.Request.Path);
                return false;
            }
        }

        private async Task HandleAccessDeniedAsync(HttpContext context)
        {
            _logger.LogWarning("Access denied for user {UserId} to path {Path}", 
                context.User.GetUserId(), context.Request.Path);

            // 如果是AJAX请求，返回JSON
            if (context.Request.Headers["X-Requested-With"] == "XMLHttpRequest")
            {
                context.Response.StatusCode = 403;
                context.Response.ContentType = "application/json";
                await context.Response.WriteAsync("{\"error\":\"权限不足\",\"message\":\"您没有访问此功能的权限\"}");
            }
            else
            {
                // 重定向到权限不足页面
                context.Response.Redirect("/Account/AccessDenied?returnUrl=" + Uri.EscapeDataString(context.Request.Path));
            }
        }
    }

    /// <summary>
    /// 权限中间件扩展
    /// </summary>
    public static class PermissionMiddlewareExtensions
    {
        public static IApplicationBuilder UsePermissionCheck(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<PermissionMiddleware>();
        }
    }
}
