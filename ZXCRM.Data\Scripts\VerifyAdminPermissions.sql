-- =============================================
-- ZXCRM系统 - Admin权限验证脚本
-- 创建日期: 2024年
-- 说明: 验证admin账号的权限分配是否正确
-- 使用方法: 在初始化后执行此脚本进行验证
-- =============================================

USE [ZXCRM]
GO

PRINT '=========================================='
PRINT 'ZXCRM系统 - Admin权限验证报告'
PRINT '=========================================='
PRINT '生成时间: ' + CONVERT(VARCHAR(20), GETDATE(), 120)
PRINT ''

-- =============================================
-- 1. 验证admin用户基本信息
-- =============================================
PRINT '1. Admin用户基本信息:'
PRINT '------------------------------------------'

DECLARE @AdminUserId INT
DECLARE @AdminInfo TABLE (
    UserId INT,
    Username NVARCHAR(50),
    Name NVARCHAR(100),
    Role NVARCHAR(50),
    Status NVARCHAR(20),
    DepartmentName NVARCHAR(100),
    CreatedAt DATETIME
)

INSERT INTO @AdminInfo
SELECT 
    u.[Id],
    u.[Username],
    u.[Name],
    u.[Role],
    u.[Status],
    d.[Name] AS DepartmentName,
    u.[CreatedAt]
FROM [Users] u
LEFT JOIN [Departments] d ON u.[DepartmentId] = d.[Id]
WHERE u.[Username] = 'admin'

SELECT @AdminUserId = UserId FROM @AdminInfo

IF @AdminUserId IS NOT NULL
BEGIN
    SELECT 
        '用户ID' AS [属性], CAST(UserId AS NVARCHAR(50)) AS [值] FROM @AdminInfo
    UNION ALL
    SELECT '用户名', Username FROM @AdminInfo
    UNION ALL
    SELECT '姓名', Name FROM @AdminInfo
    UNION ALL
    SELECT '角色', Role FROM @AdminInfo
    UNION ALL
    SELECT '状态', Status FROM @AdminInfo
    UNION ALL
    SELECT '部门', DepartmentName FROM @AdminInfo
    UNION ALL
    SELECT '创建时间', CONVERT(NVARCHAR(20), CreatedAt, 120) FROM @AdminInfo
    
    PRINT '✓ Admin用户信息正常'
END
ELSE
BEGIN
    PRINT '❌ 未找到admin用户！'
    PRINT '请先执行InitializeAdminPermissions.sql脚本'
    RETURN
END

PRINT ''

-- =============================================
-- 2. 验证权限类型
-- =============================================
PRINT '2. 基础权限类型验证:'
PRINT '------------------------------------------'

SELECT 
    [Name] AS [权限名称],
    [Code] AS [权限代码],
    [Description] AS [描述]
FROM [Permissions]
ORDER BY [Code]

DECLARE @PermissionCount INT
SELECT @PermissionCount = COUNT(*) FROM [Permissions]
PRINT '权限类型总数: ' + CAST(@PermissionCount AS VARCHAR(10))

IF @PermissionCount >= 4
    PRINT '✓ 基础权限类型完整'
ELSE
    PRINT '⚠ 权限类型不完整，建议检查'

PRINT ''

-- =============================================
-- 3. 验证admin权限分配
-- =============================================
PRINT '3. Admin权限分配详情:'
PRINT '------------------------------------------'

-- 按模块统计权限
SELECT 
    up.[ModuleType] AS [模块名称],
    COUNT(*) AS [权限数量],
    STRING_AGG(p.[Code], ', ') AS [权限列表]
FROM [UserPermissions] up
INNER JOIN [Permissions] p ON up.[PermissionId] = p.[Id]
WHERE up.[UserId] = @AdminUserId
GROUP BY up.[ModuleType]
ORDER BY up.[ModuleType]

-- 统计总权限数
DECLARE @AdminTotalPermissions INT
SELECT @AdminTotalPermissions = COUNT(*) FROM [UserPermissions] WHERE [UserId] = @AdminUserId

PRINT ''
PRINT 'Admin权限统计:'
PRINT '- 总权限数: ' + CAST(@AdminTotalPermissions AS VARCHAR(10))
PRINT '- 预期权限数: 32 (8个模块 × 4种权限)'

IF @AdminTotalPermissions = 32
    PRINT '✓ Admin权限分配完整'
ELSE IF @AdminTotalPermissions > 32
    PRINT '⚠ Admin权限数量超出预期'
ELSE
    PRINT '❌ Admin权限分配不完整'

PRINT ''

-- =============================================
-- 4. 验证权限完整性
-- =============================================
PRINT '4. 权限完整性检查:'
PRINT '------------------------------------------'

-- 检查每个模块是否都有4种权限
DECLARE @ModulePermissionCheck TABLE (
    ModuleName NVARCHAR(50),
    PermissionCount INT,
    MissingPermissions NVARCHAR(200)
)

INSERT INTO @ModulePermissionCheck
SELECT 
    ModuleType,
    COUNT(*) AS PermissionCount,
    CASE 
        WHEN COUNT(*) < 4 THEN '缺少权限'
        ELSE '完整'
    END AS MissingPermissions
FROM [UserPermissions] up
WHERE up.[UserId] = @AdminUserId
GROUP BY ModuleType

SELECT 
    [ModuleName] AS [模块名称],
    [PermissionCount] AS [权限数量],
    [MissingPermissions] AS [状态]
FROM @ModulePermissionCheck
ORDER BY [ModuleName]

-- 检查是否有遗漏的模块
PRINT ''
PRINT '模块覆盖检查:'
DECLARE @ExpectedModules TABLE (ModuleName NVARCHAR(50))
INSERT INTO @ExpectedModules VALUES 
    ('用户管理'), ('部门管理'), ('权限管理'),
    ('商机管理'), ('订单管理'), ('款项管理'), ('发票管理'), ('报表统计')

SELECT 
    em.ModuleName AS [预期模块],
    CASE 
        WHEN up.ModuleType IS NOT NULL THEN '✓ 已分配'
        ELSE '❌ 未分配'
    END AS [分配状态]
FROM @ExpectedModules em
LEFT JOIN (
    SELECT DISTINCT ModuleType 
    FROM [UserPermissions] 
    WHERE UserId = @AdminUserId
) up ON em.ModuleName = up.ModuleType
ORDER BY em.ModuleName

PRINT ''

-- =============================================
-- 5. 安全建议
-- =============================================
PRINT '5. 安全建议:'
PRINT '------------------------------------------'

-- 检查密码是否为默认密码
DECLARE @IsDefaultPassword BIT = 0
SELECT @IsDefaultPassword = 1 
FROM [Users] 
WHERE [Username] = 'admin' 
  AND [Password] = 'AQAAAAEAACcQAAAAEK8VJJzOzMqLjKvQjKvQjKvQjKvQjKvQjKvQjKvQjKvQjKvQjKvQjKvQjKvQjKvQ=='

IF @IsDefaultPassword = 1
    PRINT '⚠ 检测到admin仍使用默认密码，强烈建议立即修改！'
ELSE
    PRINT '✓ Admin密码已修改'

-- 检查最近登录时间（如果有LastLoginAt字段）
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[Users]') AND name = 'LastLoginAt')
BEGIN
    DECLARE @LastLogin DATETIME
    SELECT @LastLogin = [LastLoginAt] FROM [Users] WHERE [Username] = 'admin'
    
    IF @LastLogin IS NULL
        PRINT '- Admin账号尚未登录过'
    ELSE
        PRINT '- Admin最后登录时间: ' + CONVERT(VARCHAR(20), @LastLogin, 120)
END

PRINT ''
PRINT '安全建议清单:'
PRINT '□ 修改admin默认密码'
PRINT '□ 启用双因素认证'
PRINT '□ 限制admin登录IP范围'
PRINT '□ 定期审计admin操作日志'
PRINT '□ 设置密码复杂度策略'
PRINT '□ 启用账号锁定策略'

PRINT ''

-- =============================================
-- 6. 生成权限矩阵报告
-- =============================================
PRINT '6. Admin权限矩阵:'
PRINT '------------------------------------------'

-- 创建权限矩阵视图
SELECT 
    ModuleType AS [模块],
    MAX(CASE WHEN p.Code = 'Query' THEN '✓' ELSE '✗' END) AS [查询],
    MAX(CASE WHEN p.Code = 'Create' THEN '✓' ELSE '✗' END) AS [新建],
    MAX(CASE WHEN p.Code = 'Edit' THEN '✓' ELSE '✗' END) AS [修改],
    MAX(CASE WHEN p.Code = 'Delete' THEN '✓' ELSE '✗' END) AS [删除]
FROM [UserPermissions] up
INNER JOIN [Permissions] p ON up.PermissionId = p.Id
WHERE up.UserId = @AdminUserId
GROUP BY ModuleType
ORDER BY ModuleType

PRINT ''
PRINT '=========================================='
PRINT 'Admin权限验证完成！'
PRINT '=========================================='

-- 最终验证结果
IF @AdminTotalPermissions = 32 AND @AdminUserId IS NOT NULL
BEGIN
    PRINT '✓ 验证结果: 通过'
    PRINT '✓ Admin账号已正确配置完整权限'
END
ELSE
BEGIN
    PRINT '❌ 验证结果: 失败'
    PRINT '❌ 请检查权限配置并重新初始化'
END

PRINT '验证时间: ' + CONVERT(VARCHAR(20), GETDATE(), 120)
PRINT '=========================================='
