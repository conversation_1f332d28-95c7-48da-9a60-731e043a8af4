using System.ComponentModel.DataAnnotations;

namespace ZXCRM.WebUI.Models.ViewModels
{
    public class OpportunityListViewModel
    {
        public List<OpportunityItemViewModel> Opportunities { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageIndex { get; set; }
        public int PageSize { get; set; }
        public string SearchTerm { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
    }

    public class OpportunityItemViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string? ContactName { get; set; }
        public string? ContactPhone { get; set; }
        public string? Content { get; set; }
        public string Status { get; set; } = string.Empty;
        public string CreatedByName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        
        // 关联订单数量
        public int OrderCount { get; set; }
    }

    public class CreateOpportunityViewModel
    {
        [Required(ErrorMessage = "商机名称不能为空")]
        [StringLength(200, ErrorMessage = "商机名称长度不能超过200个字符")]
        [Display(Name = "商机名称")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "客户名称不能为空")]
        [StringLength(200, ErrorMessage = "客户名称长度不能超过200个字符")]
        [Display(Name = "客户名称")]
        public string CustomerName { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "联系人长度不能超过100个字符")]
        [Display(Name = "联系人")]
        public string? ContactName { get; set; }

        [StringLength(20, ErrorMessage = "联系电话长度不能超过20个字符")]
        [Display(Name = "联系电话")]
        public string? ContactPhone { get; set; }

        [StringLength(1000, ErrorMessage = "商机内容长度不能超过1000个字符")]
        [Display(Name = "商机内容")]
        public string? Content { get; set; }

        [Required(ErrorMessage = "请选择商机状态")]
        [Display(Name = "商机状态")]
        public string Status { get; set; } = "潜在";
    }

    public class EditOpportunityViewModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "商机名称不能为空")]
        [StringLength(200, ErrorMessage = "商机名称长度不能超过200个字符")]
        [Display(Name = "商机名称")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "客户名称不能为空")]
        [StringLength(200, ErrorMessage = "客户名称长度不能超过200个字符")]
        [Display(Name = "客户名称")]
        public string CustomerName { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "联系人长度不能超过100个字符")]
        [Display(Name = "联系人")]
        public string? ContactName { get; set; }

        [StringLength(20, ErrorMessage = "联系电话长度不能超过20个字符")]
        [Display(Name = "联系电话")]
        public string? ContactPhone { get; set; }

        [StringLength(1000, ErrorMessage = "商机内容长度不能超过1000个字符")]
        [Display(Name = "商机内容")]
        public string? Content { get; set; }

        [Required(ErrorMessage = "请选择商机状态")]
        [Display(Name = "商机状态")]
        public string Status { get; set; } = string.Empty;
    }

    public class OpportunityDetailViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string? ContactName { get; set; }
        public string? ContactPhone { get; set; }
        public string? Content { get; set; }
        public string Status { get; set; } = string.Empty;
        public string CreatedByName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        
        // 关联订单信息
        public List<RelatedOrderViewModel> RelatedOrders { get; set; } = new();
    }

    public class RelatedOrderViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
    }

    // 商机状态枚举（用于下拉选择）
    public static class OpportunityStatus
    {
        public const string Potential = "潜在";
        public const string Qualified = "合格";
        public const string Proposal = "方案";
        public const string Negotiation = "谈判";
        public const string Won = "成交";
        public const string Lost = "失败";
        public const string Cancelled = "取消";

        public static List<string> GetAllStatuses()
        {
            return new List<string>
            {
                Potential,
                Qualified,
                Proposal,
                Negotiation,
                Won,
                Lost,
                Cancelled
            };
        }

        public static string GetStatusColor(string status)
        {
            return status switch
            {
                Potential => "secondary",
                Qualified => "info",
                Proposal => "warning",
                Negotiation => "primary",
                Won => "success",
                Lost => "danger",
                Cancelled => "dark",
                _ => "light"
            };
        }

        public static string GetStatusIcon(string status)
        {
            return status switch
            {
                Potential => "fas fa-seedling",
                Qualified => "fas fa-check-circle",
                Proposal => "fas fa-file-alt",
                Negotiation => "fas fa-handshake",
                Won => "fas fa-trophy",
                Lost => "fas fa-times-circle",
                Cancelled => "fas fa-ban",
                _ => "fas fa-question"
            };
        }
    }
}
