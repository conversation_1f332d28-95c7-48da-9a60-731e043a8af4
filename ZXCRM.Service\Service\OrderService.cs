using AutoMapper;
using ZXCRM.Data.Entities;
using ZXCRM.Data.UnitOfWork;
using ZXCRM.Service.DTOs;
using ZXCRM.Service.Interface;

namespace ZXCRM.Service.Service
{
    public class OrderService : IOrderService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public OrderService(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task<IEnumerable<OrderDTO>> GetAllOrdersAsync()
        {
            var orders = await _unitOfWork.Orders.GetAllWithIncludesAsync(
                o => o.AccountManager,
                o => o.ProjectManager,
                o => o.Department,
                o => o.PerformanceDepartment,
                o => o.CreatedBy,
                o => o.Opportunity
            );
            return _mapper.Map<IEnumerable<OrderDTO>>(orders);
        }

        public async Task<IEnumerable<OrderDTO>> GetOrdersByUserIdAsync(int userId)
        {
            var orders = await _unitOfWork.Orders.FindWithIncludesAsync(
                o => o.CreatedById == userId || o.AccountManagerId == userId || o.ProjectManagerId == userId,
                o => o.AccountManager,
                o => o.ProjectManager,
                o => o.Department,
                o => o.PerformanceDepartment,
                o => o.CreatedBy,
                o => o.Opportunity
            );
            return _mapper.Map<IEnumerable<OrderDTO>>(orders);
        }

        public async Task<OrderDTO?> GetOrderByIdAsync(int id)
        {
            var order = await _unitOfWork.Orders.GetByIdWithIncludesAsync(id,
                o => o.AccountManager,
                o => o.ProjectManager,
                o => o.Department,
                o => o.PerformanceDepartment,
                o => o.CreatedBy,
                o => o.Opportunity
            );
            return order != null ? _mapper.Map<OrderDTO>(order) : null;
        }

        public async Task<OrderDTO> CreateOrderAsync(CreateOrderDTO createOrderDto)
        {
            // 检查用户是否存在
            var user = await _unitOfWork.Users.GetByIdAsync(createOrderDto.CreatedById);
            if (user == null)
            {
                throw new Exception("创建人不存在");
            }

            // 检查客户经理是否存在
            var accountManager = await _unitOfWork.Users.GetByIdAsync(createOrderDto.AccountManagerId);
            if (accountManager == null)
            {
                throw new Exception("客户经理不存在");
            }

            // 检查部门是否存在
            var department = await _unitOfWork.Departments.GetByIdAsync(createOrderDto.DepartmentId);
            if (department == null)
            {
                throw new Exception("创建部门不存在");
            }

            // 检查项目经理是否存在
            if (createOrderDto.ProjectManagerId.HasValue)
            {
                var projectManager = await _unitOfWork.Users.GetByIdAsync(createOrderDto.ProjectManagerId.Value);
                if (projectManager == null)
                {
                    throw new Exception("项目经理不存在");
                }
            }

            // 检查业绩归属部门是否存在
            if (createOrderDto.PerformanceDepartmentId.HasValue)
            {
                var performanceDepartment = await _unitOfWork.Departments.GetByIdAsync(createOrderDto.PerformanceDepartmentId.Value);
                if (performanceDepartment == null)
                {
                    throw new Exception("业绩归属部门不存在");
                }
            }

            // 检查商机是否存在
            if (createOrderDto.OpportunityId.HasValue)
            {
                var opportunity = await _unitOfWork.Opportunities.GetByIdAsync(createOrderDto.OpportunityId.Value);
                if (opportunity == null)
                {
                    throw new Exception("关联商机不存在");
                }
            }

            // 创建订单
            var order = _mapper.Map<Order>(createOrderDto);
            order.CreatedAt = DateTime.Now;
            order.UpdatedAt = DateTime.Now;

            await _unitOfWork.Orders.AddAsync(order);
            await _unitOfWork.SaveChangesAsync();

            // 重新获取包含关联数据的订单
            var createdOrder = await _unitOfWork.Orders.GetByIdWithIncludesAsync(order.Id,
                o => o.AccountManager,
                o => o.ProjectManager,
                o => o.Department,
                o => o.PerformanceDepartment,
                o => o.CreatedBy,
                o => o.Opportunity
            );

            return _mapper.Map<OrderDTO>(createdOrder);
        }

        public async Task<OrderDTO?> UpdateOrderAsync(UpdateOrderDTO updateOrderDto)
        {
            var order = await _unitOfWork.Orders.GetByIdAsync(updateOrderDto.Id);
            if (order == null)
            {
                return null;
            }

            // 检查客户经理是否存在
            var accountManager = await _unitOfWork.Users.GetByIdAsync(updateOrderDto.AccountManagerId);
            if (accountManager == null)
            {
                throw new Exception("客户经理不存在");
            }

            // 检查部门是否存在
            var department = await _unitOfWork.Departments.GetByIdAsync(updateOrderDto.DepartmentId);
            if (department == null)
            {
                throw new Exception("创建部门不存在");
            }

            // 检查项目经理是否存在
            if (updateOrderDto.ProjectManagerId.HasValue)
            {
                var projectManager = await _unitOfWork.Users.GetByIdAsync(updateOrderDto.ProjectManagerId.Value);
                if (projectManager == null)
                {
                    throw new Exception("项目经理不存在");
                }
            }

            // 检查业绩归属部门是否存在
            if (updateOrderDto.PerformanceDepartmentId.HasValue)
            {
                var performanceDepartment = await _unitOfWork.Departments.GetByIdAsync(updateOrderDto.PerformanceDepartmentId.Value);
                if (performanceDepartment == null)
                {
                    throw new Exception("业绩归属部门不存在");
                }
            }

            // 检查商机是否存在
            if (updateOrderDto.OpportunityId.HasValue)
            {
                var opportunity = await _unitOfWork.Opportunities.GetByIdAsync(updateOrderDto.OpportunityId.Value);
                if (opportunity == null)
                {
                    throw new Exception("关联商机不存在");
                }
            }

            // 更新订单信息
            order.Name = updateOrderDto.Name;
            order.Code = updateOrderDto.Code;
            order.CustomerName = updateOrderDto.CustomerName;
            order.ContactName = updateOrderDto.ContactName;
            order.ContactPhone = updateOrderDto.ContactPhone;
            order.OpportunityId = updateOrderDto.OpportunityId;
            order.Amount = updateOrderDto.Amount;
            order.Currency = updateOrderDto.Currency;
            order.SettlementAmount = updateOrderDto.SettlementAmount;
            order.AccountManagerId = updateOrderDto.AccountManagerId;
            order.DepartmentId = updateOrderDto.DepartmentId;
            order.SignDate = updateOrderDto.SignDate;
            order.Status = updateOrderDto.Status;
            order.ProjectManagerId = updateOrderDto.ProjectManagerId;
            order.PerformanceDepartmentId = updateOrderDto.PerformanceDepartmentId;
            order.UpdatedAt = DateTime.Now;

            await _unitOfWork.Orders.UpdateAsync(order);
            await _unitOfWork.SaveChangesAsync();

            // 重新获取包含关联数据的订单
            var updatedOrder = await _unitOfWork.Orders.GetByIdWithIncludesAsync(order.Id,
                o => o.AccountManager,
                o => o.ProjectManager,
                o => o.Department,
                o => o.PerformanceDepartment,
                o => o.CreatedBy,
                o => o.Opportunity
            );

            return _mapper.Map<OrderDTO>(updatedOrder);
        }

        public async Task<bool> DeleteOrderAsync(int id)
        {
            var order = await _unitOfWork.Orders.GetByIdAsync(id);
            if (order == null)
            {
                return false;
            }

            // 检查是否有关联的款项
            var payments = await _unitOfWork.Payments.FindAsync(p => p.OrderId == id);
            if (payments.Any())
            {
                throw new Exception("该订单已关联款项，无法删除");
            }

            // 软删除
            order.IsDeleted = true;
            order.UpdatedAt = DateTime.Now;

            await _unitOfWork.Orders.UpdateAsync(order);
            await _unitOfWork.SaveChangesAsync();

            return true;
        }
    }
}
