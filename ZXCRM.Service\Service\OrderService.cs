using AutoMapper;
using Microsoft.Extensions.Logging;
using ZXCRM.Data.Entities;
using ZXCRM.Data.UnitOfWork;
using ZXCRM.Service.DTOs;
using ZXCRM.Service.Interface;

namespace ZXCRM.Service.Service
{
    public class OrderService : IOrderService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<OrderService> _logger;

        public OrderService(IUnitOfWork unitOfWork, IMapper mapper, ILogger<OrderService> logger)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<IEnumerable<OrderDTO>> GetAllOrdersAsync()
        {
            _logger.LogInformation("=== 开始获取订单列表 ===");

            var orders = await _unitOfWork.Orders.GetAllWithIncludesAsync(
                o => o.AccountManager,
                o => o.ProjectManager,
                o => o.Department,
                o => o.PerformanceDepartment,
                o => o.CreatedBy,
                o => o.Opportunity
            );

            _logger.LogInformation("从数据库获取到 {Count} 个订单", orders.Count());

            // 记录第一个订单的原始数据
            if (orders.Any())
            {
                var firstOrder = orders.First();
                _logger.LogInformation("第一个订单原始数据: ID={Id}, Name={Name}, Code={Code}, Amount={Amount}, Currency={Currency}, Status={Status}, CustomerName={CustomerName}",
                    firstOrder.Id, firstOrder.Name ?? "NULL", firstOrder.Code ?? "NULL", firstOrder.Amount, firstOrder.Currency ?? "NULL", firstOrder.Status ?? "NULL", firstOrder.CustomerName ?? "NULL");

                _logger.LogInformation("第一个订单关联数据: AccountManager={AccountManager}, Department={Department}, CreatedBy={CreatedBy}",
                    firstOrder.AccountManager?.Name ?? "NULL", firstOrder.Department?.Name ?? "NULL", firstOrder.CreatedBy?.Name ?? "NULL");
            }

            var orderDTOs = _mapper.Map<IEnumerable<OrderDTO>>(orders);

            _logger.LogInformation("AutoMapper映射完成，得到 {Count} 个OrderDTO", orderDTOs.Count());

            // 记录第一个DTO的数据
            if (orderDTOs.Any())
            {
                var firstDTO = orderDTOs.First();
                _logger.LogInformation("第一个OrderDTO数据: ID={Id}, Name={Name}, Code={Code}, Amount={Amount}, Currency={Currency}, Status={Status}, CustomerName={CustomerName}",
                    firstDTO.Id, firstDTO.Name ?? "NULL", firstDTO.Code ?? "NULL", firstDTO.Amount, firstDTO.Currency ?? "NULL", firstDTO.Status ?? "NULL", firstDTO.CustomerName ?? "NULL");

                _logger.LogInformation("第一个OrderDTO关联数据: AccountManagerName={AccountManagerName}, DepartmentName={DepartmentName}, CreatedByName={CreatedByName}",
                    firstDTO.AccountManagerName ?? "NULL", firstDTO.DepartmentName ?? "NULL", firstDTO.CreatedByName ?? "NULL");
            }

            _logger.LogInformation("=== 订单列表获取完成 ===");
            return orderDTOs;
        }

        public async Task<IEnumerable<OrderDTO>> GetOrdersByUserIdAsync(int userId)
        {
            var orders = await _unitOfWork.Orders.FindWithIncludesAsync(
                o => o.CreatedById == userId || o.AccountManagerId == userId || o.ProjectManagerId == userId,
                o => o.AccountManager,
                o => o.ProjectManager,
                o => o.Department,
                o => o.PerformanceDepartment,
                o => o.CreatedBy,
                o => o.Opportunity
            );
            return _mapper.Map<IEnumerable<OrderDTO>>(orders);
        }

        public async Task<OrderDTO?> GetOrderByIdAsync(int id)
        {
            _logger.LogInformation("=== 开始获取订单详情 ID={Id} ===", id);

            var order = await _unitOfWork.Orders.GetByIdWithIncludesAsync(id,
                o => o.AccountManager,
                o => o.ProjectManager,
                o => o.Department,
                o => o.PerformanceDepartment,
                o => o.CreatedBy,
                o => o.Opportunity
            );

            if (order == null)
            {
                _logger.LogWarning("订单不存在: ID={Id}", id);
                return null;
            }

            _logger.LogInformation("订单原始数据: ID={Id}, Name={Name}, Code={Code}, Amount={Amount}, Currency={Currency}, Status={Status}, CustomerName={CustomerName}",
                order.Id, order.Name ?? "NULL", order.Code ?? "NULL", order.Amount, order.Currency ?? "NULL", order.Status ?? "NULL", order.CustomerName ?? "NULL");

            _logger.LogInformation("订单关联数据: AccountManager={AccountManager}, Department={Department}, CreatedBy={CreatedBy}",
                order.AccountManager?.Name ?? "NULL", order.Department?.Name ?? "NULL", order.CreatedBy?.Name ?? "NULL");

            var orderDTO = _mapper.Map<OrderDTO>(order);

            _logger.LogInformation("OrderDTO映射数据: ID={Id}, Name={Name}, Code={Code}, Amount={Amount}, Currency={Currency}, Status={Status}, CustomerName={CustomerName}",
                orderDTO.Id, orderDTO.Name ?? "NULL", orderDTO.Code ?? "NULL", orderDTO.Amount, orderDTO.Currency ?? "NULL", orderDTO.Status ?? "NULL", orderDTO.CustomerName ?? "NULL");

            _logger.LogInformation("OrderDTO关联数据: AccountManagerName={AccountManagerName}, DepartmentName={DepartmentName}, CreatedByName={CreatedByName}",
                orderDTO.AccountManagerName ?? "NULL", orderDTO.DepartmentName ?? "NULL", orderDTO.CreatedByName ?? "NULL");

            _logger.LogInformation("=== 订单详情获取完成 ===");
            return orderDTO;
        }

        public async Task<OrderDTO> CreateOrderAsync(CreateOrderDTO createOrderDto)
        {
            // 检查用户是否存在
            var user = await _unitOfWork.Users.GetByIdAsync(createOrderDto.CreatedById);
            if (user == null)
            {
                throw new Exception("创建人不存在");
            }

            // 检查客户经理是否存在
            var accountManager = await _unitOfWork.Users.GetByIdAsync(createOrderDto.AccountManagerId);
            if (accountManager == null)
            {
                throw new Exception("客户经理不存在");
            }

            // 检查部门是否存在
            var department = await _unitOfWork.Departments.GetByIdAsync(createOrderDto.DepartmentId);
            if (department == null)
            {
                throw new Exception("创建部门不存在");
            }

            // 检查项目经理是否存在
            if (createOrderDto.ProjectManagerId.HasValue)
            {
                var projectManager = await _unitOfWork.Users.GetByIdAsync(createOrderDto.ProjectManagerId.Value);
                if (projectManager == null)
                {
                    throw new Exception("项目经理不存在");
                }
            }

            // 检查业绩归属部门是否存在
            if (createOrderDto.PerformanceDepartmentId.HasValue)
            {
                var performanceDepartment = await _unitOfWork.Departments.GetByIdAsync(createOrderDto.PerformanceDepartmentId.Value);
                if (performanceDepartment == null)
                {
                    throw new Exception("业绩归属部门不存在");
                }
            }

            // 检查商机是否存在
            if (createOrderDto.OpportunityId.HasValue)
            {
                var opportunity = await _unitOfWork.Opportunities.GetByIdAsync(createOrderDto.OpportunityId.Value);
                if (opportunity == null)
                {
                    throw new Exception("关联商机不存在");
                }
            }

            // 创建订单
            var order = _mapper.Map<Order>(createOrderDto);
            order.CreatedAt = DateTime.Now;
            order.UpdatedAt = DateTime.Now;

            await _unitOfWork.Orders.AddAsync(order);
            await _unitOfWork.SaveChangesAsync();

            // 重新获取包含关联数据的订单
            var createdOrder = await _unitOfWork.Orders.GetByIdWithIncludesAsync(order.Id,
                o => o.AccountManager,
                o => o.ProjectManager,
                o => o.Department,
                o => o.PerformanceDepartment,
                o => o.CreatedBy,
                o => o.Opportunity
            );

            return _mapper.Map<OrderDTO>(createdOrder);
        }

        public async Task<OrderDTO?> UpdateOrderAsync(UpdateOrderDTO updateOrderDto)
        {
            var order = await _unitOfWork.Orders.GetByIdAsync(updateOrderDto.Id);
            if (order == null)
            {
                return null;
            }

            // 检查客户经理是否存在
            var accountManager = await _unitOfWork.Users.GetByIdAsync(updateOrderDto.AccountManagerId);
            if (accountManager == null)
            {
                throw new Exception("客户经理不存在");
            }

            // 检查部门是否存在
            var department = await _unitOfWork.Departments.GetByIdAsync(updateOrderDto.DepartmentId);
            if (department == null)
            {
                throw new Exception("创建部门不存在");
            }

            // 检查项目经理是否存在
            if (updateOrderDto.ProjectManagerId.HasValue)
            {
                var projectManager = await _unitOfWork.Users.GetByIdAsync(updateOrderDto.ProjectManagerId.Value);
                if (projectManager == null)
                {
                    throw new Exception("项目经理不存在");
                }
            }

            // 检查业绩归属部门是否存在
            if (updateOrderDto.PerformanceDepartmentId.HasValue)
            {
                var performanceDepartment = await _unitOfWork.Departments.GetByIdAsync(updateOrderDto.PerformanceDepartmentId.Value);
                if (performanceDepartment == null)
                {
                    throw new Exception("业绩归属部门不存在");
                }
            }

            // 检查商机是否存在
            if (updateOrderDto.OpportunityId.HasValue)
            {
                var opportunity = await _unitOfWork.Opportunities.GetByIdAsync(updateOrderDto.OpportunityId.Value);
                if (opportunity == null)
                {
                    throw new Exception("关联商机不存在");
                }
            }

            // 更新订单信息
            order.Name = updateOrderDto.Name;
            order.Code = updateOrderDto.Code;
            order.CustomerName = updateOrderDto.CustomerName;
            order.ContactName = updateOrderDto.ContactName;
            order.ContactPhone = updateOrderDto.ContactPhone;
            order.OpportunityId = updateOrderDto.OpportunityId;
            order.Amount = updateOrderDto.Amount;
            order.Currency = updateOrderDto.Currency;
            order.SettlementAmount = updateOrderDto.SettlementAmount;
            order.AccountManagerId = updateOrderDto.AccountManagerId;
            order.DepartmentId = updateOrderDto.DepartmentId;
            order.SignDate = updateOrderDto.SignDate;
            order.Status = updateOrderDto.Status;
            order.ProjectManagerId = updateOrderDto.ProjectManagerId;
            order.PerformanceDepartmentId = updateOrderDto.PerformanceDepartmentId;
            order.UpdatedAt = DateTime.Now;

            await _unitOfWork.Orders.UpdateAsync(order);
            await _unitOfWork.SaveChangesAsync();

            // 重新获取包含关联数据的订单
            var updatedOrder = await _unitOfWork.Orders.GetByIdWithIncludesAsync(order.Id,
                o => o.AccountManager,
                o => o.ProjectManager,
                o => o.Department,
                o => o.PerformanceDepartment,
                o => o.CreatedBy,
                o => o.Opportunity
            );

            return _mapper.Map<OrderDTO>(updatedOrder);
        }

        public async Task<bool> DeleteOrderAsync(int id)
        {
            var order = await _unitOfWork.Orders.GetByIdAsync(id);
            if (order == null)
            {
                return false;
            }

            // 检查是否有关联的款项
            var payments = await _unitOfWork.Payments.FindAsync(p => p.OrderId == id);
            if (payments.Any())
            {
                throw new Exception("该订单已关联款项，无法删除");
            }

            // 软删除
            order.IsDeleted = true;
            order.UpdatedAt = DateTime.Now;

            await _unitOfWork.Orders.UpdateAsync(order);
            await _unitOfWork.SaveChangesAsync();

            return true;
        }
    }
}
