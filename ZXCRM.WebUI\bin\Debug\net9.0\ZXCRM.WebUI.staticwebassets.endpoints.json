{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "css/site.c5jsywe0fz.css", "AssetFile": "css/site.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000239177230"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4180"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LF247tm+Ygw3W+aMphNfMTDK72I9bXMullzV3MzwHuY=\""}, {"Name": "ETag", "Value": "W/\"EO6RHZxTE5HHwzt4Grc+kq2k/kiX34rn5FR3qW1y7LY=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 08:15:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c5jsywe0fz"}, {"Name": "integrity", "Value": "sha256-EO6RHZxTE5HHwzt4Grc+kq2k/kiX34rn5FR3qW1y7LY="}, {"Name": "label", "Value": "css/site.css"}]}, {"Route": "css/site.c5jsywe0fz.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "19079"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"EO6RHZxTE5HHwzt4Grc+kq2k/kiX34rn5FR3qW1y7LY=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:29:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c5jsywe0fz"}, {"Name": "integrity", "Value": "sha256-EO6RHZxTE5HHwzt4Grc+kq2k/kiX34rn5FR3qW1y7LY="}, {"Name": "label", "Value": "css/site.css"}]}, {"Route": "css/site.c5jsywe0fz.css.gz", "AssetFile": "css/site.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4180"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LF247tm+Ygw3W+aMphNfMTDK72I9bXMullzV3MzwHuY=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 08:15:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c5jsywe0fz"}, {"Name": "integrity", "Value": "sha256-LF247tm+Ygw3W+aMphNfMTDK72I9bXMullzV3MzwHuY="}, {"Name": "label", "Value": "css/site.css.gz"}]}, {"Route": "css/site.css", "AssetFile": "css/site.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000239177230"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4180"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LF247tm+Ygw3W+aMphNfMTDK72I9bXMullzV3MzwHuY=\""}, {"Name": "ETag", "Value": "W/\"EO6RHZxTE5HHwzt4Grc+kq2k/kiX34rn5FR3qW1y7LY=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 08:15:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EO6RHZxTE5HHwzt4Grc+kq2k/kiX34rn5FR3qW1y7LY="}]}, {"Route": "css/site.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "19079"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"EO6RHZxTE5HHwzt4Grc+kq2k/kiX34rn5FR3qW1y7LY=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:29:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EO6RHZxTE5HHwzt4Grc+kq2k/kiX34rn5FR3qW1y7LY="}]}, {"Route": "css/site.css.gz", "AssetFile": "css/site.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4180"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LF247tm+Ygw3W+aMphNfMTDK72I9bXMullzV3MzwHuY=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 08:15:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LF247tm+Ygw3W+aMphNfMTDK72I9bXMullzV3MzwHuY="}]}, {"Route": "img/placeholder.ji9w67flmt.txt", "AssetFile": "img/placeholder.txt.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003144654088"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "317"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"NhzAA6NBedqJytCK3ToxeUU3Dc1+TmFGhFNN8Ifz84A=\""}, {"Name": "ETag", "Value": "W/\"K2pimtkMM2h05wlF2lE7oVguAzABkjNoPl2HApLArSc=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 08:15:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ji9w67flmt"}, {"Name": "integrity", "Value": "sha256-K2pimtkMM2h05wlF2lE7oVguAzABkjNoPl2HApLArSc="}, {"Name": "label", "Value": "img/placeholder.txt"}]}, {"Route": "img/placeholder.ji9w67flmt.txt", "AssetFile": "img/placeholder.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "418"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"K2pimtkMM2h05wlF2lE7oVguAzABkjNoPl2HApLArSc=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 02:54:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ji9w67flmt"}, {"Name": "integrity", "Value": "sha256-K2pimtkMM2h05wlF2lE7oVguAzABkjNoPl2HApLArSc="}, {"Name": "label", "Value": "img/placeholder.txt"}]}, {"Route": "img/placeholder.ji9w67flmt.txt.gz", "AssetFile": "img/placeholder.txt.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "317"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"NhzAA6NBedqJytCK3ToxeUU3Dc1+TmFGhFNN8Ifz84A=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 08:15:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ji9w67flmt"}, {"Name": "integrity", "Value": "sha256-NhzAA6NBedqJytCK3ToxeUU3Dc1+TmFGhFNN8Ifz84A="}, {"Name": "label", "Value": "img/placeholder.txt.gz"}]}, {"Route": "img/placeholder.txt", "AssetFile": "img/placeholder.txt.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003144654088"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "317"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"NhzAA6NBedqJytCK3ToxeUU3Dc1+TmFGhFNN8Ifz84A=\""}, {"Name": "ETag", "Value": "W/\"K2pimtkMM2h05wlF2lE7oVguAzABkjNoPl2HApLArSc=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 08:15:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K2pimtkMM2h05wlF2lE7oVguAzABkjNoPl2HApLArSc="}]}, {"Route": "img/placeholder.txt", "AssetFile": "img/placeholder.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "418"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"K2pimtkMM2h05wlF2lE7oVguAzABkjNoPl2HApLArSc=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 02:54:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K2pimtkMM2h05wlF2lE7oVguAzABkjNoPl2HApLArSc="}]}, {"Route": "img/placeholder.txt.gz", "AssetFile": "img/placeholder.txt.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "317"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"NhzAA6NBedqJytCK3ToxeUU3Dc1+TmFGhFNN8Ifz84A=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 08:15:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NhzAA6NBedqJytCK3ToxeUU3Dc1+TmFGhFNN8Ifz84A="}]}, {"Route": "js/site.js", "AssetFile": "js/site.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000431592577"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2316"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"P+850Ht/tnJrgjQ/VToKj4GQMcP41kBRp8POlZiQsxM=\""}, {"Name": "ETag", "Value": "W/\"WMbMqjC378r3pmuObT7rbUtbm6uRDaAzFKdVIrFi/PM=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 08:15:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WMbMqjC378r3pmuObT7rbUtbm6uRDaAzFKdVIrFi/PM="}]}, {"Route": "js/site.js", "AssetFile": "js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6968"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WMbMqjC378r3pmuObT7rbUtbm6uRDaAzFKdVIrFi/PM=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 02:54:12 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WMbMqjC378r3pmuObT7rbUtbm6uRDaAzFKdVIrFi/PM="}]}, {"Route": "js/site.js.gz", "AssetFile": "js/site.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2316"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"P+850Ht/tnJrgjQ/VToKj4GQMcP41kBRp8POlZiQsxM=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 08:15:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-P+850Ht/tnJrgjQ/VToKj4GQMcP41kBRp8POlZiQsxM="}]}, {"Route": "js/site.o400d3i4ax.js", "AssetFile": "js/site.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000431592577"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2316"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"P+850Ht/tnJrgjQ/VToKj4GQMcP41kBRp8POlZiQsxM=\""}, {"Name": "ETag", "Value": "W/\"WMbMqjC378r3pmuObT7rbUtbm6uRDaAzFKdVIrFi/PM=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 08:15:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o400d3i4ax"}, {"Name": "integrity", "Value": "sha256-WMbMqjC378r3pmuObT7rbUtbm6uRDaAzFKdVIrFi/PM="}, {"Name": "label", "Value": "js/site.js"}]}, {"Route": "js/site.o400d3i4ax.js", "AssetFile": "js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6968"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WMbMqjC378r3pmuObT7rbUtbm6uRDaAzFKdVIrFi/PM=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 02:54:12 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o400d3i4ax"}, {"Name": "integrity", "Value": "sha256-WMbMqjC378r3pmuObT7rbUtbm6uRDaAzFKdVIrFi/PM="}, {"Name": "label", "Value": "js/site.js"}]}, {"Route": "js/site.o400d3i4ax.js.gz", "AssetFile": "js/site.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2316"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"P+850Ht/tnJrgjQ/VToKj4GQMcP41kBRp8POlZiQsxM=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 08:15:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o400d3i4ax"}, {"Name": "integrity", "Value": "sha256-P+850Ht/tnJrgjQ/VToKj4GQMcP41kBRp8POlZiQsxM="}, {"Name": "label", "Value": "js/site.js.gz"}]}]}