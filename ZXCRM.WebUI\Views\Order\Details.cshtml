@model ZXCRM.WebUI.Models.ViewModels.OrderDetailViewModel
@using ZXCRM.WebUI.Helpers
@{
    ViewData["Title"] = "订单详情";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">订单详情</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a asp-controller="Dashboard" asp-action="Index">首页</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Order" asp-action="Index">订单管理</a></li>
                    <li class="breadcrumb-item active">订单详情</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <!-- 基本信息 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-info-circle"></i> 基本信息
                        </h3>
                        <div class="card-tools">
                            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning btn-sm">
                                <i class="fas fa-edit"></i> 编辑
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">订单名称:</dt>
                                    <dd class="col-sm-8">@Model.Name</dd>

                                    <dt class="col-sm-4">订单编号:</dt>
                                    <dd class="col-sm-8"><code>@Model.Code</code></dd>

                                    <dt class="col-sm-4">客户名称:</dt>
                                    <dd class="col-sm-8">@Model.CustomerName</dd>

                                    <dt class="col-sm-4">联系人:</dt>
                                    <dd class="col-sm-8">
                                        @if (!string.IsNullOrEmpty(Model.ContactName))
                                        {
                                            @Model.ContactName
                                        }
                                        else
                                        {
                                            <span class="text-muted">未填写</span>
                                        }
                                    </dd>

                                    <dt class="col-sm-4">联系电话:</dt>
                                    <dd class="col-sm-8">
                                        @if (!string.IsNullOrEmpty(Model.ContactPhone))
                                        {
                                            <a href="tel:@Model.ContactPhone">@Model.ContactPhone</a>
                                        }
                                        else
                                        {
                                            <span class="text-muted">未填写</span>
                                        }
                                    </dd>
                                </dl>
                            </div>
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">订单金额:</dt>
                                    <dd class="col-sm-8">
                                        <span class="badge badge-success badge-lg">
                                            @EnumHelper.FormatAmount(Model.Amount, Model.Currency)
                                        </span>
                                    </dd>

                                    <dt class="col-sm-4">结算金额:</dt>
                                    <dd class="col-sm-8">
                                        <span class="badge badge-info badge-lg">
                                            @EnumHelper.FormatAmount(Model.SettlementAmount, Model.Currency)
                                        </span>
                                    </dd>

                                    <dt class="col-sm-4">签约日期:</dt>
                                    <dd class="col-sm-8">@Model.SignDate.ToString("yyyy年MM月dd日")</dd>

                                    <dt class="col-sm-4">订单状态:</dt>
                                    <dd class="col-sm-8">
                                        <span class="badge <EMAIL>(Model.Status) badge-lg">
                                            @if (Model.Status == "Draft")
                                            {
                                                <i class="fas fa-edit"></i>
                                            }
                                            else if (Model.Status == "Confirmed")
                                            {
                                                <i class="fas fa-check"></i>
                                            }
                                            else if (Model.Status == "InProgress")
                                            {
                                                <i class="fas fa-play"></i>
                                            }
                                            else if (Model.Status == "Completed")
                                            {
                                                <i class="fas fa-check-circle"></i>
                                            }
                                            else if (Model.Status == "Cancelled")
                                            {
                                                <i class="fas fa-times"></i>
                                            }
                                            else
                                            {
                                                <i class="fas fa-question"></i>
                                            }
                                            @EnumHelper.GetOrderStatusText(Model.Status)
                                        </span>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 人员信息 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-users"></i> 人员信息
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">客户经理:</dt>
                                    <dd class="col-sm-8">
                                        <span class="badge badge-primary">@Model.AccountManagerName</span>
                                    </dd>

                                    <dt class="col-sm-4">项目经理:</dt>
                                    <dd class="col-sm-8">
                                        @if (!string.IsNullOrEmpty(Model.ProjectManagerName))
                                        {
                                            <span class="badge badge-info">@Model.ProjectManagerName</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">未指定</span>
                                        }
                                    </dd>
                                </dl>
                            </div>
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">创建部门:</dt>
                                    <dd class="col-sm-8">
                                        <span class="badge badge-secondary">@Model.DepartmentName</span>
                                    </dd>

                                    <dt class="col-sm-4">业绩归属:</dt>
                                    <dd class="col-sm-8">
                                        @if (!string.IsNullOrEmpty(Model.PerformanceDepartmentName))
                                        {
                                            <span class="badge badge-warning">@Model.PerformanceDepartmentName</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">未指定</span>
                                        }
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 关联信息 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-link"></i> 关联信息
                        </h3>
                    </div>
                    <div class="card-body">
                        <dl class="row">
                            <dt class="col-sm-2">关联商机:</dt>
                            <dd class="col-sm-10">
                                @if (!string.IsNullOrEmpty(Model.OpportunityName))
                                {
                                    <span class="badge badge-success">@Model.OpportunityName</span>
                                }
                                else
                                {
                                    <span class="text-muted">无关联商机</span>
                                }
                            </dd>
                        </dl>
                    </div>
                </div>

                <!-- 系统信息 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-cog"></i> 系统信息
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">创建人:</dt>
                                    <dd class="col-sm-8">@Model.CreatedByName</dd>

                                    <dt class="col-sm-4">创建时间:</dt>
                                    <dd class="col-sm-8">@Model.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss")</dd>
                                </dl>
                            </div>
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">最后更新:</dt>
                                    <dd class="col-sm-8">@Model.UpdatedAt.ToString("yyyy-MM-dd HH:mm:ss")</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <!-- 操作面板 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-tools"></i> 操作
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="btn-group-vertical btn-block">
                            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning">
                                <i class="fas fa-edit"></i> 编辑订单
                            </a>
                            <button type="button" class="btn btn-danger" onclick="confirmDelete(@Model.Id, '@Model.Name')">
                                <i class="fas fa-trash"></i> 删除订单
                            </button>
                            <div class="dropdown-divider"></div>
                            <a href="#" class="btn btn-info">
                                <i class="fas fa-money-bill"></i> 查看款项
                            </a>
                            <a href="#" class="btn btn-success">
                                <i class="fas fa-file-invoice"></i> 查看发票
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-bar"></i> 统计信息
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="info-box">
                            <span class="info-box-icon bg-info"><i class="fas fa-money-bill"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">已收款项</span>
                                <span class="info-box-number">0.00 @Model.Currency</span>
                            </div>
                        </div>

                        <div class="info-box">
                            <span class="info-box-icon bg-warning"><i class="fas fa-file-invoice"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">已开发票</span>
                                <span class="info-box-number">0.00 @Model.Currency</span>
                            </div>
                        </div>

                        <div class="progress-group">
                            <span class="float-right"><b>0</b>/100%</span>
                            <span>收款进度</span>
                            <div class="progress progress-sm">
                                <div class="progress-bar bg-primary" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快捷导航 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-compass"></i> 快捷导航
                        </h3>
                    </div>
                    <div class="card-body">
                        <a asp-action="Index" class="btn btn-outline-primary btn-block">
                            <i class="fas fa-list"></i> 返回订单列表
                        </a>
                        <a asp-action="Create" class="btn btn-outline-success btn-block">
                            <i class="fas fa-plus"></i> 新增订单
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>您确定要删除订单 <strong id="deleteOrderName"></strong> 吗？</p>
                <p class="text-danger"><small>删除订单前请确保该订单下没有款项记录！</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(orderId, orderName) {
            $('#deleteOrderName').text(orderName);
            $('#deleteForm').attr('action', '@Url.Action("Delete")/' + orderId);
            $('#deleteModal').modal('show');
        }
    </script>
}

@section Styles {
    <style>
        .badge-lg {
            font-size: 0.9em;
            padding: 0.5em 0.75em;
        }

        .info-box {
            margin-bottom: 1rem;
        }

        .progress-group {
            margin-bottom: 1rem;
        }
    </style>
}
