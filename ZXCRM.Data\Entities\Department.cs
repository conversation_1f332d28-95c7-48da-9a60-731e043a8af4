namespace ZXCRM.Data.Entities
{
    public class Department : BaseEntity
    {
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public int? ParentId { get; set; }
        public string Status { get; set; } = "Active";

        // 导航属性
        public Department? Parent { get; set; }
        public ICollection<Department> Children { get; set; } = new List<Department>();
        public ICollection<User> Users { get; set; } = new List<User>();
        public ICollection<Order> Orders { get; set; } = new List<Order>();
        public ICollection<Order> PerformanceOrders { get; set; } = new List<Order>();
    }
}
