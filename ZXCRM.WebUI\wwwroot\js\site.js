// ZXCRM Site JavaScript

$(document).ready(function() {
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
    
    // Initialize popovers
    $('[data-toggle="popover"]').popover();
    
    // Auto-hide alerts after 5 seconds
    $('.alert').delay(5000).fadeOut('slow');
    
    // Confirm delete actions
    $('.btn-delete').on('click', function(e) {
        e.preventDefault();
        var form = $(this).closest('form');
        var message = $(this).data('confirm') || '确定要删除这条记录吗？';
        
        if (confirm(message)) {
            form.submit();
        }
    });
    
    // Loading button state
    $('.btn-loading').on('click', function() {
        var btn = $(this);
        var originalText = btn.text();
        
        btn.prop('disabled', true);
        btn.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 处理中...');
        
        // Re-enable button after 10 seconds as fallback
        setTimeout(function() {
            btn.prop('disabled', false);
            btn.html(originalText);
        }, 10000);
    });
    
    // Form validation enhancement
    $('form').on('submit', function() {
        var form = $(this);
        var submitBtn = form.find('button[type="submit"]');
        
        if (submitBtn.length > 0) {
            submitBtn.prop('disabled', true);
            submitBtn.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 提交中...');
        }
    });
    
    // DataTable initialization (if present)
    if ($.fn.DataTable) {
        $('.data-table').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.13.4/i18n/zh.json"
            },
            "responsive": true,
            "autoWidth": false,
            "pageLength": 25,
            "order": [[0, "desc"]]
        });
    }
    
    // Chart.js default configuration
    if (typeof Chart !== 'undefined') {
        Chart.defaults.font.family = "'Source Sans Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif";
        Chart.defaults.color = '#858796';
    }
    
    // Sidebar menu state persistence
    var currentPath = window.location.pathname;
    $('.nav-sidebar .nav-link').each(function() {
        var link = $(this);
        var href = link.attr('href');
        
        if (href && currentPath.indexOf(href) === 0) {
            link.addClass('active');
            link.closest('.nav-item').addClass('menu-open');
        }
    });
    
    // Search functionality
    $('#globalSearch').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('.searchable-item').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });
    
    // Number formatting
    $('.format-currency').each(function() {
        var value = parseFloat($(this).text());
        if (!isNaN(value)) {
            $(this).text('¥' + value.toLocaleString('zh-CN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }));
        }
    });
    
    // Date formatting
    $('.format-date').each(function() {
        var dateStr = $(this).text();
        var date = new Date(dateStr);
        if (!isNaN(date.getTime())) {
            $(this).text(date.toLocaleDateString('zh-CN'));
        }
    });
    
    // DateTime formatting
    $('.format-datetime').each(function() {
        var dateStr = $(this).text();
        var date = new Date(dateStr);
        if (!isNaN(date.getTime())) {
            $(this).text(date.toLocaleString('zh-CN'));
        }
    });
});

// Utility functions
var ZXCRM = {
    // Show success message
    showSuccess: function(message) {
        this.showAlert(message, 'success');
    },
    
    // Show error message
    showError: function(message) {
        this.showAlert(message, 'danger');
    },
    
    // Show warning message
    showWarning: function(message) {
        this.showAlert(message, 'warning');
    },
    
    // Show info message
    showInfo: function(message) {
        this.showAlert(message, 'info');
    },
    
    // Show alert
    showAlert: function(message, type) {
        var alertHtml = '<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' +
            '<i class="fas fa-' + this.getAlertIcon(type) + '"></i> ' + message +
            '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
            '</div>';
        
        $('.content').prepend(alertHtml);
        
        // Auto-hide after 5 seconds
        setTimeout(function() {
            $('.alert').first().fadeOut('slow');
        }, 5000);
    },
    
    // Get alert icon
    getAlertIcon: function(type) {
        switch(type) {
            case 'success': return 'check-circle';
            case 'danger': return 'exclamation-circle';
            case 'warning': return 'exclamation-triangle';
            case 'info': return 'info-circle';
            default: return 'info-circle';
        }
    },
    
    // Format currency
    formatCurrency: function(value) {
        return '¥' + parseFloat(value).toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    },
    
    // Format date
    formatDate: function(dateStr) {
        var date = new Date(dateStr);
        return date.toLocaleDateString('zh-CN');
    },
    
    // Format datetime
    formatDateTime: function(dateStr) {
        var date = new Date(dateStr);
        return date.toLocaleString('zh-CN');
    },
    
    // Confirm action
    confirm: function(message, callback) {
        if (confirm(message || '确定要执行此操作吗？')) {
            if (typeof callback === 'function') {
                callback();
            }
            return true;
        }
        return false;
    },
    
    // AJAX helper
    ajax: function(options) {
        var defaults = {
            type: 'GET',
            dataType: 'json',
            beforeSend: function() {
                // Show loading indicator
            },
            complete: function() {
                // Hide loading indicator
            },
            error: function(xhr, status, error) {
                ZXCRM.showError('请求失败: ' + error);
            }
        };
        
        return $.ajax($.extend(defaults, options));
    }
};

// Global error handler
window.onerror = function(msg, url, lineNo, columnNo, error) {
    console.error('JavaScript Error: ', {
        message: msg,
        source: url,
        line: lineNo,
        column: columnNo,
        error: error
    });
    
    // Don't show error to user in production
    if (window.location.hostname === 'localhost') {
        ZXCRM.showError('JavaScript错误: ' + msg);
    }
    
    return false;
};
