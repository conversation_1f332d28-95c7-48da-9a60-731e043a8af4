@model ZXCRM.Service.DTOs.PermissionDto
@{
    ViewData["Title"] = "权限详情";
}

<!-- 详情页面容器 -->
<div class="detail-page-container">
    <!-- 页面标题栏 -->
    <div class="detail-header">
        <div class="detail-title">
            <h2>
                <i class="fas fa-key"></i>
                权限详情
            </h2>
        </div>
        <div class="detail-actions">
            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning">
                <i class="fas fa-edit"></i> 编辑
            </a>
            <a asp-action="Index" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> 返回列表
            </a>
        </div>
    </div>

    <!-- 详情内容 -->
    <div class="detail-content">
        <div class="row">
            <!-- 基本信息 -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle"></i>
                            基本信息
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12">
                                <dl class="row detail-list">
                                    <dt class="col-sm-4">权限ID:</dt>
                                    <dd class="col-sm-8">
                                        <span class="badge bg-light text-dark">#@Model.Id</span>
                                    </dd>

                                    <dt class="col-sm-4">权限名称:</dt>
                                    <dd class="col-sm-8">
                                        <strong>@Model.Name</strong>
                                    </dd>

                                    <dt class="col-sm-4">权限代码:</dt>
                                    <dd class="col-sm-8">
                                        <span class="badge bg-primary text-white">@Model.Code</span>
                                    </dd>

                                    <dt class="col-sm-4">权限描述:</dt>
                                    <dd class="col-sm-8">
                                        @if (!string.IsNullOrEmpty(Model.Description))
                                        {
                                            <div class="description-text">@Model.Description</div>
                                        }
                                        else
                                        {
                                            <span class="text-muted">未设置描述</span>
                                        }
                                    </dd>

                                    @if (!string.IsNullOrEmpty(Model.ModuleType))
                                    {
                                        <dt class="col-sm-4">模块类型:</dt>
                                        <dd class="col-sm-8">
                                            <span class="badge bg-info text-white">@Model.ModuleType</span>
                                        </dd>
                                    }
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 时间信息 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-clock"></i>
                            时间信息
                        </h5>
                    </div>
                    <div class="card-body">
                        <dl class="detail-list">
                            <dt>创建时间:</dt>
                            <dd>
                                <div class="text-nowrap">@Model.CreatedAt.ToString("yyyy年MM月dd日")</div>
                                <small class="text-muted">@Model.CreatedAt.ToString("HH:mm:ss")</small>
                            </dd>

                            <dt>更新时间:</dt>
                            <dd>
                                <div class="text-nowrap">@Model.UpdatedAt.ToString("yyyy年MM月dd日")</div>
                                <small class="text-muted">@Model.UpdatedAt.ToString("HH:mm:ss")</small>
                            </dd>
                        </dl>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-tools"></i>
                            快速操作
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-outline-warning btn-sm">
                                <i class="fas fa-edit"></i> 编辑权限
                            </a>
                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="confirmDelete(@Model.Id, '@Model.Name')">
                                <i class="fas fa-trash"></i> 删除权限
                            </button>
                            <hr class="my-2">
                            <a asp-controller="User" asp-action="Index" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-users"></i> 查看用户列表
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>您确定要删除权限 <strong id="deletePermissionName"></strong> 吗？</p>
                <p class="text-danger"><small>删除权限将会影响所有拥有此权限的用户！</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(permissionId, permissionName) {
            $('#deletePermissionName').text(permissionName);
            $('#deleteForm').attr('action', '@Url.Action("Delete")/' + permissionId);
            $('#deleteModal').modal('show');
        }

        // 显示提示消息
        @if (TempData["SuccessMessage"] != null)
        {
            <text>
            toastr.success('@TempData["SuccessMessage"]');
            </text>
        }

        @if (TempData["ErrorMessage"] != null)
        {
            <text>
            toastr.error('@TempData["ErrorMessage"]');
            </text>
        }
    </script>
}

<style>
    .detail-page-container {
        padding: 20px;
    }

    .detail-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #dee2e6;
    }

    .detail-title h2 {
        margin: 0;
        color: #495057;
        font-size: 1.5rem;
    }

    .detail-title i {
        margin-right: 10px;
        color: #6c757d;
    }

    .detail-actions .btn {
        margin-left: 10px;
    }

    .detail-list dt {
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
    }

    .detail-list dd {
        margin-bottom: 15px;
    }

    .description-text {
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 4px;
        border-left: 3px solid #007bff;
    }

    .card-title i {
        margin-right: 8px;
        color: #6c757d;
    }

    .badge {
        font-size: 0.875em;
    }
</style>
