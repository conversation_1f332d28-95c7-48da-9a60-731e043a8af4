@model ZXCRM.WebUI.Models.ViewModels.EditDepartmentViewModel
@{
    ViewData["Title"] = "编辑部门";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">编辑部门</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a asp-controller="Dashboard" asp-action="Index">首页</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Department" asp-action="Index">部门管理</a></li>
                    <li class="breadcrumb-item active">编辑部门</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-edit"></i> 编辑部门信息
                        </h3>
                        <div class="card-tools">
                            <span class="badge bg-info text-white">部门ID: @Model.Id</span>
                        </div>
                    </div>
                    <form asp-action="Edit" method="post">
                        <input asp-for="Id" type="hidden" />

                        <div class="card-body">
                            <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>

                            <!-- 部门名称 -->
                            <div class="form-group">
                                <label asp-for="Name" class="form-label required"></label>
                                <input asp-for="Name" class="form-control" placeholder="请输入部门名称" />
                                <span asp-validation-for="Name" class="text-danger"></span>
                                <small class="form-text text-muted">部门名称应该简洁明了，便于识别</small>
                            </div>

                            <!-- 状态 -->
                            <div class="form-group">
                                <div class="form-check">
                                    <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                                    <label asp-for="IsActive" class="form-check-label"></label>
                                </div>
                                <span asp-validation-for="IsActive" class="text-danger"></span>
                                <small class="form-text text-muted">禁用部门将影响该部门下用户的权限</small>
                            </div>
                        </div>

                        <div class="card-footer">
                            <div class="row">
                                <div class="col-md-6">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> 保存修改
                                    </button>
                                    <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info ml-2">
                                        <i class="fas fa-eye"></i> 查看详情
                                    </a>
                                    <a asp-action="Index" class="btn btn-secondary ml-2">
                                        <i class="fas fa-times"></i> 取消
                                    </a>
                                </div>
                                <div class="col-md-6 text-right">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        标有 <span class="text-danger">*</span> 的字段为必填项
                                    </small>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="col-md-4">
                <!-- 操作历史 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-history"></i> 操作历史
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <div class="time-label">
                                <span class="bg-info">最近操作</span>
                            </div>
                            <div>
                                <i class="fas fa-edit bg-warning"></i>
                                <div class="timeline-item">
                                    <span class="time">
                                        <i class="fas fa-clock"></i> 正在编辑
                                    </span>
                                    <h3 class="timeline-header">编辑部门信息</h3>
                                    <div class="timeline-body">
                                        当前正在编辑部门 <strong>@Model.Name</strong> 的信息
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 帮助信息 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-question-circle"></i> 编辑说明
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb"></i> 提示</h6>
                            <ul class="mb-0">
                                <li>修改部门名称时请确保不与其他部门重复</li>
                                <li>禁用部门会影响该部门下所有用户</li>
                                <li>建议在非工作时间进行重要修改</li>
                            </ul>
                        </div>

                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> 注意</h6>
                            <p class="mb-0">
                                禁用部门后，该部门下的用户可能无法正常访问系统功能。
                                请谨慎操作！
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-bolt"></i> 快速操作
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info btn-sm btn-block">
                                <i class="fas fa-eye"></i> 查看详情
                            </a>

                            <a asp-controller="User" asp-action="Index" class="btn btn-success btn-sm btn-block">
                                <i class="fas fa-users"></i> 管理人员
                            </a>

                            <button type="button" class="btn btn-danger btn-sm btn-block" onclick="confirmDelete(@Model.Id, '@Model.Name')">
                                <i class="fas fa-trash"></i> 删除部门
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>您确定要删除部门 <strong id="deleteDepartmentName"></strong> 吗？</p>
                <p class="text-danger"><small>删除部门前请确保该部门下没有用户！</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(departmentId, departmentName) {
            $('#deleteDepartmentName').text(departmentName);
            $('#deleteForm').attr('action', '@Url.Action("Delete", "Department")/' + departmentId);
            $('#deleteModal').modal('show');
        }

        // 表单验证增强
        $(document).ready(function() {
            // 部门名称实时验证
            $('#Name').on('blur', function() {
                const name = $(this).val();
                if (name.length > 0 && name.length < 2) {
                    $(this).addClass('is-invalid');
                    $(this).next('.text-danger').text('部门名称长度至少2个字符');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });


        });
    </script>
}

@section Styles {
    <style>
        .required::after {
            content: " *";
            color: red;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .is-invalid {
            border-color: #dc3545;
        }

        .timeline {
            position: relative;
            margin: 0 0 30px 0;
            padding: 0;
            list-style: none;
        }

        .timeline:before {
            content: '';
            position: absolute;
            top: 0;
            left: 25px;
            height: 100%;
            width: 2px;
            background: #dee2e6;
        }

        .timeline > div {
            position: relative;
            margin-right: 10px;
            margin-bottom: 15px;
        }

        .timeline > div > .timeline-item {
            box-shadow: 0 1px 1px rgba(0,0,0,0.1);
            border-radius: 3px;
            margin-top: 0;
            background: #fff;
            color: #444;
            margin-left: 60px;
            margin-right: 15px;
            padding: 10px;
            position: relative;
        }

        .timeline > div > .fas {
            width: 50px;
            height: 50px;
            font-size: 15px;
            line-height: 50px;
            position: absolute;
            color: #666;
            background: #f4f4f4;
            border-radius: 50%;
            text-align: center;
            left: 0;
            top: 0;
        }
    </style>
}
