@model ZXCRM.WebUI.Models.ViewModels.OpportunityReportViewModel
@{
    ViewData["Title"] = "商机统计报表";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">商机统计报表</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a asp-controller="Dashboard" asp-action="Index">首页</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Report" asp-action="Index">报表统计</a></li>
                    <li class="breadcrumb-item active">商机统计</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <!-- 查询条件 -->
        <div class="card collapsed-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-filter"></i> 查询条件
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body" style="display: none;">
                <form method="get" asp-action="Opportunity">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label asp-for="Query.StartDate">开始日期</label>
                                <input asp-for="Query.StartDate" type="date" class="form-control" />
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label asp-for="Query.EndDate">结束日期</label>
                                <input asp-for="Query.EndDate" type="date" class="form-control" />
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label asp-for="Query.UserId">用户</label>
                                <select asp-for="Query.UserId" class="form-control">
                                    <option value="">全部用户</option>
                                    @foreach (var user in Model.Users)
                                    {
                                        <option value="@user.Id">@user.Name</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label asp-for="Query.Status">状态</label>
                                <select asp-for="Query.Status" class="form-control">
                                    <option value="">全部状态</option>
                                    @foreach (var status in ZXCRM.WebUI.Models.ViewModels.OpportunityStatusOptions.GetAllStatuses())
                                    {
                                        <option value="@status">@status</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary btn-block">
                                        <i class="fas fa-search"></i> 查询
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        @if (Model.Report != null)
        {
            <!-- 总体统计 -->
            <div class="row">
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3>@Model.Report.TotalOpportunities</h3>
                            <p>总商机数</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-seedling"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3>@Model.Report.StatusStats.Where(s => s.Status == "成交").FirstOrDefault()?.Count ?? 0</h3>
                            <p>成交商机</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-trophy"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3>@Model.Report.OverallWinRate.ToString("F1")%</h3>
                            <p>总成交率</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-danger">
                        <div class="inner">
                            <h3>@Model.Report.StatusStats.Where(s => s.Status == "失败").FirstOrDefault()?.Count ?? 0</h3>
                            <p>失败商机</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-times-circle"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 状态分布 -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-chart-pie"></i> 商机状态分布
                            </h3>
                        </div>
                        <div class="card-body">
                            @if (Model.Report.StatusStats.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>状态</th>
                                                <th>数量</th>
                                                <th>占比</th>
                                                <th>进度</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var stat in Model.Report.StatusStats)
                                            {
                                                <tr>
                                                    <td>
                                                        <span class="badge badge-@(ZXCRM.WebUI.Models.ViewModels.OpportunityStatus.GetStatusColor(stat.Status))">
                                                            @stat.Status
                                                        </span>
                                                    </td>
                                                    <td><strong>@stat.Count</strong></td>
                                                    <td>@stat.Percentage.ToString("F1")%</td>
                                                    <td>
                                                        <div class="progress progress-xs">
                                                            <div class="progress-bar bg-@(ZXCRM.WebUI.Models.ViewModels.OpportunityStatus.GetStatusColor(stat.Status))" 
                                                                 style="width: @stat.Percentage%"></div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <p class="text-muted">暂无状态统计数据</p>
                            }
                        </div>
                    </div>
                </div>

                <!-- 用户业绩 -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-users"></i> 用户业绩排行
                            </h3>
                        </div>
                        <div class="card-body">
                            @if (Model.Report.UserStats.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>用户</th>
                                                <th>总数</th>
                                                <th>成交</th>
                                                <th>成交率</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var stat in Model.Report.UserStats.Take(10))
                                            {
                                                <tr>
                                                    <td><strong>@stat.UserName</strong></td>
                                                    <td>@stat.TotalCount</td>
                                                    <td>@stat.WonCount</td>
                                                    <td>
                                                        <span class="badge badge-@(stat.WinRate >= 50 ? "success" : stat.WinRate >= 30 ? "warning" : "danger")">
                                                            @stat.WinRate.ToString("F1")%
                                                        </span>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <p class="text-muted">暂无用户统计数据</p>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- 月度趋势 -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-chart-line"></i> 月度趋势分析
                            </h3>
                        </div>
                        <div class="card-body">
                            @if (Model.Report.MonthlyStats.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>月份</th>
                                                <th>总商机数</th>
                                                <th>成交数</th>
                                                <th>成交率</th>
                                                <th>趋势</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var stat in Model.Report.MonthlyStats.OrderBy(s => s.Month))
                                            {
                                                <tr>
                                                    <td><strong>@stat.Month</strong></td>
                                                    <td>@stat.TotalCount</td>
                                                    <td>@stat.WonCount</td>
                                                    <td>
                                                        <span class="badge badge-@(stat.WinRate >= 50 ? "success" : stat.WinRate >= 30 ? "warning" : "danger")">
                                                            @stat.WinRate.ToString("F1")%
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="progress progress-xs">
                                                            <div class="progress-bar bg-primary" style="width: @(stat.TotalCount * 10)%"></div>
                                                        </div>
                                                        <span class="badge badge-secondary">@stat.TotalCount 个</span>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <p class="text-muted">暂无月度统计数据</p>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
        else
        {
            <div class="alert alert-warning">
                <h4><i class="icon fas fa-exclamation-triangle"></i> 提示</h4>
                暂无商机统计数据，请检查查询条件或联系管理员。
            </div>
        }

        <!-- 快捷操作 -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-bolt"></i> 快捷操作
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="btn-group" role="group">
                            <a asp-controller="Opportunity" asp-action="Index" class="btn btn-success">
                                <i class="fas fa-seedling"></i> 商机管理
                            </a>
                            <a asp-controller="Opportunity" asp-action="Create" class="btn btn-primary">
                                <i class="fas fa-plus"></i> 新增商机
                            </a>
                            <a asp-action="Dashboard" class="btn btn-info">
                                <i class="fas fa-tachometer-alt"></i> 综合仪表盘
                            </a>
                            <a asp-controller="Report" asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> 返回报表首页
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        $(document).ready(function() {
            // 显示提示消息
            @if (TempData["SuccessMessage"] != null)
            {
                <text>
                toastr.success('@TempData["SuccessMessage"]');
                </text>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <text>
                toastr.error('@TempData["ErrorMessage"]');
                </text>
            }
        });
    </script>
}
