@model ZXCRM.WebUI.Models.ViewModels.EditInvoiceViewModel
@{
    ViewData["Title"] = "编辑发票";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">编辑发票</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a asp-controller="Dashboard" asp-action="Index">首页</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Invoice" asp-action="Index">发票管理</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Invoice" asp-action="Details" asp-route-id="@Model.Id">发票详情</a></li>
                    <li class="breadcrumb-item active">编辑发票</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-edit"></i> 编辑发票信息
                        </h3>
                        <div class="card-tools">
                            <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info btn-sm">
                                <i class="fas fa-eye"></i> 查看详情
                            </a>
                        </div>
                    </div>
                    <form asp-action="Edit" method="post">
                        <input asp-for="Id" type="hidden" />
                        <div class="card-body">
                            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <!-- 关联款项 -->
                                    <div class="form-group">
                                        <label asp-for="PaymentId" class="form-label required"></label>
                                        <select asp-for="PaymentId" class="form-control" disabled>
                                            <option value="">请选择关联款项</option>
                                            @foreach (var payment in Model.Payments)
                                            {
                                                <option value="@payment.Id">
                                                    @payment.Name (@payment.Code) - @payment.OrderName - @payment.CustomerName - @payment.Amount.ToString("N2") @payment.Currency
                                                </option>
                                            }
                                        </select>
                                        <input asp-for="PaymentId" type="hidden" />
                                        <span asp-validation-for="PaymentId" class="text-danger"></span>
                                        <small class="form-text text-muted">关联款项不可修改</small>
                                    </div>

                                    <!-- 发票公司 -->
                                    <div class="form-group">
                                        <label asp-for="Company" class="form-label required"></label>
                                        <select asp-for="Company" class="form-control">
                                            <option value="">请选择发票公司</option>
                                            <option value="A公司">A公司</option>
                                            <option value="B公司">B公司</option>
                                            <option value="C公司">C公司</option>
                                        </select>
                                        <span asp-validation-for="Company" class="text-danger"></span>
                                    </div>

                                    <!-- 发票类型 -->
                                    <div class="form-group">
                                        <label asp-for="Type" class="form-label required"></label>
                                        <select asp-for="Type" class="form-control">
                                            <option value="">请选择发票类型</option>
                                            <option value="增值税专用发票">增值税专用发票</option>
                                            <option value="增值税普通发票">增值税普通发票</option>
                                            <option value="收据">收据</option>
                                        </select>
                                        <span asp-validation-for="Type" class="text-danger"></span>
                                    </div>

                                    <!-- 发票税率 -->
                                    <div class="form-group">
                                        <label asp-for="TaxRate" class="form-label required"></label>
                                        <select asp-for="TaxRate" class="form-control">
                                            <option value="">请选择发票税率</option>
                                            <option value="16">16%</option>
                                            <option value="13">13%</option>
                                            <option value="6">6%</option>
                                            <option value="0">免税</option>
                                        </select>
                                        <span asp-validation-for="TaxRate" class="text-danger"></span>
                                    </div>

                                    <!-- 发票金额 -->
                                    <div class="form-group">
                                        <label asp-for="Amount" class="form-label required"></label>
                                        <input asp-for="Amount" class="form-control" placeholder="0.00" step="0.01" />
                                        <span asp-validation-for="Amount" class="text-danger"></span>
                                        <small class="form-text text-muted">发票金额不能超过款项金额</small>
                                    </div>

                                    <!-- 发票状态 -->
                                    <div class="form-group">
                                        <label asp-for="Status" class="form-label"></label>
                                        <select asp-for="Status" class="form-control">
                                            <option value="Normal">正常</option>
                                            <option value="Cancelled">已作废</option>
                                        </select>
                                        <span asp-validation-for="Status" class="text-danger"></span>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <!-- 发票内容 -->
                                    <div class="form-group">
                                        <label asp-for="Content" class="form-label"></label>
                                        <textarea asp-for="Content" class="form-control" rows="3" placeholder="请输入发票内容"></textarea>
                                        <span asp-validation-for="Content" class="text-danger"></span>
                                    </div>

                                    <!-- 发票代号 -->
                                    <div class="form-group">
                                        <label asp-for="Code" class="form-label"></label>
                                        <input asp-for="Code" class="form-control" placeholder="请输入发票代号" />
                                        <span asp-validation-for="Code" class="text-danger"></span>
                                    </div>

                                    <!-- 收件人 -->
                                    <div class="form-group">
                                        <label asp-for="ReceiverName" class="form-label"></label>
                                        <input asp-for="ReceiverName" class="form-control" placeholder="请输入收件人姓名" />
                                        <span asp-validation-for="ReceiverName" class="text-danger"></span>
                                    </div>

                                    <!-- 收件人电话 -->
                                    <div class="form-group">
                                        <label asp-for="ReceiverPhone" class="form-label"></label>
                                        <input asp-for="ReceiverPhone" class="form-control" placeholder="请输入收件人电话" />
                                        <span asp-validation-for="ReceiverPhone" class="text-danger"></span>
                                    </div>

                                    <!-- 邮寄地址 -->
                                    <div class="form-group">
                                        <label asp-for="MailingAddress" class="form-label"></label>
                                        <textarea asp-for="MailingAddress" class="form-control" rows="2" placeholder="请输入发票邮寄地址"></textarea>
                                        <span asp-validation-for="MailingAddress" class="text-danger"></span>
                                    </div>

                                    <!-- 申请人电话 -->
                                    <div class="form-group">
                                        <label asp-for="ApplicantPhone" class="form-label"></label>
                                        <input asp-for="ApplicantPhone" class="form-control" placeholder="请输入申请人电话" />
                                        <span asp-validation-for="ApplicantPhone" class="text-danger"></span>
                                    </div>

                                    <!-- 客户电子邮件 -->
                                    <div class="form-group">
                                        <label asp-for="CustomerEmail" class="form-label"></label>
                                        <input asp-for="CustomerEmail" class="form-control" type="email" placeholder="请输入客户电子邮件" />
                                        <span asp-validation-for="CustomerEmail" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card-footer">
                            <div class="row">
                                <div class="col-md-6">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> 保存更改
                                    </button>
                                    <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-secondary ml-2">
                                        <i class="fas fa-times"></i> 取消
                                    </a>
                                </div>
                                <div class="col-md-6 text-right">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i> 
                                        标有 <span class="text-danger">*</span> 的字段为必填项
                                    </small>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="col-md-4">
                <!-- 编辑说明 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-info-circle"></i> 编辑说明
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> 注意事项</h6>
                            <ul class="mb-0">
                                <li>关联款项不可修改</li>
                                <li>修改发票金额可能影响款项开票状态</li>
                                <li>发票状态变更会影响相关统计</li>
                                <li>已作废的发票不建议再次修改</li>
                            </ul>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb"></i> 提示</h6>
                            <p class="mb-0">
                                如果发票已经寄出，建议谨慎修改发票信息。
                                状态变更会记录在系统日志中。
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 快捷操作 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-bolt"></i> 快捷操作
                        </h3>
                    </div>
                    <div class="card-body">
                        <button type="button" class="btn btn-outline-secondary btn-sm btn-block" onclick="setDefaultTaxRate()">
                            <i class="fas fa-percentage"></i> 设置默认税率（13%）
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm btn-block" onclick="markAsCancelled()">
                            <i class="fas fa-ban"></i> 标记为已作废
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm btn-block" onclick="markAsNormal()">
                            <i class="fas fa-check"></i> 标记为正常
                        </button>
                    </div>
                </div>

                <!-- 税额计算器 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-calculator"></i> 税额计算器
                        </h3>
                    </div>
                    <div class="card-body">
                        <div id="taxCalculator">
                            <div class="form-group">
                                <label>发票金额:</label>
                                <div class="input-group">
                                    <input type="number" id="calcAmount" class="form-control" placeholder="0.00" step="0.01">
                                    <div class="input-group-append">
                                        <span class="input-group-text">元</span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>税率:</label>
                                <select id="calcTaxRate" class="form-control">
                                    <option value="16">16%</option>
                                    <option value="13" selected>13%</option>
                                    <option value="6">6%</option>
                                    <option value="0">免税</option>
                                </select>
                            </div>
                            <div class="result">
                                <p><strong>税额:</strong> <span id="taxAmount">0.00</span> 元</p>
                                <p><strong>不含税金额:</strong> <span id="netAmount">0.00</span> 元</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作历史 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-history"></i> 操作历史
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <div class="time-label">
                                <span class="bg-info">最近操作</span>
                            </div>
                            <div>
                                <i class="fas fa-edit bg-blue"></i>
                                <div class="timeline-item">
                                    <span class="time"><i class="fas fa-clock"></i> 正在编辑</span>
                                    <h3 class="timeline-header">编辑发票信息</h3>
                                    <div class="timeline-body">
                                        当前正在编辑发票信息，请确认所有字段填写正确。
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        function setDefaultTaxRate() {
            document.getElementById('TaxRate').value = '13';
            toastr.info('已设置默认税率为13%');
            calculateTax();
        }

        function markAsCancelled() {
            document.getElementById('Status').value = 'Cancelled';
            toastr.warning('已标记为已作废');
        }

        function markAsNormal() {
            document.getElementById('Status').value = 'Normal';
            toastr.success('已标记为正常');
        }

        function calculateTax() {
            const amount = parseFloat($('#Amount').val()) || 0;
            const taxRate = parseFloat($('#TaxRate').val()) || 0;
            
            if (amount > 0 && taxRate > 0) {
                const taxAmount = amount * taxRate / (100 + taxRate);
                const netAmount = amount - taxAmount;
                
                $('#taxAmount').text(taxAmount.toFixed(2));
                $('#netAmount').text(netAmount.toFixed(2));
            } else {
                $('#taxAmount').text('0.00');
                $('#netAmount').text(amount.toFixed(2));
            }
        }

        // 表单验证增强
        $(document).ready(function() {
            // 金额输入验证
            $('#Amount').on('input', function() {
                const value = parseFloat($(this).val());
                if (value <= 0) {
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
                calculateTax();
            });

            // 税率变更处理
            $('#TaxRate').on('change', function() {
                calculateTax();
            });

            // 发票类型变更处理
            $('#Type').on('change', function() {
                const type = $(this).val();
                if (type === '收据') {
                    $('#TaxRate').val('0');
                    toastr.info('收据类型已自动设置为免税');
                    calculateTax();
                }
            });

            // 税额计算器
            $('#calcAmount, #calcTaxRate').on('input change', function() {
                const amount = parseFloat($('#calcAmount').val()) || 0;
                const taxRate = parseFloat($('#calcTaxRate').val()) || 0;
                
                if (amount > 0 && taxRate > 0) {
                    const taxAmount = amount * taxRate / (100 + taxRate);
                    const netAmount = amount - taxAmount;
                    
                    $('#taxAmount').text(taxAmount.toFixed(2));
                    $('#netAmount').text(netAmount.toFixed(2));
                } else {
                    $('#taxAmount').text('0.00');
                    $('#netAmount').text(amount.toFixed(2));
                }
            });

            // 初始计算
            calculateTax();
        });
    </script>
}

@section Styles {
    <style>
        .required::after {
            content: " *";
            color: red;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .is-invalid {
            border-color: #dc3545;
        }

        .timeline {
            position: relative;
            margin: 0 0 30px 0;
            padding: 0;
            list-style: none;
        }

        .timeline:before {
            content: '';
            position: absolute;
            top: 0;
            left: 25px;
            height: 100%;
            width: 2px;
            background: #dee2e6;
        }

        .timeline > div {
            position: relative;
            margin-right: 10px;
            margin-bottom: 15px;
        }

        .timeline > div > .timeline-item {
            box-shadow: 0 1px 1px rgba(0,0,0,0.1);
            border-radius: 3px;
            margin-top: 0;
            background: #fff;
            color: #444;
            margin-left: 60px;
            margin-right: 15px;
            padding: 0;
            position: relative;
        }

        .timeline > div > .fas {
            width: 30px;
            height: 30px;
            font-size: 15px;
            line-height: 30px;
            position: absolute;
            color: #666;
            background: #f4f4f4;
            border-radius: 50%;
            text-align: center;
            left: 18px;
            top: 0;
        }

        #taxCalculator .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
    </style>
}
