@model ZXCRM.WebUI.Models.ViewModels.UserPermissionViewModel
@{
    ViewData["Title"] = $"用户权限分配 - {Model.UserName}";
}

<!-- 用户权限分配页面 -->
<div class="permission-assignment-container">
    <!-- 页面标题栏 -->
    <div class="assignment-header">
        <div class="assignment-title">
            <h2>
                <i class="fas fa-user-shield"></i>
                用户权限分配
            </h2>
            <div class="user-info">
                <span class="badge bg-primary">用户：@Model.UserName</span>
                <span class="badge bg-info">ID：#@Model.UserId</span>
            </div>
        </div>
        <div class="assignment-actions">
            <button type="button" class="btn btn-success" onclick="savePermissions()">
                <i class="fas fa-save"></i> 保存权限设置
            </button>
            <a asp-controller="User" asp-action="Index" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> 返回用户列表
            </a>
        </div>
    </div>

    <!-- 权限分配内容 -->
    <div class="assignment-content">
        <form id="permissionForm">
            @Html.AntiForgeryToken()
            <input type="hidden" id="userId" value="@Model.UserId" />

            <!-- 业务模块权限 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-briefcase text-primary"></i>
                        业务模块权限
                        <small class="text-muted">（普通用户权限）</small>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="permission-grid">
                        @{
                            var businessModules = new[] { "商机管理", "订单管理", "款项管理", "发票管理", "报表统计" };
                            var permissionTypes = new[] { "查询", "新建", "修改", "删除" };
                        }

                        <!-- 表头 -->
                        <div class="permission-header">
                            <div class="module-name">模块</div>
                            @foreach (var permType in permissionTypes)
                            {
                                <div class="permission-type">@permType</div>
                            }
                        </div>

                        <!-- 业务模块权限行 -->
                        @foreach (var module in businessModules)
                        {
                            <div class="permission-row">
                                <div class="module-name">
                                    <i class="fas fa-cube"></i>
                                    @module
                                </div>
                                @foreach (var permType in permissionTypes)
                                {
                                    <div class="permission-type">
                                        @{
                                            var isChecked = Model.UserPermissions.Any(up => up.ModuleType == module && 
                                                Model.AllPermissions.Any(p => p.Id == up.PermissionId && p.Code == permType));
                                            var isDefault = permType == "查询"; // 查询权限是默认的
                                        }
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" 
                                                   type="checkbox" 
                                                   id="@($"{module}_{permType}")"
                                                   data-module="@module" 
                                                   data-permission="@permType"
                                                   @(isChecked ? "checked" : "")
                                                   @(isDefault ? "data-default='true'" : "")>
                                            <label class="form-check-label" for="@($"{module}_{permType}")">
                                                @if (isDefault)
                                                {
                                                    <span class="text-success">
                                                        <i class="fas fa-check-circle"></i>
                                                    </span>
                                                }
                                            </label>
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                    </div>

                    <div class="permission-note mt-3">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>说明：</strong>
                            <ul class="mb-0">
                                <li><i class="fas fa-check-circle text-success"></i> 标记的权限为普通用户默认权限</li>
                                <li>查询权限是基础权限，建议保持开启</li>
                                <li>其他权限可根据用户职责进行分配</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统管理权限 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs text-warning"></i>
                        系统管理权限
                        <small class="text-muted">（管理员权限）</small>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="permission-grid">
                        @{
                            var systemModules = new[] { "用户管理", "部门管理", "权限管理" };
                        }

                        <!-- 表头 -->
                        <div class="permission-header">
                            <div class="module-name">模块</div>
                            @foreach (var permType in permissionTypes)
                            {
                                <div class="permission-type">@permType</div>
                            }
                        </div>

                        <!-- 系统模块权限行 -->
                        @foreach (var module in systemModules)
                        {
                            <div class="permission-row">
                                <div class="module-name">
                                    <i class="fas fa-shield-alt"></i>
                                    @module
                                </div>
                                @foreach (var permType in permissionTypes)
                                {
                                    <div class="permission-type">
                                        @{
                                            var isChecked = Model.UserPermissions.Any(up => up.ModuleType == module && 
                                                Model.AllPermissions.Any(p => p.Id == up.PermissionId && p.Code == permType));
                                        }
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" 
                                                   type="checkbox" 
                                                   id="@($"{module}_{permType}")"
                                                   data-module="@module" 
                                                   data-permission="@permType"
                                                   @(isChecked ? "checked" : "")>
                                            <label class="form-check-label" for="@($"{module}_{permType}")"></label>
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                    </div>

                    <div class="permission-note mt-3">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>警告：</strong>
                            系统管理权限具有较高的安全级别，请谨慎分配。拥有这些权限的用户可以管理其他用户和系统设置。
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="card mt-4">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-magic"></i>
                        快速操作
                    </h6>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="setDefaultPermissions()">
                            <i class="fas fa-user"></i> 设为普通用户
                        </button>
                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="setAdminPermissions()">
                            <i class="fas fa-user-shield"></i> 设为管理员
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearAllPermissions()">
                            <i class="fas fa-times"></i> 清除所有权限
                        </button>
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="resetToOriginal()">
                            <i class="fas fa-undo"></i> 重置为原始状态
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    <script>
        // 保存原始权限状态
        var originalPermissions = [];
        
        $(document).ready(function() {
            // 记录原始权限状态
            saveOriginalState();
            
            // 权限变更监听
            $('.permission-checkbox').on('change', function() {
                updatePermissionStatus();
            });
        });

        function saveOriginalState() {
            originalPermissions = [];
            $('.permission-checkbox:checked').each(function() {
                originalPermissions.push({
                    module: $(this).data('module'),
                    permission: $(this).data('permission')
                });
            });
        }

        function updatePermissionStatus() {
            var hasChanges = false;
            var currentPermissions = [];
            
            $('.permission-checkbox:checked').each(function() {
                currentPermissions.push({
                    module: $(this).data('module'),
                    permission: $(this).data('permission')
                });
            });

            // 检查是否有变更
            if (currentPermissions.length !== originalPermissions.length) {
                hasChanges = true;
            } else {
                for (var i = 0; i < currentPermissions.length; i++) {
                    var found = originalPermissions.some(function(orig) {
                        return orig.module === currentPermissions[i].module && 
                               orig.permission === currentPermissions[i].permission;
                    });
                    if (!found) {
                        hasChanges = true;
                        break;
                    }
                }
            }

            // 更新保存按钮状态
            var saveBtn = $('.btn-success');
            if (hasChanges) {
                saveBtn.removeClass('btn-success').addClass('btn-warning');
                saveBtn.html('<i class="fas fa-exclamation-triangle"></i> 有未保存的更改');
            } else {
                saveBtn.removeClass('btn-warning').addClass('btn-success');
                saveBtn.html('<i class="fas fa-save"></i> 保存权限设置');
            }
        }

        function setDefaultPermissions() {
            // 清除所有权限
            $('.permission-checkbox').prop('checked', false);
            
            // 设置业务模块的查询权限
            var businessModules = ['商机管理', '订单管理', '款项管理', '发票管理', '报表统计'];
            businessModules.forEach(function(module) {
                $('#' + module + '_查询').prop('checked', true);
            });
            
            updatePermissionStatus();
            toastr.info('已设置为普通用户权限（业务模块查询权限）');
        }

        function setAdminPermissions() {
            // 设置所有权限
            $('.permission-checkbox').prop('checked', true);
            
            updatePermissionStatus();
            toastr.info('已设置为管理员权限（所有权限）');
        }

        function clearAllPermissions() {
            if (confirm('确定要清除所有权限吗？这将使用户无法访问任何功能。')) {
                $('.permission-checkbox').prop('checked', false);
                updatePermissionStatus();
                toastr.warning('已清除所有权限');
            }
        }

        function resetToOriginal() {
            // 重置为原始状态
            $('.permission-checkbox').prop('checked', false);
            originalPermissions.forEach(function(perm) {
                $('#' + perm.module + '_' + perm.permission).prop('checked', true);
            });
            
            updatePermissionStatus();
            toastr.info('已重置为原始权限状态');
        }

        function savePermissions() {
            var userId = $('#userId').val();
            var permissions = [];
            
            $('.permission-checkbox:checked').each(function() {
                var module = $(this).data('module');
                var permissionCode = $(this).data('permission');
                
                // 查找对应的权限ID
                @foreach (var permission in Model.AllPermissions)
                {
                    <text>
                    if (permissionCode === '@permission.Code') {
                        permissions.push({
                            UserId: parseInt(userId),
                            PermissionId: @permission.Id,
                            ModuleType: module
                        });
                    }
                    </text>
                }
            });

            // 发送保存请求
            $.ajax({
                url: '/api/userpermission/user/' + userId,
                type: 'PUT',
                contentType: 'application/json',
                data: JSON.stringify(permissions),
                headers: {
                    'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                },
                success: function(response) {
                    toastr.success('权限设置保存成功！');
                    saveOriginalState(); // 更新原始状态
                    updatePermissionStatus();
                },
                error: function(xhr, status, error) {
                    toastr.error('保存权限设置失败：' + error);
                }
            });
        }

        // 显示提示消息
        @if (TempData["SuccessMessage"] != null)
        {
            <text>
            toastr.success('@TempData["SuccessMessage"]');
            </text>
        }

        @if (TempData["ErrorMessage"] != null)
        {
            <text>
            toastr.error('@TempData["ErrorMessage"]');
            </text>
        }
    </script>
}

<style>
    .permission-assignment-container {
        padding: 20px;
    }

    .assignment-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #dee2e6;
    }

    .assignment-title h2 {
        margin: 0;
        color: #495057;
        font-size: 1.5rem;
    }

    .assignment-title i {
        margin-right: 10px;
        color: #6c757d;
    }

    .user-info {
        margin-top: 8px;
    }

    .user-info .badge {
        margin-right: 8px;
        font-size: 0.875em;
    }

    .assignment-actions .btn {
        margin-left: 10px;
    }

    .permission-grid {
        display: grid;
        grid-template-columns: 200px repeat(4, 1fr);
        gap: 1px;
        background-color: #dee2e6;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        overflow: hidden;
    }

    .permission-header {
        display: contents;
    }

    .permission-header > div {
        background-color: #f8f9fa;
        padding: 12px 8px;
        font-weight: 600;
        text-align: center;
        border-bottom: 2px solid #dee2e6;
    }

    .permission-row {
        display: contents;
    }

    .permission-row > div {
        background-color: white;
        padding: 12px 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 50px;
    }

    .module-name {
        justify-content: flex-start !important;
        font-weight: 500;
        background-color: #f8f9fa !important;
    }

    .module-name i {
        margin-right: 8px;
        color: #6c757d;
    }

    .permission-type {
        text-align: center;
    }

    .form-check {
        margin: 0;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .form-check-input {
        margin: 0;
    }

    .form-check-label {
        margin-left: 5px;
    }

    .permission-note .alert {
        margin-bottom: 0;
        font-size: 0.875rem;
    }

    .permission-note ul {
        padding-left: 20px;
        margin-top: 8px;
    }

    .permission-note li {
        margin-bottom: 4px;
    }

    .card-title i {
        margin-right: 8px;
    }

    .btn-group .btn {
        font-size: 0.875rem;
    }

    /* 响应式设计 */
    @@media (max-width: 768px) {
        .permission-grid {
            grid-template-columns: 150px repeat(4, 1fr);
            font-size: 0.875rem;
        }

        .assignment-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .assignment-actions {
            margin-top: 15px;
            width: 100%;
        }

        .assignment-actions .btn {
            margin-left: 0;
            margin-right: 10px;
            margin-bottom: 5px;
        }
    }

    @@media (max-width: 576px) {
        .permission-grid {
            grid-template-columns: 120px repeat(2, 1fr);
        }

        .permission-header > div:nth-child(n+4),
        .permission-row > div:nth-child(n+4) {
            display: none;
        }

        .btn-group {
            flex-direction: column;
            width: 100%;
        }

        .btn-group .btn {
            margin-bottom: 5px;
        }
    }

    /* 权限状态指示 */
    .permission-checkbox:checked + label::before {
        background-color: #28a745;
        border-color: #28a745;
    }

    .permission-checkbox[data-default="true"]:checked + label {
        color: #28a745;
    }

    /* 卡片样式增强 */
    .card {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border: 1px solid rgba(0, 0, 0, 0.125);
    }

    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    }
</style>
