using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ZXCRM.WebUI.Services;
using System.Reflection;
using System.Diagnostics;

namespace ZXCRM.WebUI.Controllers
{
    [Authorize]
    public class SystemController : Controller
    {
        private readonly IApiPerformanceService _performanceService;
        private readonly ILogger<SystemController> _logger;

        public SystemController(IApiPerformanceService performanceService, ILogger<SystemController> logger)
        {
            _performanceService = performanceService;
            _logger = logger;
        }

        // GET: System/Health
        public IActionResult Health()
        {
            try
            {
                var healthInfo = new SystemHealthInfo
                {
                    ApplicationName = "ZXCRM",
                    Version = GetApplicationVersion(),
                    Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown",
                    MachineName = Environment.MachineName,
                    ProcessorCount = Environment.ProcessorCount,
                    WorkingSet = Environment.WorkingSet,
                    StartTime = Process.GetCurrentProcess().StartTime,
                    UpTime = DateTime.Now - Process.GetCurrentProcess().StartTime,
                    
                    // 性能统计
                    PerformanceStats = _performanceService.GetPerformanceStats(),
                    
                    // 系统状态
                    Status = "Healthy",
                    Timestamp = DateTime.Now
                };

                return View(healthInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting system health information");
                
                var errorInfo = new SystemHealthInfo
                {
                    ApplicationName = "ZXCRM",
                    Status = "Unhealthy",
                    ErrorMessage = ex.Message,
                    Timestamp = DateTime.Now
                };
                
                return View(errorInfo);
            }
        }

        // GET: System/Performance
        public IActionResult Performance()
        {
            try
            {
                var stats = _performanceService.GetPerformanceStats();
                return View(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting performance statistics");
                TempData["ErrorMessage"] = "获取性能统计信息时发生错误";
                return RedirectToAction(nameof(Health));
            }
        }

        // GET: System/Logs
        public IActionResult Logs(int pageSize = 50)
        {
            try
            {
                // 这里可以实现日志查看功能
                // 目前返回一个简单的视图
                var logInfo = new
                {
                    Message = "日志查看功能正在开发中",
                    LogLevel = "Information",
                    Timestamp = DateTime.Now
                };

                return View(logInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error accessing logs");
                TempData["ErrorMessage"] = "访问日志时发生错误";
                return RedirectToAction(nameof(Health));
            }
        }

        // POST: System/ClearCache
        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult ClearCache()
        {
            try
            {
                // 这里可以实现缓存清理功能
                _logger.LogInformation("Cache clear requested by user");
                
                TempData["SuccessMessage"] = "缓存清理成功";
                return RedirectToAction(nameof(Health));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing cache");
                TempData["ErrorMessage"] = "清理缓存时发生错误";
                return RedirectToAction(nameof(Health));
            }
        }

        // GET: System/Info
        public IActionResult Info()
        {
            try
            {
                var systemInfo = new
                {
                    Application = new
                    {
                        Name = "ZXCRM",
                        Version = GetApplicationVersion(),
                        Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown",
                        StartTime = Process.GetCurrentProcess().StartTime,
                        UpTime = DateTime.Now - Process.GetCurrentProcess().StartTime
                    },
                    System = new
                    {
                        MachineName = Environment.MachineName,
                        OSVersion = Environment.OSVersion.ToString(),
                        ProcessorCount = Environment.ProcessorCount,
                        WorkingSet = Environment.WorkingSet,
                        Is64BitProcess = Environment.Is64BitProcess,
                        Is64BitOperatingSystem = Environment.Is64BitOperatingSystem
                    },
                    Runtime = new
                    {
                        Version = Environment.Version.ToString(),
                        FrameworkDescription = System.Runtime.InteropServices.RuntimeInformation.FrameworkDescription,
                        OSDescription = System.Runtime.InteropServices.RuntimeInformation.OSDescription,
                        ProcessArchitecture = System.Runtime.InteropServices.RuntimeInformation.ProcessArchitecture.ToString(),
                        OSArchitecture = System.Runtime.InteropServices.RuntimeInformation.OSArchitecture.ToString()
                    }
                };

                return View(systemInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting system information");
                TempData["ErrorMessage"] = "获取系统信息时发生错误";
                return RedirectToAction(nameof(Health));
            }
        }

        private string GetApplicationVersion()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                var version = assembly.GetName().Version;
                return version?.ToString() ?? "Unknown";
            }
            catch
            {
                return "Unknown";
            }
        }
    }

    public class SystemHealthInfo
    {
        public string ApplicationName { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public string Environment { get; set; } = string.Empty;
        public string MachineName { get; set; } = string.Empty;
        public int ProcessorCount { get; set; }
        public long WorkingSet { get; set; }
        public DateTime StartTime { get; set; }
        public TimeSpan UpTime { get; set; }
        public string Status { get; set; } = string.Empty;
        public string? ErrorMessage { get; set; }
        public DateTime Timestamp { get; set; }
        public ApiPerformanceStats? PerformanceStats { get; set; }
        
        public string WorkingSetMB => (WorkingSet / 1024 / 1024).ToString("N0");
        public string UpTimeFormatted => $"{UpTime.Days}天 {UpTime.Hours}小时 {UpTime.Minutes}分钟";
    }
}
