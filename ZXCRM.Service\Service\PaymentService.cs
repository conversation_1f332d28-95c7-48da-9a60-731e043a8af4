using AutoMapper;
using ZXCRM.Data.Entities;
using ZXCRM.Data.UnitOfWork;
using ZXCRM.Service.DTOs;
using ZXCRM.Service.Interface;

namespace ZXCRM.Service.Service
{
    public class PaymentService : IPaymentService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public PaymentService(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task<IEnumerable<PaymentDTO>> GetAllPaymentsAsync()
        {
            var payments = await _unitOfWork.Payments.GetAllWithIncludesAsync(
                p => p.Order,
                p => p.Order.AccountManager,
                p => p.Order.ProjectManager
            );
            return _mapper.Map<IEnumerable<PaymentDTO>>(payments);
        }

        public async Task<IEnumerable<PaymentDTO>> GetPaymentsByOrderIdAsync(int orderId)
        {
            var payments = await _unitOfWork.Payments.FindWithIncludesAsync(
                p => p.OrderId == orderId,
                p => p.Order,
                p => p.Order.AccountManager,
                p => p.Order.ProjectManager
            );
            return _mapper.Map<IEnumerable<PaymentDTO>>(payments);
        }

        public async Task<PaymentDTO?> GetPaymentByIdAsync(int id)
        {
            var payment = await _unitOfWork.Payments.GetByIdWithIncludesAsync(id,
                p => p.Order,
                p => p.Order.AccountManager,
                p => p.Order.ProjectManager
            );
            return payment != null ? _mapper.Map<PaymentDTO>(payment) : null;
        }

        public async Task<PaymentDTO> CreatePaymentAsync(CreatePaymentDTO createPaymentDto)
        {
            // 检查订单是否存在
            var order = await _unitOfWork.Orders.GetByIdAsync(createPaymentDto.OrderId);
            if (order == null)
            {
                throw new Exception("关联订单不存在");
            }

            // 创建款项
            var payment = _mapper.Map<Payment>(createPaymentDto);
            payment.CreatedAt = DateTime.Now;
            payment.UpdatedAt = DateTime.Now;

            await _unitOfWork.Payments.AddAsync(payment);
            await _unitOfWork.SaveChangesAsync();

            // 如果款项状态为已开票，更新订单状态
            if (payment.InvoiceStatus == "已开票" && payment.InvoiceDate.HasValue)
            {
                // 可以在这里添加更新订单状态的逻辑
            }

            // 重新获取包含关联数据的款项
            var createdPayment = await _unitOfWork.Payments.GetByIdWithIncludesAsync(payment.Id,
                p => p.Order,
                p => p.Order.AccountManager,
                p => p.Order.ProjectManager
            );

            return _mapper.Map<PaymentDTO>(createdPayment);
        }

        public async Task<PaymentDTO?> UpdatePaymentAsync(UpdatePaymentDTO updatePaymentDto)
        {
            var payment = await _unitOfWork.Payments.GetByIdAsync(updatePaymentDto.Id);
            if (payment == null)
            {
                return null;
            }

            // 更新款项信息
            payment.Name = updatePaymentDto.Name;
            payment.Code = updatePaymentDto.Code;
            payment.PaymentType = updatePaymentDto.PaymentType;
            payment.Amount = updatePaymentDto.Amount;
            payment.Currency = updatePaymentDto.Currency;
            payment.SettlementAmount = updatePaymentDto.SettlementAmount;
            payment.ExpectedPaymentDate = updatePaymentDto.ExpectedPaymentDate;
            payment.ActualPaymentDate = updatePaymentDto.ActualPaymentDate;
            payment.InvoiceStatus = updatePaymentDto.InvoiceStatus;
            payment.InvoiceDate = updatePaymentDto.InvoiceDate;
            payment.UpdatedAt = DateTime.Now;

            await _unitOfWork.Payments.UpdateAsync(payment);
            await _unitOfWork.SaveChangesAsync();

            // 重新获取包含关联数据的款项
            var updatedPayment = await _unitOfWork.Payments.GetByIdWithIncludesAsync(payment.Id,
                p => p.Order,
                p => p.Order.AccountManager,
                p => p.Order.ProjectManager
            );

            return _mapper.Map<PaymentDTO>(updatedPayment);
        }

        public async Task<bool> DeletePaymentAsync(int id)
        {
            var payment = await _unitOfWork.Payments.GetByIdAsync(id);
            if (payment == null)
            {
                return false;
            }

            // 检查是否有关联的发票
            var invoices = await _unitOfWork.Invoices.FindAsync(i => i.PaymentId == id);
            if (invoices.Any())
            {
                throw new Exception("该款项已关联发票，无法删除");
            }

            // 软删除
            payment.IsDeleted = true;
            payment.UpdatedAt = DateTime.Now;

            await _unitOfWork.Payments.UpdateAsync(payment);
            await _unitOfWork.SaveChangesAsync();

            return true;
        }
    }
}
