@model ZXCRM.WebUI.Models.ViewModels.UserPermissionListViewModel
@{
    ViewData["Title"] = "用户权限管理";
}

<!-- 用户权限管理页面容器 -->
<div class="permission-management-container">
    <!-- 页面标题栏 -->
    <div class="management-header">
        <div class="management-title">
            <h2>
                <i class="fas fa-users-cog"></i>
                用户权限管理
            </h2>
            <small class="text-muted">为用户分配业务模块和系统管理权限</small>
        </div>
        <div class="management-actions">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="showPermissionGuide()">
                    <i class="fas fa-question-circle"></i> 权限说明
                </button>
            </div>
        </div>
    </div>

    <!-- 查询区域 -->
    <div class="query-section" id="querySection">
        <div class="query-header">
            <div class="query-title">
                <i class="fas fa-filter"></i>
                <span>筛选条件</span>
                <span class="badge bg-info ml-2 text-white">共 @Model.TotalCount 个用户</span>
            </div>
            <button type="button" class="query-toggle" onclick="toggleQuerySection()">
                <i class="fas fa-chevron-up" id="queryToggleIcon"></i>
            </button>
        </div>
        <div class="query-content" id="queryContent">
            <form asp-action="Index" method="get" class="query-form">
                <div class="query-row">
                    <div class="query-item query-item-search">
                        <label>搜索用户:</label>
                        <div class="input-group input-group-sm">
                            <input type="text" name="searchTerm" value="@Model.SearchTerm" class="form-control" placeholder="用户名、姓名或部门">
                            <button type="submit" class="btn btn-outline-secondary">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="query-item">
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearSearch()">
                            <i class="fas fa-times"></i> 清除
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 用户权限列表 -->
    <div class="table-section">
        @if (Model.Users.Any())
        {
            <div class="table-container">
                <table class="table table-compact">
                    <thead class="table-header-fixed">
                        <tr>
                            <th width="120">用户名</th>
                            <th width="100">姓名</th>
                            <th width="100">部门</th>
                            <th width="60">角色</th>
                            <th width="200">业务权限</th>
                            <th width="150">系统权限</th>
                            <th width="60">状态</th>
                            <th width="120">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var user in Model.Users)
                        {
                            <tr class="table-row">
                                <td>
                                    <div class="user-info">
                                        <strong>@user.Username</strong>
                                        <small class="text-muted d-block">ID: @user.Id</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="user-name">@user.Name</span>
                                </td>
                                <td>
                                    <span class="badge bg-secondary text-white">@user.DepartmentName</span>
                                </td>
                                <td>
                                    <span class="badge bg-info text-white">@user.Role</span>
                                </td>
                                <td>
                                    <div class="permission-badges">
                                        @if (user.HasBusinessAccess)
                                        {
                                            @foreach (var perm in user.BusinessPermissions.Take(3))
                                            {
                                                <span class="badge bg-primary text-white badge-xs">@perm</span>
                                            }
                                            @if (user.BusinessPermissions.Count > 3)
                                            {
                                                <span class="badge bg-light text-dark badge-xs">+@(user.BusinessPermissions.Count - 3)</span>
                                            }
                                        }
                                        else
                                        {
                                            <span class="text-muted">无业务权限</span>
                                        }
                                    </div>
                                </td>
                                <td>
                                    <div class="permission-badges">
                                        @if (user.HasSystemAccess)
                                        {
                                            @foreach (var perm in user.SystemPermissions.Take(2))
                                            {
                                                <span class="badge bg-warning text-dark badge-xs">@perm</span>
                                            }
                                            @if (user.SystemPermissions.Count > 2)
                                            {
                                                <span class="badge bg-light text-dark badge-xs">+@(user.SystemPermissions.Count - 2)</span>
                                            }
                                        }
                                        else
                                        {
                                            <span class="text-muted">无系统权限</span>
                                        }
                                    </div>
                                </td>
                                <td>
                                    @if (user.Status == "Active")
                                    {
                                        <span class="badge bg-success text-white">
                                            <i class="fas fa-check"></i> 启用
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-danger text-white">
                                            <i class="fas fa-times"></i> 禁用
                                        </span>
                                    }
                                </td>
                                <td>
                                    <div class="btn-group btn-group-xs" role="group">
                                        <a asp-action="UserPermissions" asp-route-userId="@user.Id" class="btn btn-primary btn-xs" title="权限分配">
                                            <i class="fas fa-user-shield"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-info btn-xs" title="快速设置" onclick="showQuickSettings(@user.Id, '@user.Name')">
                                            <i class="fas fa-magic"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-users-slash"></i>
                </div>
                <div class="empty-content">
                    <h4>暂无用户数据</h4>
                    <p class="text-muted">系统中还没有用户，请先创建用户</p>
                    <a asp-controller="User" asp-action="Create" class="btn btn-primary">创建用户</a>
                </div>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        function showPermissionGuide() {
            alert('权限说明：\n\n业务模块权限：\n- 商机管理、订单管理、款项管理、发票管理、报表统计\n- 普通用户默认拥有查询权限\n\n系统管理权限：\n- 用户管理、部门管理、权限管理\n- 仅管理员可分配');
        }

        function showQuickSettings(userId, userName) {
            if (confirm('为用户 ' + userName + ' 设置默认业务权限？')) {
                // 这里可以调用API设置默认权限
                alert('功能开发中...');
            }
        }

        function clearSearch() {
            window.location.href = '@Url.Action("Index")';
        }

        // 查询区域折叠功能
        function toggleQuerySection() {
            var content = document.getElementById('queryContent');
            var icon = document.getElementById('queryToggleIcon');
            
            if (content.style.display === 'none') {
                content.style.display = 'block';
                icon.className = 'fas fa-chevron-up';
            } else {
                content.style.display = 'none';
                icon.className = 'fas fa-chevron-down';
            }
        }
    </script>
}

<style>
    .permission-management-container {
        padding: 20px;
    }

    .management-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #dee2e6;
    }

    .management-title h2 {
        margin: 0;
        color: #495057;
        font-size: 1.5rem;
    }

    .management-title i {
        margin-right: 10px;
        color: #6c757d;
    }

    .permission-badges {
        display: flex;
        flex-wrap: wrap;
        gap: 2px;
    }

    .badge-xs {
        font-size: 0.75em;
        padding: 0.25em 0.4em;
    }

    .user-info strong {
        color: #495057;
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
    }

    .empty-icon {
        font-size: 4rem;
        color: #6c757d;
        margin-bottom: 20px;
    }

    .query-section {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        margin-bottom: 20px;
    }

    .query-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        border-bottom: 1px solid #dee2e6;
        background-color: #e9ecef;
        border-radius: 0.375rem 0.375rem 0 0;
    }

    .query-content {
        padding: 16px;
    }

    .query-row {
        display: flex;
        gap: 15px;
        align-items: end;
        flex-wrap: wrap;
    }

    .query-item {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .query-item-search {
        flex: 1;
        min-width: 200px;
    }

    .query-toggle {
        background: none;
        border: none;
        color: #6c757d;
        cursor: pointer;
    }

    .table-compact {
        font-size: 0.875rem;
    }

    .table-compact th,
    .table-compact td {
        padding: 8px 12px;
        vertical-align: middle;
    }
</style>
