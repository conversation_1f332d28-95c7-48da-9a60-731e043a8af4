using ZXCRM.Service.DTOs;
using ZXCRM.WebUI.Models;

namespace ZXCRM.WebUI.Services
{
    public interface IPermissionService
    {
        Task<IEnumerable<PermissionDto>> GetAllAsync();
        Task<PermissionDto?> GetByIdAsync(int id);
        Task<PermissionDto?> CreateAsync(PermissionDto permission);
        Task<PermissionDto?> UpdateAsync(PermissionDto permission);
        Task<bool> DeleteAsync(int id);
    }
}
