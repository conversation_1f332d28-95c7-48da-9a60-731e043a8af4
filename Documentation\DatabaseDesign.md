# ZXCRM 数据库设计

## 数据库概述

ZXCRM系统使用SQL Server 2022作为数据库管理系统，采用关系型数据库设计。数据库名称为`ZXCRM`。

## 实体关系图

![ER图](https://via.placeholder.com/800x600?text=ZXCRM+ER+Diagram)

## 数据表设计

### 用户和部门管理

#### 部门表 (Departments)

| 字段名 | 数据类型 | 允许空 | 主键 | 说明 |
|--------|----------|--------|------|------|
| Id | int | 否 | 是 | 部门ID，自增 |
| Name | nvarchar(50) | 否 | 否 | 部门名称 |
| Code | nvarchar(20) | 否 | 否 | 部门编号 |
| ParentId | int | 是 | 否 | 父部门ID，自关联 |
| CreatedAt | datetime | 否 | 否 | 创建时间 |
| UpdatedAt | datetime | 否 | 否 | 更新时间 |
| IsDeleted | bit | 否 | 否 | 是否删除 |

#### 用户表 (Users)

| 字段名 | 数据类型 | 允许空 | 主键 | 说明 |
|--------|----------|--------|------|------|
| Id | int | 否 | 是 | 用户ID，自增 |
| Username | nvarchar(50) | 否 | 否 | 登录名 |
| Password | nvarchar(100) | 否 | 否 | 密码（加密存储） |
| Name | nvarchar(50) | 否 | 否 | 姓名 |
| Gender | nvarchar(10) | 是 | 否 | 性别 |
| Email | nvarchar(100) | 是 | 否 | 电子邮件 |
| Phone | nvarchar(20) | 是 | 否 | 电话 |
| DepartmentId | int | 否 | 否 | 所属部门ID，外键 |
| CreatedAt | datetime | 否 | 否 | 创建时间 |
| UpdatedAt | datetime | 否 | 否 | 更新时间 |
| IsDeleted | bit | 否 | 否 | 是否删除 |

#### 权限表 (Permissions)

| 字段名 | 数据类型 | 允许空 | 主键 | 说明 |
|--------|----------|--------|------|------|
| Id | int | 否 | 是 | 权限ID，自增 |
| Name | nvarchar(50) | 否 | 否 | 权限名称 |
| Code | nvarchar(50) | 否 | 否 | 权限代码 |
| Description | nvarchar(200) | 是 | 否 | 权限描述 |

#### 用户权限表 (UserPermissions)

| 字段名 | 数据类型 | 允许空 | 主键 | 说明 |
|--------|----------|--------|------|------|
| Id | int | 否 | 是 | 记录ID，自增 |
| UserId | int | 否 | 否 | 用户ID，外键 |
| PermissionId | int | 否 | 否 | 权限ID，外键 |
| ModuleType | nvarchar(50) | 否 | 否 | 模块类型（商机、订单、款项、发票） |

### 商机管理

#### 商机表 (Opportunities)

| 字段名 | 数据类型 | 允许空 | 主键 | 说明 |
|--------|----------|--------|------|------|
| Id | int | 否 | 是 | 商机ID，自增 |
| Name | nvarchar(100) | 否 | 否 | 商机名称 |
| CustomerName | nvarchar(100) | 否 | 否 | 客户名称 |
| ContactName | nvarchar(50) | 是 | 否 | 联系人 |
| ContactPhone | nvarchar(20) | 是 | 否 | 联系电话 |
| Content | nvarchar(500) | 是 | 否 | 商机内容 |
| Status | nvarchar(20) | 否 | 否 | 商机状态 |
| CreatedById | int | 否 | 否 | 创建人ID，外键 |
| CreatedAt | datetime | 否 | 否 | 创建时间 |
| UpdatedAt | datetime | 否 | 否 | 更新时间 |
| IsDeleted | bit | 否 | 否 | 是否删除 |

### 订单管理

#### 订单表 (Orders)

| 字段名 | 数据类型 | 允许空 | 主键 | 说明 |
|--------|----------|--------|------|------|
| Id | int | 否 | 是 | 订单ID，自增 |
| Name | nvarchar(100) | 否 | 否 | 订单名称 |
| Code | nvarchar(50) | 否 | 否 | 订单编号 |
| CustomerName | nvarchar(100) | 否 | 否 | 客户名称 |
| ContactName | nvarchar(50) | 是 | 否 | 联系人 |
| ContactPhone | nvarchar(20) | 是 | 否 | 联系电话 |
| OpportunityId | int | 是 | 否 | 关联商机ID，外键 |
| Amount | decimal(18,2) | 否 | 否 | 订单金额 |
| Currency | nvarchar(20) | 否 | 否 | 订单币种 |
| SettlementAmount | decimal(18,2) | 否 | 否 | 结算金额（人民币） |
| AccountManagerId | int | 否 | 否 | 客户经理ID，外键 |
| DepartmentId | int | 否 | 否 | 创建部门ID，外键 |
| SignDate | datetime | 否 | 否 | 签订日期 |
| Status | nvarchar(20) | 否 | 否 | 订单状态 |
| ProjectManagerId | int | 是 | 否 | 项目经理ID，外键 |
| PerformanceDepartmentId | int | 是 | 否 | 业绩归属部门ID，外键 |
| CreatedById | int | 否 | 否 | 创建人ID，外键 |
| CreatedAt | datetime | 否 | 否 | 创建时间 |
| UpdatedAt | datetime | 否 | 否 | 更新时间 |
| IsDeleted | bit | 否 | 否 | 是否删除 |

### 款项管理

#### 款项表 (Payments)

| 字段名 | 数据类型 | 允许空 | 主键 | 说明 |
|--------|----------|--------|------|------|
| Id | int | 否 | 是 | 款项ID，自增 |
| Name | nvarchar(100) | 否 | 否 | 款项名称 |
| Code | nvarchar(50) | 否 | 否 | 款项编号 |
| OrderId | int | 否 | 否 | 关联订单ID，外键 |
| PaymentType | nvarchar(20) | 否 | 否 | 款项类型 |
| Amount | decimal(18,2) | 否 | 否 | 款项金额 |
| Currency | nvarchar(20) | 否 | 否 | 款项币种 |
| SettlementAmount | decimal(18,2) | 否 | 否 | 结算金额（人民币） |
| ExpectedPaymentDate | datetime | 是 | 否 | 预计回款日期 |
| ActualPaymentDate | datetime | 是 | 否 | 实际回款日期 |
| InvoiceStatus | nvarchar(20) | 否 | 否 | 开票状态 |
| InvoiceDate | datetime | 是 | 否 | 开票日期 |
| CreatedAt | datetime | 否 | 否 | 创建时间 |
| UpdatedAt | datetime | 否 | 否 | 更新时间 |
| IsDeleted | bit | 否 | 否 | 是否删除 |

### 发票管理

#### 发票表 (Invoices)

| 字段名 | 数据类型 | 允许空 | 主键 | 说明 |
|--------|----------|--------|------|------|
| Id | int | 否 | 是 | 发票ID，自增 |
| PaymentId | int | 否 | 否 | 关联款项ID，外键 |
| Company | nvarchar(50) | 否 | 否 | 发票公司 |
| Type | nvarchar(50) | 否 | 否 | 发票类型 |
| TaxRate | decimal(5,2) | 否 | 否 | 发票税率 |
| Amount | decimal(18,2) | 否 | 否 | 发票金额 |
| Status | nvarchar(20) | 否 | 否 | 发票状态（Normal-正常，Cancelled-已作废） |
| Content | nvarchar(200) | 是 | 否 | 发票内容 |
| Code | nvarchar(50) | 是 | 否 | 发票代号 |
| ReceiverName | nvarchar(50) | 是 | 否 | 收件人 |
| ReceiverPhone | nvarchar(20) | 是 | 否 | 收件人电话 |
| MailingAddress | nvarchar(200) | 是 | 否 | 发票邮寄地址 |
| ApplicantPhone | nvarchar(20) | 是 | 否 | 申请人电话 |
| CustomerEmail | nvarchar(100) | 是 | 否 | 客户电子邮件 |
| CreatedById | int | 否 | 否 | 创建人ID，外键 |
| CreatedAt | datetime | 否 | 否 | 创建时间 |
| UpdatedAt | datetime | 否 | 否 | 更新时间 |
| IsDeleted | bit | 否 | 否 | 是否删除 |

## 索引设计

- 所有表的主键自动创建聚集索引
- 外键字段创建非聚集索引
- 常用查询条件字段创建非聚集索引

## 约束设计

- 主键约束：确保每个表的记录唯一性
- 外键约束：确保数据完整性和一致性
- 非空约束：确保必填字段有值
- 默认值约束：为某些字段提供默认值
- 检查约束：确保数据符合业务规则

## 数据库初始化

系统将提供初始化脚本，包括：
- 创建数据库
- 创建表结构
- 创建约束和索引
- 插入基础数据（如权限数据）
