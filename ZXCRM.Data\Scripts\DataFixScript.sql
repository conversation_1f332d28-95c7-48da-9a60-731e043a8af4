-- =============================================
-- 数据修复脚本 - 修复显示问题
-- 创建时间: 2024年12月
-- 描述: 检查和修复数据库中的数据显示问题
-- =============================================

-- 1. 检查当前数据状态
PRINT '=== 数据状态检查 ===';

-- 检查部门数据
PRINT '1. 部门数据检查:';
SELECT
    Id, Name, Code, Status,
    CASE WHEN Status IS NULL OR Status = '' THEN '❌ 状态为空' ELSE '✅ 状态正常' END AS StatusCheck
FROM Departments;

-- 检查用户数据
PRINT '2. 用户数据检查:';
SELECT
    Id, Username, Name, Role, Status, DepartmentId,
    CASE WHEN Role IS NULL OR Role = '' THEN '❌ 角色为空' ELSE '✅ 角色正常' END AS RoleCheck,
    CASE WHEN Status IS NULL OR Status = '' THEN '❌ 状态为空' ELSE '✅ 状态正常' END AS StatusCheck
FROM Users;

-- 检查订单数据
PRINT '3. 订单数据检查:';
SELECT
    Id, Name, Code, Amount, Currency, Status,
    CASE WHEN Amount IS NULL OR Amount = 0 THEN '❌ 金额为空' ELSE '✅ 金额正常' END AS AmountCheck,
    CASE WHEN Currency IS NULL OR Currency = '' THEN '❌ 货币为空' ELSE '✅ 货币正常' END AS CurrencyCheck,
    CASE WHEN Status IS NULL OR Status = '' THEN '❌ 状态为空' ELSE '✅ 状态正常' END AS StatusCheck
FROM Orders;

-- 检查款项数据
PRINT '4. 款项数据检查:';
SELECT
    Id, Name, Code, PaymentType, Amount, Currency, PaymentStatus, InvoiceStatus,
    CASE WHEN PaymentType IS NULL OR PaymentType = '' THEN '❌ 类型为空' ELSE '✅ 类型正常' END AS TypeCheck,
    CASE WHEN Amount IS NULL OR Amount = 0 THEN '❌ 金额为空' ELSE '✅ 金额正常' END AS AmountCheck,
    CASE WHEN PaymentStatus IS NULL OR PaymentStatus = '' THEN '❌ 回款状态为空' ELSE '✅ 回款状态正常' END AS PaymentStatusCheck,
    CASE WHEN InvoiceStatus IS NULL OR InvoiceStatus = '' THEN '❌ 开票状态为空' ELSE '✅ 开票状态正常' END AS InvoiceStatusCheck
FROM Payments;

-- 检查发票数据
PRINT '5. 发票数据检查:';
SELECT
    Id, Company, Type, TaxRate, Amount, Status,
    CASE WHEN Company IS NULL OR Company = '' THEN '❌ 公司为空' ELSE '✅ 公司正常' END AS CompanyCheck,
    CASE WHEN Type IS NULL OR Type = '' THEN '❌ 类型为空' ELSE '✅ 类型正常' END AS TypeCheck,
    CASE WHEN TaxRate IS NULL THEN '❌ 税率为空' ELSE '✅ 税率正常' END AS TaxRateCheck,
    CASE WHEN Amount IS NULL OR Amount = 0 THEN '❌ 金额为空' ELSE '✅ 金额正常' END AS AmountCheck,
    CASE WHEN Status IS NULL OR Status = '' THEN '❌ 状态为空' ELSE '✅ 状态正常' END AS StatusCheck
FROM Invoices;

-- 2. 数据修复
PRINT '=== 开始数据修复 ===';

BEGIN TRANSACTION;

-- 修复部门状态
UPDATE Departments
SET Status = 'Active'
WHERE Status IS NULL OR Status = '';
PRINT '✅ 部门状态修复完成';

-- 修复用户角色和状态
UPDATE Users
SET Role = CASE
    WHEN Username = 'admin' THEN 'Admin'
    WHEN Username LIKE '%manager%' OR Username LIKE '%经理%' THEN 'Manager'
    WHEN Username LIKE '%supervisor%' OR Username LIKE '%主管%' THEN 'Supervisor'
    ELSE 'Employee'
END
WHERE Role IS NULL OR Role = '';

UPDATE Users
SET Status = 'Active'
WHERE Status IS NULL OR Status = '';
PRINT '✅ 用户角色和状态修复完成';

-- 修复订单数据
UPDATE Orders
SET Currency = 'CNY'
WHERE Currency IS NULL OR Currency = '';

UPDATE Orders
SET Status = CASE
    WHEN SignDate IS NOT NULL AND SignDate <= GETDATE() THEN 'Confirmed'
    ELSE 'Draft'
END
WHERE Status IS NULL OR Status = '';

-- 为测试数据设置示例金额（如果金额为0或NULL）
UPDATE Orders
SET Amount = 100000.00,
    SettlementAmount = 100000.00
WHERE (Amount IS NULL OR Amount = 0) AND Name IS NOT NULL;
PRINT '✅ 订单数据修复完成';

-- 修复款项数据
UPDATE Payments
SET PaymentType = CASE
    WHEN Name LIKE '%首付%' OR Name LIKE '%定金%' THEN '首付款'
    WHEN Name LIKE '%二期%' OR Name LIKE '%第二%' THEN '二期款'
    WHEN Name LIKE '%三期%' OR Name LIKE '%第三%' THEN '三期款'
    WHEN Name LIKE '%四期%' OR Name LIKE '%第四%' THEN '四期款'
    WHEN Name LIKE '%五期%' OR Name LIKE '%第五%' THEN '五期款'
    WHEN Name LIKE '%尾款%' OR Name LIKE '%最后%' THEN '尾款'
    WHEN Name LIKE '%全款%' THEN '全款'
    ELSE '首付款'
END
WHERE PaymentType IS NULL OR PaymentType = '';

UPDATE Payments
SET Currency = 'CNY'
WHERE Currency IS NULL OR Currency = '';

UPDATE Payments
SET PaymentStatus = CASE
    WHEN ActualPaymentDate IS NOT NULL THEN 'Completed'
    WHEN ExpectedPaymentDate IS NOT NULL AND ExpectedPaymentDate < GETDATE() THEN 'Overdue'
    ELSE 'Pending'
END
WHERE PaymentStatus IS NULL OR PaymentStatus = '';

UPDATE Payments
SET InvoiceStatus = CASE
    WHEN InvoiceDate IS NOT NULL THEN '已开票'
    ELSE '未开票'
END
WHERE InvoiceStatus IS NULL OR InvoiceStatus = '';

-- 为测试数据设置示例金额
UPDATE Payments
SET Amount = 50000.00,
    SettlementAmount = 50000.00
WHERE (Amount IS NULL OR Amount = 0) AND Name IS NOT NULL;
PRINT '✅ 款项数据修复完成';

-- 修复发票数据
UPDATE Invoices
SET Company = CASE
    WHEN Company IS NULL OR Company = '' THEN '示例公司A'
    ELSE Company
END;

UPDATE Invoices
SET Type = CASE
    WHEN Type IS NULL OR Type = '' THEN '增值税专用发票'
    ELSE Type
END;

UPDATE Invoices
SET TaxRate = CASE
    WHEN TaxRate IS NULL THEN 0.13
    ELSE TaxRate
END;

UPDATE Invoices
SET Status = CASE
    WHEN Status IS NULL OR Status = '' THEN 'Issued'
    ELSE Status
END;

-- 为测试数据设置示例金额
UPDATE Invoices
SET Amount = 50000.00
WHERE (Amount IS NULL OR Amount = 0) AND PaymentId IS NOT NULL;
PRINT '✅ 发票数据修复完成';

COMMIT TRANSACTION;

-- 3. 验证修复结果
PRINT '=== 修复结果验证 ===';

PRINT '1. 部门数据验证:';
SELECT COUNT(*) AS TotalDepartments,
       SUM(CASE WHEN Status IS NOT NULL AND Status != '' THEN 1 ELSE 0 END) AS ValidStatus
FROM Departments;

PRINT '2. 用户数据验证:';
SELECT COUNT(*) AS TotalUsers,
       SUM(CASE WHEN Role IS NOT NULL AND Role != '' THEN 1 ELSE 0 END) AS ValidRole,
       SUM(CASE WHEN Status IS NOT NULL AND Status != '' THEN 1 ELSE 0 END) AS ValidStatus
FROM Users;

PRINT '3. 订单数据验证:';
SELECT COUNT(*) AS TotalOrders,
       SUM(CASE WHEN Amount > 0 THEN 1 ELSE 0 END) AS ValidAmount,
       SUM(CASE WHEN Currency IS NOT NULL AND Currency != '' THEN 1 ELSE 0 END) AS ValidCurrency,
       SUM(CASE WHEN Status IS NOT NULL AND Status != '' THEN 1 ELSE 0 END) AS ValidStatus
FROM Orders;

PRINT '4. 款项数据验证:';
SELECT COUNT(*) AS TotalPayments,
       SUM(CASE WHEN PaymentType IS NOT NULL AND PaymentType != '' THEN 1 ELSE 0 END) AS ValidType,
       SUM(CASE WHEN Amount > 0 THEN 1 ELSE 0 END) AS ValidAmount,
       SUM(CASE WHEN PaymentStatus IS NOT NULL AND PaymentStatus != '' THEN 1 ELSE 0 END) AS ValidPaymentStatus,
       SUM(CASE WHEN InvoiceStatus IS NOT NULL AND InvoiceStatus != '' THEN 1 ELSE 0 END) AS ValidInvoiceStatus
FROM Payments;

PRINT '5. 发票数据验证:';
SELECT COUNT(*) AS TotalInvoices,
       SUM(CASE WHEN Company IS NOT NULL AND Company != '' THEN 1 ELSE 0 END) AS ValidCompany,
       SUM(CASE WHEN Type IS NOT NULL AND Type != '' THEN 1 ELSE 0 END) AS ValidType,
       SUM(CASE WHEN TaxRate IS NOT NULL THEN 1 ELSE 0 END) AS ValidTaxRate,
       SUM(CASE WHEN Amount > 0 THEN 1 ELSE 0 END) AS ValidAmount,
       SUM(CASE WHEN Status IS NOT NULL AND Status != '' THEN 1 ELSE 0 END) AS ValidStatus
FROM Invoices;

PRINT '🎉 数据修复脚本执行完成！';
