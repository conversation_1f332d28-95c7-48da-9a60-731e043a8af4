using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ZXCRM.Service.DTOs;
using ZXCRM.Service.Interface;

namespace ZXCRM.WebAPI.Controllers
{
    [Authorize]
    public class DepartmentController : BaseController
    {
        private readonly IDepartmentService _departmentService;

        public DepartmentController(IDepartmentService departmentService)
        {
            _departmentService = departmentService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAllDepartments()
        {
            try
            {
                var departments = await _departmentService.GetAllDepartmentsAsync();
                return Success(departments);
            }
            catch (Exception ex)
            {
                return Failure(ex.Message);
            }
        }

        [HttpGet("tree")]
        public async Task<IActionResult> GetDepartmentTree()
        {
            try
            {
                var departmentTree = await _departmentService.GetDepartmentTreeAsync();
                return Success(departmentTree);
            }
            catch (Exception ex)
            {
                return Failure(ex.Message);
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetDepartmentById(int id)
        {
            try
            {
                var department = await _departmentService.GetDepartmentByIdAsync(id);
                if (department == null)
                {
                    return Failure("部门不存在");
                }
                return Success(department);
            }
            catch (Exception ex)
            {
                return Failure(ex.Message);
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreateDepartment([FromBody] CreateDepartmentDTO createDepartmentDto)
        {
            try
            {
                var department = await _departmentService.CreateDepartmentAsync(createDepartmentDto);
                return Success(department, "创建部门成功");
            }
            catch (Exception ex)
            {
                return Failure(ex.Message);
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateDepartment(int id, [FromBody] UpdateDepartmentDTO updateDepartmentDto)
        {
            try
            {
                if (id != updateDepartmentDto.Id)
                {
                    return Failure("部门ID不匹配");
                }

                var department = await _departmentService.UpdateDepartmentAsync(updateDepartmentDto);
                if (department == null)
                {
                    return Failure("部门不存在");
                }
                return Success(department, "更新部门成功");
            }
            catch (Exception ex)
            {
                return Failure(ex.Message);
            }
        }

        [HttpGet("{id}/users")]
        public async Task<IActionResult> GetDepartmentUsers(int id)
        {
            try
            {
                var users = await _departmentService.GetDepartmentUsersAsync(id);
                return Success(users);
            }
            catch (Exception ex)
            {
                return Failure(ex.Message);
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteDepartment(int id)
        {
            try
            {
                var result = await _departmentService.DeleteDepartmentAsync(id);
                if (!result)
                {
                    return Failure("删除部门失败");
                }
                return Success(null, "删除部门成功");
            }
            catch (Exception ex)
            {
                return Failure(ex.Message);
            }
        }
    }
}
