@model ZXCRM.WebUI.Models.ViewModels.EditUserViewModel
@{
    ViewData["Title"] = "编辑用户";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">编辑用户</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a asp-controller="Dashboard" asp-action="Index">首页</a></li>
                    <li class="breadcrumb-item"><a asp-controller="User" asp-action="Index">用户管理</a></li>
                    <li class="breadcrumb-item active">编辑用户</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-user-edit"></i> 编辑用户信息
                        </h3>
                        <div class="card-tools">
                            <span class="badge badge-info">用户ID: @Model.Id</span>
                        </div>
                    </div>
                    <form asp-action="Edit" method="post">
                        <input asp-for="Id" type="hidden" />
                        
                        <div class="card-body">
                            <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <!-- 用户名 (只读) -->
                                    <div class="form-group">
                                        <label asp-for="Username" class="form-label"></label>
                                        <input asp-for="Username" class="form-control" readonly />
                                        <small class="form-text text-muted">用户名不可修改</small>
                                    </div>

                                    <!-- 姓名 -->
                                    <div class="form-group">
                                        <label asp-for="Name" class="form-label required"></label>
                                        <input asp-for="Name" class="form-control" placeholder="请输入真实姓名" />
                                        <span asp-validation-for="Name" class="text-danger"></span>
                                    </div>

                                    <!-- 性别 -->
                                    <div class="form-group">
                                        <label asp-for="Gender" class="form-label"></label>
                                        <select asp-for="Gender" class="form-control">
                                            <option value="">请选择性别</option>
                                            <option value="男">男</option>
                                            <option value="女">女</option>
                                        </select>
                                        <span asp-validation-for="Gender" class="text-danger"></span>
                                    </div>

                                    <!-- 邮箱 -->
                                    <div class="form-group">
                                        <label asp-for="Email" class="form-label"></label>
                                        <input asp-for="Email" class="form-control" placeholder="请输入邮箱地址" />
                                        <span asp-validation-for="Email" class="text-danger"></span>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <!-- 电话 -->
                                    <div class="form-group">
                                        <label asp-for="Phone" class="form-label"></label>
                                        <input asp-for="Phone" class="form-control" placeholder="请输入手机号码" />
                                        <span asp-validation-for="Phone" class="text-danger"></span>
                                    </div>

                                    <!-- 部门 -->
                                    <div class="form-group">
                                        <label asp-for="DepartmentId" class="form-label required"></label>
                                        <select asp-for="DepartmentId" class="form-control">
                                            <option value="">请选择部门</option>
                                            @foreach (var dept in Model.Departments)
                                            {
                                                <option value="@dept.Id" selected="@(dept.Id == Model.DepartmentId)">@dept.Name</option>
                                            }
                                        </select>
                                        <span asp-validation-for="DepartmentId" class="text-danger"></span>
                                    </div>

                                    <!-- 角色 -->
                                    <div class="form-group">
                                        <label asp-for="Role" class="form-label required"></label>
                                        <select asp-for="Role" class="form-control">
                                            <option value="">请选择角色</option>
                                            @foreach (var role in Model.Roles)
                                            {
                                                <option value="@role.Value" selected="@(role.Value == Model.Role)">@role.Text</option>
                                            }
                                        </select>
                                        <span asp-validation-for="Role" class="text-danger"></span>
                                    </div>

                                    <!-- 状态 -->
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                                            <label asp-for="IsActive" class="form-check-label"></label>
                                        </div>
                                        <span asp-validation-for="IsActive" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <!-- 密码修改区域 -->
                            <div class="row">
                                <div class="col-12">
                                    <h5>
                                        <i class="fas fa-key"></i> 密码修改
                                        <small class="text-muted">(如不需要修改密码，请留空)</small>
                                    </h5>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <!-- 新密码 -->
                                    <div class="form-group">
                                        <label asp-for="NewPassword" class="form-label"></label>
                                        <div class="input-group">
                                            <input asp-for="NewPassword" class="form-control" type="password" placeholder="如需修改密码请输入新密码" />
                                            <div class="input-group-append">
                                                <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('NewPassword')">
                                                    <i class="fas fa-eye" id="NewPassword-eye"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <span asp-validation-for="NewPassword" class="text-danger"></span>
                                        <small class="form-text text-muted">留空表示不修改密码</small>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <!-- 确认新密码 -->
                                    <div class="form-group">
                                        <label asp-for="ConfirmNewPassword" class="form-label"></label>
                                        <div class="input-group">
                                            <input asp-for="ConfirmNewPassword" class="form-control" type="password" placeholder="请再次输入新密码" />
                                            <div class="input-group-append">
                                                <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('ConfirmNewPassword')">
                                                    <i class="fas fa-eye" id="ConfirmNewPassword-eye"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <span asp-validation-for="ConfirmNewPassword" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card-footer">
                            <div class="row">
                                <div class="col-md-6">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> 保存修改
                                    </button>
                                    <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info ml-2">
                                        <i class="fas fa-eye"></i> 查看详情
                                    </a>
                                    <a asp-action="Index" class="btn btn-secondary ml-2">
                                        <i class="fas fa-times"></i> 取消
                                    </a>
                                </div>
                                <div class="col-md-6 text-right">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i> 
                                        标有 <span class="text-danger">*</span> 的字段为必填项
                                    </small>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="col-md-4">
                <!-- 操作历史 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-history"></i> 操作历史
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <div class="time-label">
                                <span class="bg-info">最近操作</span>
                            </div>
                            <div>
                                <i class="fas fa-user-edit bg-warning"></i>
                                <div class="timeline-item">
                                    <span class="time">
                                        <i class="fas fa-clock"></i> 正在编辑
                                    </span>
                                    <h3 class="timeline-header">编辑用户信息</h3>
                                    <div class="timeline-body">
                                        当前正在编辑用户 <strong>@Model.Username</strong> 的信息
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 帮助信息 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-question-circle"></i> 编辑说明
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb"></i> 提示</h6>
                            <ul class="mb-0">
                                <li>用户名创建后不可修改</li>
                                <li>如不需要修改密码，请将密码字段留空</li>
                                <li>修改部门可能影响用户的数据访问权限</li>
                                <li>禁用用户将阻止其登录系统</li>
                            </ul>
                        </div>

                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> 注意</h6>
                            <p class="mb-0">
                                修改用户信息后，用户可能需要重新登录才能看到最新的权限变更。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        function togglePassword(fieldId) {
            const field = document.getElementById(fieldId);
            const eye = document.getElementById(fieldId + '-eye');
            
            if (field.type === 'password') {
                field.type = 'text';
                eye.classList.remove('fa-eye');
                eye.classList.add('fa-eye-slash');
            } else {
                field.type = 'password';
                eye.classList.remove('fa-eye-slash');
                eye.classList.add('fa-eye');
            }
        }

        // 密码确认验证
        $(document).ready(function() {
            $('#ConfirmNewPassword').on('input', function() {
                const newPassword = $('#NewPassword').val();
                const confirmPassword = $(this).val();
                
                if (newPassword !== confirmPassword && confirmPassword.length > 0) {
                    $(this).addClass('is-invalid');
                    $(this).next('.text-danger').text('两次输入的密码不一致');
                } else {
                    $(this).removeClass('is-invalid');
                    $(this).next('.text-danger').text('');
                }
            });

            // 新密码输入时清空确认密码
            $('#NewPassword').on('input', function() {
                $('#ConfirmNewPassword').val('').removeClass('is-invalid');
            });
        });
    </script>
}

@section Styles {
    <style>
        .required::after {
            content: " *";
            color: red;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .is-invalid {
            border-color: #dc3545;
        }
        
        .timeline {
            position: relative;
            margin: 0 0 30px 0;
            padding: 0;
            list-style: none;
        }
        
        .timeline:before {
            content: '';
            position: absolute;
            top: 0;
            left: 25px;
            height: 100%;
            width: 2px;
            background: #dee2e6;
        }
        
        .timeline > div {
            position: relative;
            margin-right: 10px;
            margin-bottom: 15px;
        }
        
        .timeline > div > .timeline-item {
            box-shadow: 0 1px 1px rgba(0,0,0,0.1);
            border-radius: 3px;
            margin-top: 0;
            background: #fff;
            color: #444;
            margin-left: 60px;
            margin-right: 15px;
            padding: 0;
            position: relative;
        }
        
        .timeline > div > .fas {
            width: 50px;
            height: 50px;
            font-size: 15px;
            line-height: 50px;
            position: absolute;
            color: #666;
            background: #f4f4f4;
            border-radius: 50%;
            text-align: center;
            left: 0;
            top: 0;
        }
    </style>
}
