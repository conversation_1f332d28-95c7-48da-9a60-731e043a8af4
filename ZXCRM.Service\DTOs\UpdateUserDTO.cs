using System.ComponentModel.DataAnnotations;

namespace ZXCRM.Service.DTOs
{
    public class UpdateUserDTO
    {
        [Required(ErrorMessage = "用户ID不能为空")]
        public int Id { get; set; }

        [StringLength(50, ErrorMessage = "姓名长度不能超过50个字符")]
        public string? Name { get; set; }

        public string? Gender { get; set; }
        public string? Email { get; set; }
        public string? Phone { get; set; }
        public int? DepartmentId { get; set; }

        [StringLength(100, MinimumLength = 6, ErrorMessage = "密码长度必须在6-100个字符之间")]
        public string? Password { get; set; }
    }
}
