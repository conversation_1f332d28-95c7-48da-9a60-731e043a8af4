using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using ZXCRM.WebUI.Services;
using ZXCRM.WebUI.Middleware;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllersWithViews();

// Add Razor runtime compilation for development
if (builder.Environment.IsDevelopment())
{
    builder.Services.AddRazorPages().AddRazorRuntimeCompilation();
}

// Configure HttpClient for API calls
builder.Services.AddHttpClient("ZXCRM_API", client =>
{
    client.BaseAddress = new Uri(builder.Configuration.GetValue<string>("ApiSettings:BaseUrl") ?? "http://localhost:5000/");
    client.DefaultRequestHeaders.Add("Accept", "application/json");
});

// Register services
builder.Services.AddScoped<IApiService, ApiService>();
builder.Services.AddScoped<IAuthService, AuthService>();
builder.Services.AddScoped<IDashboardService, DashboardService>();
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddScoped<IDepartmentService, DepartmentService>();
builder.Services.AddScoped<IOrderService, OrderService>();
builder.Services.AddScoped<IPaymentService, PaymentService>();
builder.Services.AddScoped<IInvoiceService, InvoiceService>();
builder.Services.AddScoped<IOpportunityService, OpportunityService>();
builder.Services.AddScoped<ZXCRM.WebUI.Services.IReportService, ZXCRM.WebUI.Services.ReportService>();
builder.Services.AddScoped<ZXCRM.WebUI.Services.IPermissionService, ZXCRM.WebUI.Services.PermissionService>();
builder.Services.AddScoped<ZXCRM.WebUI.Services.IUserPermissionService, ZXCRM.WebUI.Services.UserPermissionService>();
builder.Services.AddScoped<INavigationPermissionService, NavigationPermissionService>();
builder.Services.AddSingleton<IApiPerformanceService, ApiPerformanceService>();

// 注册HttpContextAccessor
builder.Services.AddHttpContextAccessor();

// 注册权限检查服务（通过API调用）
builder.Services.AddHttpClient<IPermissionCheckService, PermissionCheckApiService>(client =>
{
    client.BaseAddress = new Uri("http://localhost:5000/"); // WebAPI地址
});
builder.Services.AddScoped<IPermissionCheckService, PermissionCheckApiService>();

// Configure Authentication
builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
    .AddCookie(options =>
    {
        options.LoginPath = "/Account/Login";
        options.LogoutPath = "/Account/Logout";
        options.AccessDeniedPath = "/Account/AccessDenied";
        options.ExpireTimeSpan = TimeSpan.FromHours(8);
        options.SlidingExpiration = true;
        options.Cookie.HttpOnly = true;
        options.Cookie.SecurePolicy = CookieSecurePolicy.SameAsRequest;
    });

// Configure Authorization
builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("AdminOnly", policy => policy.RequireRole("Admin"));
    options.AddPolicy("UserOrAdmin", policy => policy.RequireRole("User", "Admin"));

    // 设置默认授权策略，要求所有用户必须登录
    options.FallbackPolicy = new AuthorizationPolicyBuilder()
        .RequireAuthenticatedUser()
        .Build();
});

// Configure Session
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromMinutes(30);
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

app.UseSession();
app.UseAuthentication();
app.UseTokenMiddleware();
app.UseAuthorization();

// 使用权限检查中间件
app.UsePermissionCheck();

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Account}/{action=Login}/{id?}");

app.Run();
