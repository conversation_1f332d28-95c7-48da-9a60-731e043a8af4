using AutoMapper;
using ZXCRM.Data.Entities;
using ZXCRM.Data.UnitOfWork;
using ZXCRM.Service.DTOs;
using ZXCRM.Service.Interface;

namespace ZXCRM.Service.Service
{
    public class OpportunityService : IOpportunityService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public OpportunityService(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task<IEnumerable<OpportunityDTO>> GetAllOpportunitiesAsync()
        {
            var opportunities = await _unitOfWork.Opportunities.GetAllWithIncludesAsync(
                o => o.CreatedBy
            );
            return _mapper.Map<IEnumerable<OpportunityDTO>>(opportunities);
        }

        public async Task<IEnumerable<OpportunityDTO>> GetOpportunitiesByUserIdAsync(int userId)
        {
            var opportunities = await _unitOfWork.Opportunities.FindWithIncludesAsync(
                o => o.CreatedById == userId,
                o => o.CreatedBy
            );
            return _mapper.Map<IEnumerable<OpportunityDTO>>(opportunities);
        }

        public async Task<OpportunityDTO?> GetOpportunityByIdAsync(int id)
        {
            var opportunity = await _unitOfWork.Opportunities.GetByIdWithIncludesAsync(id,
                o => o.CreatedBy
            );
            return opportunity != null ? _mapper.Map<OpportunityDTO>(opportunity) : null;
        }

        public async Task<OpportunityDTO> CreateOpportunityAsync(CreateOpportunityDTO createOpportunityDto)
        {
            // 检查用户是否存在
            var user = await _unitOfWork.Users.GetByIdAsync(createOpportunityDto.CreatedById);
            if (user == null)
            {
                throw new Exception("创建人不存在");
            }

            // 创建商机
            var opportunity = _mapper.Map<Opportunity>(createOpportunityDto);
            opportunity.CreatedAt = DateTime.Now;
            opportunity.UpdatedAt = DateTime.Now;

            await _unitOfWork.Opportunities.AddAsync(opportunity);
            await _unitOfWork.SaveChangesAsync();

            // 重新获取包含关联数据的商机
            var createdOpportunity = await _unitOfWork.Opportunities.GetByIdWithIncludesAsync(opportunity.Id,
                o => o.CreatedBy
            );

            return _mapper.Map<OpportunityDTO>(createdOpportunity);
        }

        public async Task<OpportunityDTO?> UpdateOpportunityAsync(UpdateOpportunityDTO updateOpportunityDto)
        {
            var opportunity = await _unitOfWork.Opportunities.GetByIdAsync(updateOpportunityDto.Id);
            if (opportunity == null)
            {
                return null;
            }

            // 更新商机信息
            opportunity.Name = updateOpportunityDto.Name;
            opportunity.CustomerName = updateOpportunityDto.CustomerName;
            opportunity.ContactName = updateOpportunityDto.ContactName;
            opportunity.ContactPhone = updateOpportunityDto.ContactPhone;
            opportunity.Content = updateOpportunityDto.Content;
            opportunity.Status = updateOpportunityDto.Status;
            opportunity.UpdatedAt = DateTime.Now;

            await _unitOfWork.Opportunities.UpdateAsync(opportunity);
            await _unitOfWork.SaveChangesAsync();

            // 重新获取包含关联数据的商机
            var updatedOpportunity = await _unitOfWork.Opportunities.GetByIdWithIncludesAsync(opportunity.Id,
                o => o.CreatedBy
            );

            return _mapper.Map<OpportunityDTO>(updatedOpportunity);
        }

        public async Task<bool> DeleteOpportunityAsync(int id)
        {
            var opportunity = await _unitOfWork.Opportunities.GetByIdAsync(id);
            if (opportunity == null)
            {
                return false;
            }

            // 检查是否有关联的订单
            var orders = await _unitOfWork.Orders.FindAsync(o => o.OpportunityId == id);
            if (orders.Any())
            {
                throw new Exception("该商机已关联订单，无法删除");
            }

            // 软删除
            opportunity.IsDeleted = true;
            opportunity.UpdatedAt = DateTime.Now;

            await _unitOfWork.Opportunities.UpdateAsync(opportunity);
            await _unitOfWork.SaveChangesAsync();

            return true;
        }
    }
}
