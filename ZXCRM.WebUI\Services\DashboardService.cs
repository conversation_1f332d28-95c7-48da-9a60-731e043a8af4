using ZXCRM.WebUI.Models;
using ZXCRM.WebUI.Models.ViewModels;

namespace ZXCRM.WebUI.Services
{
    public class DashboardService : IDashboardService
    {
        private readonly IApiService _apiService;
        private readonly ILogger<DashboardService> _logger;

        public DashboardService(IApiService apiService, ILogger<DashboardService> logger)
        {
            _apiService = apiService;
            _logger = logger;
        }

        public async Task<ApiResponse<DashboardSummaryViewModel>> GetDashboardSummaryAsync()
        {
            try
            {
                _logger.LogInformation("Getting dashboard summary");

                var response = await _apiService.GetAsync<DashboardSummaryViewModel>("api/dashboard/summary");

                if (response.Success)
                {
                    _logger.LogInformation("Successfully retrieved dashboard summary");
                }
                else
                {
                    _logger.LogWarning("Failed to retrieve dashboard summary: {Message}", response.Message);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dashboard summary");
                return new ApiResponse<DashboardSummaryViewModel>
                {
                    Success = false,
                    Message = $"Failed to get dashboard summary: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<List<RecentActivityViewModel>>> GetRecentActivitiesAsync()
        {
            try
            {
                _logger.LogInformation("Getting recent activities");

                var response = await _apiService.GetAsync<List<RecentActivityViewModel>>("api/dashboard/recent-activities");

                if (response.Success)
                {
                    _logger.LogInformation("Successfully retrieved recent activities");
                }
                else
                {
                    _logger.LogWarning("Failed to retrieve recent activities: {Message}", response.Message);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recent activities");
                return new ApiResponse<List<RecentActivityViewModel>>
                {
                    Success = false,
                    Message = $"Failed to get recent activities: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<ChartDataViewModel>> GetChartDataAsync(string chartType)
        {
            try
            {
                _logger.LogInformation("Getting chart data for type: {ChartType}", chartType);

                var response = await _apiService.GetAsync<ChartDataViewModel>($"api/dashboard/chart-data/{chartType}");

                if (response.Success)
                {
                    _logger.LogInformation("Successfully retrieved chart data for type: {ChartType}", chartType);
                }
                else
                {
                    _logger.LogWarning("Failed to retrieve chart data for type: {ChartType}. Message: {Message}",
                        chartType, response.Message);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting chart data for type: {ChartType}", chartType);
                return new ApiResponse<ChartDataViewModel>
                {
                    Success = false,
                    Message = $"Failed to get chart data: {ex.Message}"
                };
            }
        }
    }
}
