@model ZXCRM.WebUI.Controllers.SystemHealthInfo
@{
    ViewData["Title"] = "系统健康状态";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">系统健康状态</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a asp-controller="Dashboard" asp-action="Index">首页</a></li>
                    <li class="breadcrumb-item active">系统健康</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <!-- 系统状态概览 -->
        <div class="row">
            <div class="col-md-3">
                <div class="info-box">
                    <span class="info-box-icon bg-@(Model.Status == "Healthy" ? "success" : "danger")">
                        <i class="fas fa-@(Model.Status == "Healthy" ? "check-circle" : "exclamation-triangle")"></i>
                    </span>
                    <div class="info-box-content">
                        <span class="info-box-text">系统状态</span>
                        <span class="info-box-number">@Model.Status</span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="info-box">
                    <span class="info-box-icon bg-info">
                        <i class="fas fa-clock"></i>
                    </span>
                    <div class="info-box-content">
                        <span class="info-box-text">运行时间</span>
                        <span class="info-box-number">@Model.UpTimeFormatted</span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="info-box">
                    <span class="info-box-icon bg-warning">
                        <i class="fas fa-memory"></i>
                    </span>
                    <div class="info-box-content">
                        <span class="info-box-text">内存使用</span>
                        <span class="info-box-number">@Model.WorkingSetMB MB</span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="info-box">
                    <span class="info-box-icon bg-primary">
                        <i class="fas fa-microchip"></i>
                    </span>
                    <div class="info-box-content">
                        <span class="info-box-text">CPU核心数</span>
                        <span class="info-box-number">@Model.ProcessorCount</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 应用程序信息 -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-info-circle"></i> 应用程序信息
                        </h3>
                    </div>
                    <div class="card-body">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>应用名称:</strong></td>
                                <td>@Model.ApplicationName</td>
                            </tr>
                            <tr>
                                <td><strong>版本:</strong></td>
                                <td>@Model.Version</td>
                            </tr>
                            <tr>
                                <td><strong>环境:</strong></td>
                                <td>
                                    <span class="badge badge-@(Model.Environment == "Production" ? "success" : Model.Environment == "Development" ? "warning" : "info")">
                                        @Model.Environment
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>服务器:</strong></td>
                                <td>@Model.MachineName</td>
                            </tr>
                            <tr>
                                <td><strong>启动时间:</strong></td>
                                <td>@Model.StartTime.ToString("yyyy-MM-dd HH:mm:ss")</td>
                            </tr>
                            <tr>
                                <td><strong>检查时间:</strong></td>
                                <td>@Model.Timestamp.ToString("yyyy-MM-dd HH:mm:ss")</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <!-- API性能统计 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-line"></i> API性能统计
                        </h3>
                    </div>
                    <div class="card-body">
                        @if (Model.PerformanceStats != null)
                        {
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>24小时调用:</strong></td>
                                    <td>@Model.PerformanceStats.TotalCalls24h 次</td>
                                </tr>
                                <tr>
                                    <td><strong>24小时成功率:</strong></td>
                                    <td>
                                        <span class="badge badge-@(Model.PerformanceStats.SuccessRate24h >= 95 ? "success" : Model.PerformanceStats.SuccessRate24h >= 90 ? "warning" : "danger")">
                                            @Model.PerformanceStats.SuccessRate24h.ToString("F1")%
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>24小时平均响应:</strong></td>
                                    <td>@Model.PerformanceStats.AverageResponseTime24h.ToString("F0") ms</td>
                                </tr>
                                <tr>
                                    <td><strong>最近1小时调用:</strong></td>
                                    <td>@Model.PerformanceStats.TotalCallsLastHour 次</td>
                                </tr>
                                <tr>
                                    <td><strong>最近1小时成功率:</strong></td>
                                    <td>
                                        <span class="badge badge-@(Model.PerformanceStats.SuccessRateLastHour >= 95 ? "success" : Model.PerformanceStats.SuccessRateLastHour >= 90 ? "warning" : "danger")">
                                            @Model.PerformanceStats.SuccessRateLastHour.ToString("F1")%
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>最近1小时平均响应:</strong></td>
                                    <td>@Model.PerformanceStats.AverageResponseTimeLastHour.ToString("F0") ms</td>
                                </tr>
                            </table>
                        }
                        else
                        {
                            <p class="text-muted">暂无性能统计数据</p>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- 错误信息 -->
        @if (!string.IsNullOrEmpty(Model.ErrorMessage))
        {
            <div class="row">
                <div class="col-md-12">
                    <div class="alert alert-danger">
                        <h5><i class="icon fas fa-ban"></i> 系统错误</h5>
                        @Model.ErrorMessage
                    </div>
                </div>
            </div>
        }

        <!-- 操作按钮 -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-tools"></i> 系统操作
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="btn-group" role="group">
                            <a asp-action="Performance" class="btn btn-info">
                                <i class="fas fa-chart-bar"></i> 性能详情
                            </a>
                            <a asp-action="Info" class="btn btn-secondary">
                                <i class="fas fa-info"></i> 系统信息
                            </a>
                            <a asp-action="Logs" class="btn btn-warning">
                                <i class="fas fa-file-alt"></i> 系统日志
                            </a>
                            <button type="button" class="btn btn-danger" onclick="clearCache()">
                                <i class="fas fa-trash"></i> 清理缓存
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最慢端点 -->
        @if (Model.PerformanceStats?.SlowestEndpoints.Any() == true)
        {
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-turtle"></i> 最慢API端点
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>端点</th>
                                            <th>平均响应时间</th>
                                            <th>调用次数</th>
                                            <th>成功率</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var endpoint in Model.PerformanceStats.SlowestEndpoints)
                                        {
                                            <tr>
                                                <td><code>@endpoint.Endpoint</code></td>
                                                <td>@endpoint.AverageResponseTime.ToString("F0") ms</td>
                                                <td>@endpoint.CallCount</td>
                                                <td>
                                                    <span class="badge badge-@(endpoint.SuccessRate >= 95 ? "success" : endpoint.SuccessRate >= 90 ? "warning" : "danger")">
                                                        @endpoint.SuccessRate.ToString("F1")%
                                                    </span>
                                                </td>
                                                <td>
                                                    @if (endpoint.AverageResponseTime > 5000)
                                                    {
                                                        <span class="badge badge-danger">很慢</span>
                                                    }
                                                    else if (endpoint.AverageResponseTime > 3000)
                                                    {
                                                        <span class="badge badge-warning">较慢</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge badge-success">正常</span>
                                                    }
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>
</section>

@section Scripts {
    <script>
        function clearCache() {
            if (confirm('确定要清理系统缓存吗？')) {
                // 创建表单并提交
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '@Url.Action("ClearCache")';
                
                // 添加防伪令牌
                const token = document.createElement('input');
                token.type = 'hidden';
                token.name = '__RequestVerificationToken';
                token.value = $('input[name="__RequestVerificationToken"]').val();
                form.appendChild(token);
                
                document.body.appendChild(form);
                form.submit();
            }
        }

        // 自动刷新页面（每30秒）
        setTimeout(function() {
            location.reload();
        }, 30000);

        // 显示提示消息
        @if (TempData["SuccessMessage"] != null)
        {
            <text>
            toastr.success('@TempData["SuccessMessage"]');
            </text>
        }

        @if (TempData["ErrorMessage"] != null)
        {
            <text>
            toastr.error('@TempData["ErrorMessage"]');
            </text>
        }
    </script>
}

@Html.AntiForgeryToken()
