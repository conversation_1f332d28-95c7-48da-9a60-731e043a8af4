{"openapi": "3.0.4", "info": {"title": "ZXCRM.WebAPI", "version": "1.0"}, "paths": {"/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/validate-token": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/Auth/current-user": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/Department": {"get": {"tags": ["Department"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Department"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDepartmentDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateDepartmentDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateDepartmentDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Department/tree": {"get": {"tags": ["Department"], "responses": {"200": {"description": "OK"}}}}, "/api/Department/{id}": {"get": {"tags": ["Department"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Department"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDepartmentDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateDepartmentDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateDepartmentDTO"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Department"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Department/{id}/users": {"get": {"tags": ["Department"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Invoice": {"get": {"tags": ["Invoice"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Invoice"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateInvoiceDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateInvoiceDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateInvoiceDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Invoice/{id}": {"get": {"tags": ["Invoice"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Invoice"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateInvoiceDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateInvoiceDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateInvoiceDTO"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Invoice"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Invoice/payment/{paymentId}": {"get": {"tags": ["Invoice"], "parameters": [{"name": "paymentId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Invoice/payment/{paymentId}/amount": {"get": {"tags": ["Invoice"], "parameters": [{"name": "paymentId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Opportunity": {"get": {"tags": ["Opportunity"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Opportunity"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOpportunityDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateOpportunityDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateOpportunityDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Opportunity/all": {"get": {"tags": ["Opportunity"], "responses": {"200": {"description": "OK"}}}}, "/api/Opportunity/{id}": {"get": {"tags": ["Opportunity"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Opportunity"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOpportunityDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateOpportunityDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateOpportunityDTO"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Opportunity"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Opportunity/user/{userId}": {"get": {"tags": ["Opportunity"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Order": {"get": {"tags": ["Order"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Order"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOrderDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateOrderDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateOrderDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/{id}": {"get": {"tags": ["Order"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Order"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOrderDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateOrderDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateOrderDTO"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Order"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Order/user/{userId}": {"get": {"tags": ["Order"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Payment": {"get": {"tags": ["Payment"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Payment"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePaymentDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreatePaymentDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreatePaymentDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Payment/{id}": {"get": {"tags": ["Payment"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Payment"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePaymentDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdatePaymentDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdatePaymentDTO"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Payment"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Payment/order/{orderId}": {"get": {"tags": ["Payment"], "parameters": [{"name": "orderId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Permission": {"get": {"tags": ["Permission"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Permission"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PermissionDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PermissionDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PermissionDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Permission/{id}": {"get": {"tags": ["Permission"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Permission"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PermissionDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PermissionDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PermissionDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Permission"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Report/dashboard": {"get": {"tags": ["Report"], "parameters": [{"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "DepartmentId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Report/opportunity": {"get": {"tags": ["Report"], "parameters": [{"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "DepartmentId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Report/order": {"get": {"tags": ["Report"], "parameters": [{"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "DepartmentId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Report/payment": {"get": {"tags": ["Report"], "parameters": [{"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "DepartmentId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Report/invoice": {"get": {"tags": ["Report"], "parameters": [{"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "DepartmentId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Report/export/{reportType}": {"get": {"tags": ["Report"], "parameters": [{"name": "reportType", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "DepartmentId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Report/opportunity/all": {"get": {"tags": ["Report"], "parameters": [{"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "DepartmentId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Report/summary": {"get": {"tags": ["Report"], "parameters": [{"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "DepartmentId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Report/trends": {"get": {"tags": ["Report"], "parameters": [{"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "DepartmentId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Test": {"get": {"tags": ["Test"], "responses": {"200": {"description": "OK"}}}}, "/api/TestApi": {"get": {"tags": ["TestApi"], "responses": {"200": {"description": "OK"}}}}, "/api/TestApi/echo": {"post": {"tags": ["TestApi"], "requestBody": {"content": {"application/json": {"schema": {}}, "text/json": {"schema": {}}, "application/*+json": {"schema": {}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/User": {"get": {"tags": ["User"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateUserDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateUserDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/User/{id}": {"get": {"tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserDTO"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/User/check-permission": {"get": {"tags": ["User"], "parameters": [{"name": "moduleType", "in": "query", "schema": {"type": "string"}}, {"name": "permissionCode", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/UserPermission/user/{userId}": {"get": {"tags": ["UserPermission"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["UserPermission"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserPermissionDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserPermissionDto"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserPermissionDto"}}}}}, "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"CreateDepartmentDTO": {"required": ["Name"], "type": "object", "properties": {"Name": {"maxLength": 100, "minLength": 0, "type": "string"}, "Code": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "ParentId": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "CreateInvoiceDTO": {"required": ["Amount", "Company", "PaymentId", "TaxRate", "Type"], "type": "object", "properties": {"PaymentId": {"type": "integer", "format": "int32"}, "Company": {"maxLength": 50, "minLength": 0, "type": "string"}, "Type": {"maxLength": 50, "minLength": 0, "type": "string"}, "TaxRate": {"maximum": 100, "minimum": 0, "type": "number", "format": "double"}, "Amount": {"minimum": 0.01, "type": "number", "format": "double"}, "Status": {"type": "string", "nullable": true}, "Content": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "Code": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "ReceiverName": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "ReceiverPhone": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "MailingAddress": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "ApplicantPhone": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "CustomerEmail": {"maxLength": 100, "minLength": 0, "type": "string", "format": "email", "nullable": true}, "CreatedById": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CreateOpportunityDTO": {"required": ["CustomerName", "Name", "Status"], "type": "object", "properties": {"Name": {"maxLength": 100, "minLength": 0, "type": "string"}, "CustomerName": {"maxLength": 100, "minLength": 0, "type": "string"}, "ContactName": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "ContactPhone": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "Content": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "Status": {"maxLength": 20, "minLength": 0, "type": "string"}, "CreatedById": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CreateOrderDTO": {"required": ["AccountManagerId", "Amount", "Code", "<PERSON><PERSON><PERSON><PERSON>", "CustomerName", "DepartmentId", "Name", "SettlementAmount", "SignDate", "Status"], "type": "object", "properties": {"Name": {"maxLength": 100, "minLength": 0, "type": "string"}, "Code": {"maxLength": 50, "minLength": 0, "type": "string"}, "CustomerName": {"maxLength": 100, "minLength": 0, "type": "string"}, "ContactName": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "ContactPhone": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "OpportunityId": {"type": "integer", "format": "int32", "nullable": true}, "Amount": {"minimum": 0.01, "type": "number", "format": "double"}, "Currency": {"maxLength": 20, "minLength": 0, "type": "string"}, "SettlementAmount": {"minimum": 0.01, "type": "number", "format": "double"}, "AccountManagerId": {"type": "integer", "format": "int32"}, "DepartmentId": {"type": "integer", "format": "int32"}, "SignDate": {"type": "string", "format": "date-time"}, "Status": {"maxLength": 20, "minLength": 0, "type": "string"}, "ProjectManagerId": {"type": "integer", "format": "int32", "nullable": true}, "PerformanceDepartmentId": {"type": "integer", "format": "int32", "nullable": true}, "CreatedById": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CreatePaymentDTO": {"required": ["Amount", "Code", "<PERSON><PERSON><PERSON><PERSON>", "InvoiceStatus", "Name", "OrderId", "PaymentType", "SettlementAmount"], "type": "object", "properties": {"Name": {"maxLength": 100, "minLength": 0, "type": "string"}, "Code": {"maxLength": 50, "minLength": 0, "type": "string"}, "OrderId": {"type": "integer", "format": "int32"}, "PaymentType": {"maxLength": 20, "minLength": 0, "type": "string"}, "Amount": {"minimum": 0.01, "type": "number", "format": "double"}, "Currency": {"maxLength": 20, "minLength": 0, "type": "string"}, "SettlementAmount": {"minimum": 0.01, "type": "number", "format": "double"}, "ExpectedPaymentDate": {"type": "string", "format": "date-time", "nullable": true}, "ActualPaymentDate": {"type": "string", "format": "date-time", "nullable": true}, "InvoiceStatus": {"maxLength": 20, "minLength": 0, "type": "string"}, "InvoiceDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "CreateUserDTO": {"required": ["DepartmentId", "Name", "Password", "Username"], "type": "object", "properties": {"Username": {"maxLength": 50, "minLength": 0, "type": "string"}, "Password": {"maxLength": 100, "minLength": 6, "type": "string"}, "Name": {"maxLength": 50, "minLength": 0, "type": "string"}, "Gender": {"type": "string", "nullable": true}, "Email": {"type": "string", "nullable": true}, "Phone": {"type": "string", "nullable": true}, "DepartmentId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "LoginDTO": {"required": ["Password", "Username"], "type": "object", "properties": {"Username": {"minLength": 1, "type": "string"}, "Password": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "PermissionDto": {"type": "object", "properties": {"Id": {"type": "integer", "format": "int32"}, "Name": {"type": "string", "nullable": true}, "Code": {"type": "string", "nullable": true}, "Description": {"type": "string", "nullable": true}, "ModuleType": {"type": "string", "nullable": true}, "CreatedAt": {"type": "string", "format": "date-time"}, "UpdatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "UpdateDepartmentDTO": {"required": ["Id", "Name"], "type": "object", "properties": {"Id": {"type": "integer", "format": "int32"}, "Name": {"maxLength": 100, "minLength": 0, "type": "string"}, "Code": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "ParentId": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "UpdateInvoiceDTO": {"required": ["Amount", "Company", "Id", "TaxRate", "Type"], "type": "object", "properties": {"Id": {"type": "integer", "format": "int32"}, "Company": {"maxLength": 50, "minLength": 0, "type": "string"}, "Type": {"maxLength": 50, "minLength": 0, "type": "string"}, "TaxRate": {"maximum": 100, "minimum": 0, "type": "number", "format": "double"}, "Amount": {"minimum": 0.01, "type": "number", "format": "double"}, "Status": {"type": "string", "nullable": true}, "Content": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "Code": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "ReceiverName": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "ReceiverPhone": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "MailingAddress": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "ApplicantPhone": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "CustomerEmail": {"maxLength": 100, "minLength": 0, "type": "string", "format": "email", "nullable": true}}, "additionalProperties": false}, "UpdateOpportunityDTO": {"required": ["CustomerName", "Id", "Name", "Status"], "type": "object", "properties": {"Id": {"type": "integer", "format": "int32"}, "Name": {"maxLength": 100, "minLength": 0, "type": "string"}, "CustomerName": {"maxLength": 100, "minLength": 0, "type": "string"}, "ContactName": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "ContactPhone": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "Content": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "Status": {"maxLength": 20, "minLength": 0, "type": "string"}}, "additionalProperties": false}, "UpdateOrderDTO": {"required": ["AccountManagerId", "Amount", "Code", "<PERSON><PERSON><PERSON><PERSON>", "CustomerName", "DepartmentId", "Id", "Name", "SettlementAmount", "SignDate", "Status"], "type": "object", "properties": {"Id": {"type": "integer", "format": "int32"}, "Name": {"maxLength": 100, "minLength": 0, "type": "string"}, "Code": {"maxLength": 50, "minLength": 0, "type": "string"}, "CustomerName": {"maxLength": 100, "minLength": 0, "type": "string"}, "ContactName": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "ContactPhone": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "OpportunityId": {"type": "integer", "format": "int32", "nullable": true}, "Amount": {"minimum": 0.01, "type": "number", "format": "double"}, "Currency": {"maxLength": 20, "minLength": 0, "type": "string"}, "SettlementAmount": {"minimum": 0.01, "type": "number", "format": "double"}, "AccountManagerId": {"type": "integer", "format": "int32"}, "DepartmentId": {"type": "integer", "format": "int32"}, "SignDate": {"type": "string", "format": "date-time"}, "Status": {"maxLength": 20, "minLength": 0, "type": "string"}, "ProjectManagerId": {"type": "integer", "format": "int32", "nullable": true}, "PerformanceDepartmentId": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "UpdatePaymentDTO": {"required": ["Amount", "Code", "<PERSON><PERSON><PERSON><PERSON>", "Id", "InvoiceStatus", "Name", "PaymentType", "SettlementAmount"], "type": "object", "properties": {"Id": {"type": "integer", "format": "int32"}, "Name": {"maxLength": 100, "minLength": 0, "type": "string"}, "Code": {"maxLength": 50, "minLength": 0, "type": "string"}, "PaymentType": {"maxLength": 20, "minLength": 0, "type": "string"}, "Amount": {"minimum": 0.01, "type": "number", "format": "double"}, "Currency": {"maxLength": 20, "minLength": 0, "type": "string"}, "SettlementAmount": {"minimum": 0.01, "type": "number", "format": "double"}, "ExpectedPaymentDate": {"type": "string", "format": "date-time", "nullable": true}, "ActualPaymentDate": {"type": "string", "format": "date-time", "nullable": true}, "InvoiceStatus": {"maxLength": 20, "minLength": 0, "type": "string"}, "InvoiceDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "UpdateUserDTO": {"required": ["Id"], "type": "object", "properties": {"Id": {"type": "integer", "format": "int32"}, "Name": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "Gender": {"type": "string", "nullable": true}, "Email": {"type": "string", "nullable": true}, "Phone": {"type": "string", "nullable": true}, "DepartmentId": {"type": "integer", "format": "int32", "nullable": true}, "Password": {"maxLength": 100, "minLength": 6, "type": "string", "nullable": true}}, "additionalProperties": false}, "UserPermissionDto": {"type": "object", "properties": {"Id": {"type": "integer", "format": "int32"}, "UserId": {"type": "integer", "format": "int32"}, "PermissionId": {"type": "integer", "format": "int32"}, "ModuleType": {"type": "string", "nullable": true}, "CreatedAt": {"type": "string", "format": "date-time"}, "UpdatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}}}}