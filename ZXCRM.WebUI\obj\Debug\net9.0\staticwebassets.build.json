{"Version": 1, "Hash": "OOTothgGKmq0bTLKLu9PJvr4bl9NyhmTXsN1DrAs8CE=", "Source": "ZXCRM.WebUI", "BasePath": "_content/ZXCRM.WebUI", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "ZXCRM.WebUI\\wwwroot", "Source": "ZXCRM.WebUI", "ContentRoot": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\", "BasePath": "_content/ZXCRM.WebUI", "Pattern": "**"}], "Assets": [{"Identity": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\2mesmu78bt-c5jsywe0fz.gz", "SourceId": "ZXCRM.WebUI", "SourceType": "Discovered", "ContentRoot": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ZXCRM.WebUI", "RelativePath": "css/site#[.{fingerprint=c5jsywe0fz}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "07f12uybcj", "Integrity": "LF247tm+Ygw3W+aMphNfMTDK72I9bXMullzV3MzwHuY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\css\\site.css", "FileLength": 4180, "LastWriteTime": "2025-06-05T08:15:56+00:00"}, {"Identity": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\ipjiihu8wq-ji9w67flmt.gz", "SourceId": "ZXCRM.WebUI", "SourceType": "Discovered", "ContentRoot": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ZXCRM.WebUI", "RelativePath": "img/placeholder#[.{fingerprint=ji9w67flmt}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\img\\placeholder.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "awksfbh0mk", "Integrity": "NhzAA6NBedqJytCK3ToxeUU3Dc1+TmFGhFNN8Ifz84A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\img\\placeholder.txt", "FileLength": 317, "LastWriteTime": "2025-06-05T08:15:56+00:00"}, {"Identity": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\lr93ksuwtu-o400d3i4ax.gz", "SourceId": "ZXCRM.WebUI", "SourceType": "Discovered", "ContentRoot": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ZXCRM.WebUI", "RelativePath": "js/site#[.{fingerprint=o400d3i4ax}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bd2hdj<PERSON><PERSON>", "Integrity": "P+850Ht/tnJrgjQ/VToKj4GQMcP41kBRp8POlZiQsxM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\js\\site.js", "FileLength": 2316, "LastWriteTime": "2025-06-05T08:15:56+00:00"}, {"Identity": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\css\\site.css", "SourceId": "ZXCRM.WebUI", "SourceType": "Discovered", "ContentRoot": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\", "BasePath": "_content/ZXCRM.WebUI", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c5jsywe0fz", "Integrity": "EO6RHZxTE5HHwzt4Grc+kq2k/kiX34rn5FR3qW1y7LY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 19079, "LastWriteTime": "2025-05-29T08:29:19+00:00"}, {"Identity": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\img\\placeholder.txt", "SourceId": "ZXCRM.WebUI", "SourceType": "Discovered", "ContentRoot": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\", "BasePath": "_content/ZXCRM.WebUI", "RelativePath": "img/placeholder#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ji9w67flmt", "Integrity": "K2pimtkMM2h05wlF2lE7oVguAzABkjNoPl2HApLArSc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\placeholder.txt", "FileLength": 418, "LastWriteTime": "2025-05-26T02:54:22+00:00"}, {"Identity": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\js\\site.js", "SourceId": "ZXCRM.WebUI", "SourceType": "Discovered", "ContentRoot": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\", "BasePath": "_content/ZXCRM.WebUI", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "o400d3i4ax", "Integrity": "WMbMqjC378r3pmuObT7rbUtbm6uRDaAzFKdVIrFi/PM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 6968, "LastWriteTime": "2025-05-26T02:54:12+00:00"}], "Endpoints": [{"Route": "css/site.c5jsywe0fz.css", "AssetFile": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\2mesmu78bt-c5jsywe0fz.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000239177230"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4180"}, {"Name": "ETag", "Value": "\"LF247tm+Ygw3W+aMphNfMTDK72I9bXMullzV3MzwHuY=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 08:15:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"EO6RHZxTE5HHwzt4Grc+kq2k/kiX34rn5FR3qW1y7LY=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c5jsywe0fz"}, {"Name": "label", "Value": "css/site.css"}, {"Name": "integrity", "Value": "sha256-EO6RHZxTE5HHwzt4Grc+kq2k/kiX34rn5FR3qW1y7LY="}]}, {"Route": "css/site.c5jsywe0fz.css", "AssetFile": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19079"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"EO6RHZxTE5HHwzt4Grc+kq2k/kiX34rn5FR3qW1y7LY=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:29:19 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c5jsywe0fz"}, {"Name": "label", "Value": "css/site.css"}, {"Name": "integrity", "Value": "sha256-EO6RHZxTE5HHwzt4Grc+kq2k/kiX34rn5FR3qW1y7LY="}]}, {"Route": "css/site.c5jsywe0fz.css.gz", "AssetFile": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\2mesmu78bt-c5jsywe0fz.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4180"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LF247tm+Ygw3W+aMphNfMTDK72I9bXMullzV3MzwHuY=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 08:15:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c5jsywe0fz"}, {"Name": "label", "Value": "css/site.css.gz"}, {"Name": "integrity", "Value": "sha256-LF247tm+Ygw3W+aMphNfMTDK72I9bXMullzV3MzwHuY="}]}, {"Route": "css/site.css", "AssetFile": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\2mesmu78bt-c5jsywe0fz.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000239177230"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4180"}, {"Name": "ETag", "Value": "\"LF247tm+Ygw3W+aMphNfMTDK72I9bXMullzV3MzwHuY=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 08:15:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"EO6RHZxTE5HHwzt4Grc+kq2k/kiX34rn5FR3qW1y7LY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EO6RHZxTE5HHwzt4Grc+kq2k/kiX34rn5FR3qW1y7LY="}]}, {"Route": "css/site.css", "AssetFile": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19079"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"EO6RHZxTE5HHwzt4Grc+kq2k/kiX34rn5FR3qW1y7LY=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:29:19 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EO6RHZxTE5HHwzt4Grc+kq2k/kiX34rn5FR3qW1y7LY="}]}, {"Route": "css/site.css.gz", "AssetFile": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\2mesmu78bt-c5jsywe0fz.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4180"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LF247tm+Ygw3W+aMphNfMTDK72I9bXMullzV3MzwHuY=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 08:15:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LF247tm+Ygw3W+aMphNfMTDK72I9bXMullzV3MzwHuY="}]}, {"Route": "img/placeholder.ji9w67flmt.txt", "AssetFile": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\ipjiihu8wq-ji9w67flmt.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003144654088"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "317"}, {"Name": "ETag", "Value": "\"NhzAA6NBedqJytCK3ToxeUU3Dc1+TmFGhFNN8Ifz84A=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 08:15:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"K2pimtkMM2h05wlF2lE7oVguAzABkjNoPl2HApLArSc=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ji9w67flmt"}, {"Name": "label", "Value": "img/placeholder.txt"}, {"Name": "integrity", "Value": "sha256-K2pimtkMM2h05wlF2lE7oVguAzABkjNoPl2HApLArSc="}]}, {"Route": "img/placeholder.ji9w67flmt.txt", "AssetFile": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\img\\placeholder.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "418"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"K2pimtkMM2h05wlF2lE7oVguAzABkjNoPl2HApLArSc=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 02:54:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ji9w67flmt"}, {"Name": "label", "Value": "img/placeholder.txt"}, {"Name": "integrity", "Value": "sha256-K2pimtkMM2h05wlF2lE7oVguAzABkjNoPl2HApLArSc="}]}, {"Route": "img/placeholder.ji9w67flmt.txt.gz", "AssetFile": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\ipjiihu8wq-ji9w67flmt.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "317"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"NhzAA6NBedqJytCK3ToxeUU3Dc1+TmFGhFNN8Ifz84A=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 08:15:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ji9w67flmt"}, {"Name": "label", "Value": "img/placeholder.txt.gz"}, {"Name": "integrity", "Value": "sha256-NhzAA6NBedqJytCK3ToxeUU3Dc1+TmFGhFNN8Ifz84A="}]}, {"Route": "img/placeholder.txt", "AssetFile": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\ipjiihu8wq-ji9w67flmt.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003144654088"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "317"}, {"Name": "ETag", "Value": "\"NhzAA6NBedqJytCK3ToxeUU3Dc1+TmFGhFNN8Ifz84A=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 08:15:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"K2pimtkMM2h05wlF2lE7oVguAzABkjNoPl2HApLArSc=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K2pimtkMM2h05wlF2lE7oVguAzABkjNoPl2HApLArSc="}]}, {"Route": "img/placeholder.txt", "AssetFile": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\img\\placeholder.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "418"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"K2pimtkMM2h05wlF2lE7oVguAzABkjNoPl2HApLArSc=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 02:54:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K2pimtkMM2h05wlF2lE7oVguAzABkjNoPl2HApLArSc="}]}, {"Route": "img/placeholder.txt.gz", "AssetFile": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\ipjiihu8wq-ji9w67flmt.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "317"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"NhzAA6NBedqJytCK3ToxeUU3Dc1+TmFGhFNN8Ifz84A=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 08:15:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NhzAA6NBedqJytCK3ToxeUU3Dc1+TmFGhFNN8Ifz84A="}]}, {"Route": "js/site.js", "AssetFile": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\lr93ksuwtu-o400d3i4ax.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000431592577"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2316"}, {"Name": "ETag", "Value": "\"P+850Ht/tnJrgjQ/VToKj4GQMcP41kBRp8POlZiQsxM=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 08:15:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"WMbMqjC378r3pmuObT7rbUtbm6uRDaAzFKdVIrFi/PM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WMbMqjC378r3pmuObT7rbUtbm6uRDaAzFKdVIrFi/PM="}]}, {"Route": "js/site.js", "AssetFile": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\js\\site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6968"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WMbMqjC378r3pmuObT7rbUtbm6uRDaAzFKdVIrFi/PM=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 02:54:12 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WMbMqjC378r3pmuObT7rbUtbm6uRDaAzFKdVIrFi/PM="}]}, {"Route": "js/site.js.gz", "AssetFile": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\lr93ksuwtu-o400d3i4ax.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2316"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"P+850Ht/tnJrgjQ/VToKj4GQMcP41kBRp8POlZiQsxM=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 08:15:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-P+850Ht/tnJrgjQ/VToKj4GQMcP41kBRp8POlZiQsxM="}]}, {"Route": "js/site.o400d3i4ax.js", "AssetFile": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\lr93ksuwtu-o400d3i4ax.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000431592577"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2316"}, {"Name": "ETag", "Value": "\"P+850Ht/tnJrgjQ/VToKj4GQMcP41kBRp8POlZiQsxM=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 08:15:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"WMbMqjC378r3pmuObT7rbUtbm6uRDaAzFKdVIrFi/PM=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o400d3i4ax"}, {"Name": "label", "Value": "js/site.js"}, {"Name": "integrity", "Value": "sha256-WMbMqjC378r3pmuObT7rbUtbm6uRDaAzFKdVIrFi/PM="}]}, {"Route": "js/site.o400d3i4ax.js", "AssetFile": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\wwwroot\\js\\site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6968"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WMbMqjC378r3pmuObT7rbUtbm6uRDaAzFKdVIrFi/PM=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 02:54:12 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o400d3i4ax"}, {"Name": "label", "Value": "js/site.js"}, {"Name": "integrity", "Value": "sha256-WMbMqjC378r3pmuObT7rbUtbm6uRDaAzFKdVIrFi/PM="}]}, {"Route": "js/site.o400d3i4ax.js.gz", "AssetFile": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\obj\\Debug\\net9.0\\compressed\\lr93ksuwtu-o400d3i4ax.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2316"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"P+850Ht/tnJrgjQ/VToKj4GQMcP41kBRp8POlZiQsxM=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 08:15:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o400d3i4ax"}, {"Name": "label", "Value": "js/site.js.gz"}, {"Name": "integrity", "Value": "sha256-P+850Ht/tnJrgjQ/VToKj4GQMcP41kBRp8POlZiQsxM="}]}]}