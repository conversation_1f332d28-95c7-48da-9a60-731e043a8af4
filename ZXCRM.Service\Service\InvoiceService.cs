using AutoMapper;
using ZXCRM.Data.Entities;
using ZXCRM.Data.UnitOfWork;
using ZXCRM.Service.DTOs;
using ZXCRM.Service.Interface;

namespace ZXCRM.Service.Service
{
    public class InvoiceService : IInvoiceService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public InvoiceService(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task<IEnumerable<InvoiceDTO>> GetAllInvoicesAsync()
        {
            var invoices = await _unitOfWork.Invoices.GetAllWithIncludesAsync(
                i => i.Payment,
                i => i.Payment.Order,
                i => i.CreatedBy
            );
            return _mapper.Map<IEnumerable<InvoiceDTO>>(invoices);
        }

        public async Task<IEnumerable<InvoiceDTO>> GetInvoicesByPaymentIdAsync(int paymentId)
        {
            var invoices = await _unitOfWork.Invoices.FindWithIncludesAsync(
                i => i.PaymentId == paymentId && !i.IsDeleted,
                i => i.Payment,
                i => i.Payment.Order,
                i => i.CreatedBy
            );
            return _mapper.Map<IEnumerable<InvoiceDTO>>(invoices);
        }

        /// <summary>
        /// 获取款项的有效发票金额总额（排除已作废的发票）
        /// </summary>
        /// <param name="paymentId">款项ID</param>
        /// <returns>有效发票金额总额</returns>
        public async Task<decimal> GetValidInvoiceAmountByPaymentIdAsync(int paymentId)
        {
            var invoices = await _unitOfWork.Invoices.FindAsync(i => i.PaymentId == paymentId && !i.IsDeleted && i.Status != "Cancelled");
            return invoices.Sum(i => i.Amount);
        }

        public async Task<InvoiceDTO?> GetInvoiceByIdAsync(int id)
        {
            var invoice = await _unitOfWork.Invoices.GetByIdWithIncludesAsync(id,
                i => i.Payment,
                i => i.Payment.Order,
                i => i.CreatedBy
            );
            return invoice != null ? _mapper.Map<InvoiceDTO>(invoice) : null;
        }

        public async Task<InvoiceDTO> CreateInvoiceAsync(CreateInvoiceDTO createInvoiceDto)
        {
            // 检查款项是否存在
            var payment = await _unitOfWork.Payments.GetByIdAsync(createInvoiceDto.PaymentId);
            if (payment == null)
            {
                throw new Exception("关联款项不存在");
            }

            // 检查用户是否存在
            var user = await _unitOfWork.Users.GetByIdAsync(createInvoiceDto.CreatedById);
            if (user == null)
            {
                throw new Exception("创建人不存在");
            }

            // 检查发票金额是否超过款项金额
            if (createInvoiceDto.Amount > payment.Amount)
            {
                throw new Exception($"发票金额({createInvoiceDto.Amount:N2})不能超过款项金额({payment.Amount:N2})");
            }

            // 检查已有发票金额加上新发票金额是否超过款项金额
            decimal existingInvoiceAmount = await GetValidInvoiceAmountByPaymentIdAsync(payment.Id);
            if (existingInvoiceAmount + createInvoiceDto.Amount > payment.Amount)
            {
                throw new Exception($"所有发票金额之和({existingInvoiceAmount + createInvoiceDto.Amount:N2})不能超过款项金额({payment.Amount:N2})");
            }

            // 创建发票
            var invoice = _mapper.Map<Invoice>(createInvoiceDto);
            invoice.CreatedAt = DateTime.Now;
            invoice.UpdatedAt = DateTime.Now;

            await _unitOfWork.Invoices.AddAsync(invoice);

            // 更新款项的开票状态
            payment.InvoiceStatus = "已开票";
            payment.InvoiceDate = DateTime.Now;
            payment.UpdatedAt = DateTime.Now;

            await _unitOfWork.Payments.UpdateAsync(payment);
            await _unitOfWork.SaveChangesAsync();

            // 重新获取包含关联数据的发票
            var createdInvoice = await _unitOfWork.Invoices.GetByIdWithIncludesAsync(invoice.Id,
                i => i.Payment,
                i => i.Payment.Order,
                i => i.CreatedBy
            );

            return _mapper.Map<InvoiceDTO>(createdInvoice);
        }

        public async Task<InvoiceDTO?> UpdateInvoiceAsync(UpdateInvoiceDTO updateInvoiceDto)
        {
            var invoice = await _unitOfWork.Invoices.GetByIdAsync(updateInvoiceDto.Id);
            if (invoice == null)
            {
                return null;
            }

            // 获取关联款项
            var payment = await _unitOfWork.Payments.GetByIdAsync(invoice.PaymentId);
            if (payment == null)
            {
                throw new Exception("关联款项不存在");
            }

            // 检查发票金额是否超过款项金额
            if (updateInvoiceDto.Amount > payment.Amount)
            {
                throw new Exception($"发票金额({updateInvoiceDto.Amount:N2})不能超过款项金额({payment.Amount:N2})");
            }

            // 检查已有发票金额加上新发票金额是否超过款项金额
            decimal existingInvoiceAmount = await GetValidInvoiceAmountByPaymentIdAsync(payment.Id);
            // 需要减去当前发票的金额，因为我们要更新它
            existingInvoiceAmount -= invoice.Status != "Cancelled" ? invoice.Amount : 0;

            if (existingInvoiceAmount + updateInvoiceDto.Amount > payment.Amount)
            {
                throw new Exception($"所有发票金额之和({existingInvoiceAmount + updateInvoiceDto.Amount:N2})不能超过款项金额({payment.Amount:N2})");
            }

            // 更新发票信息
            invoice.Company = updateInvoiceDto.Company;
            invoice.Type = updateInvoiceDto.Type;
            invoice.TaxRate = updateInvoiceDto.TaxRate;
            invoice.Amount = updateInvoiceDto.Amount; // 更新发票金额
            invoice.Status = updateInvoiceDto.Status; // 更新发票状态
            invoice.Content = updateInvoiceDto.Content;
            invoice.Code = updateInvoiceDto.Code;
            invoice.ReceiverName = updateInvoiceDto.ReceiverName;
            invoice.ReceiverPhone = updateInvoiceDto.ReceiverPhone;
            invoice.MailingAddress = updateInvoiceDto.MailingAddress;
            invoice.ApplicantPhone = updateInvoiceDto.ApplicantPhone;
            invoice.CustomerEmail = updateInvoiceDto.CustomerEmail;
            invoice.UpdatedAt = DateTime.Now;

            await _unitOfWork.Invoices.UpdateAsync(invoice);
            await _unitOfWork.SaveChangesAsync();

            // 重新获取包含关联数据的发票
            var updatedInvoice = await _unitOfWork.Invoices.GetByIdWithIncludesAsync(invoice.Id,
                i => i.Payment,
                i => i.Payment.Order,
                i => i.CreatedBy
            );

            return _mapper.Map<InvoiceDTO>(updatedInvoice);
        }

        public async Task<bool> DeleteInvoiceAsync(int id)
        {
            var invoice = await _unitOfWork.Invoices.GetByIdAsync(id);
            if (invoice == null)
            {
                return false;
            }

            // 获取关联的款项
            var payment = await _unitOfWork.Payments.GetByIdAsync(invoice.PaymentId);

            // 软删除发票
            invoice.IsDeleted = true;
            invoice.Status = "Cancelled"; // 设置发票状态为已作废
            invoice.UpdatedAt = DateTime.Now;

            await _unitOfWork.Invoices.UpdateAsync(invoice);

            // 检查该款项是否还有其他发票
            var otherInvoices = await _unitOfWork.Invoices.FindAsync(i => i.PaymentId == invoice.PaymentId && i.Id != id && !i.IsDeleted);
            if (!otherInvoices.Any() && payment != null)
            {
                // 如果没有其他发票，更新款项的开票状态
                payment.InvoiceStatus = "未开票";
                payment.InvoiceDate = null;
                payment.UpdatedAt = DateTime.Now;

                await _unitOfWork.Payments.UpdateAsync(payment);
            }

            await _unitOfWork.SaveChangesAsync();

            return true;
        }
    }
}
