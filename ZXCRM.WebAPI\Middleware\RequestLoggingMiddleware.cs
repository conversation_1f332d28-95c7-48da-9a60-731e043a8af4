using System.Diagnostics;
using System.Text;

namespace ZXCRM.WebAPI.Middleware
{
    public class RequestLoggingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<RequestLoggingMiddleware> _logger;

        public RequestLoggingMiddleware(RequestDelegate next, ILogger<RequestLoggingMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var stopwatch = Stopwatch.StartNew();
            var requestId = Guid.NewGuid().ToString("N")[..8];

            // 添加请求ID到响应头
            context.Response.Headers.Append("X-Request-ID", requestId);

            // 记录请求开始
            _logger.LogInformation("Request {RequestId} started: {Method} {Path} from {RemoteIpAddress}",
                requestId,
                context.Request.Method,
                context.Request.Path,
                context.Connection.RemoteIpAddress);

            // 记录请求体（仅对POST/PUT请求，且非文件上传）
            if (ShouldLogRequestBody(context.Request))
            {
                await LogRequestBody(context, requestId);
            }

            // 保存原始响应流
            var originalBodyStream = context.Response.Body;

            try
            {
                using var responseBody = new MemoryStream();
                context.Response.Body = responseBody;

                await _next(context);

                stopwatch.Stop();

                // 记录响应
                await LogResponse(context, requestId, stopwatch.ElapsedMilliseconds);

                // 复制响应到原始流
                responseBody.Seek(0, SeekOrigin.Begin);
                await responseBody.CopyToAsync(originalBodyStream);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Request {RequestId} failed after {ElapsedMs}ms: {Method} {Path}",
                    requestId,
                    stopwatch.ElapsedMilliseconds,
                    context.Request.Method,
                    context.Request.Path);
                throw;
            }
            finally
            {
                context.Response.Body = originalBodyStream;
            }
        }

        private static bool ShouldLogRequestBody(HttpRequest request)
        {
            return (request.Method == "POST" || request.Method == "PUT") &&
                   request.ContentType != null &&
                   request.ContentType.Contains("application/json") &&
                   request.ContentLength < 10240; // 限制10KB以下
        }

        private async Task LogRequestBody(HttpContext context, string requestId)
        {
            try
            {
                context.Request.EnableBuffering();
                var buffer = new byte[Convert.ToInt32(context.Request.ContentLength)];
                await context.Request.Body.ReadExactlyAsync(buffer, 0, buffer.Length);
                var requestBody = Encoding.UTF8.GetString(buffer);
                context.Request.Body.Position = 0;

                _logger.LogDebug("Request {RequestId} body: {RequestBody}", requestId, requestBody);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to log request body for {RequestId}", requestId);
            }
        }

        private async Task LogResponse(HttpContext context, string requestId, long elapsedMs)
        {
            try
            {
                context.Response.Body.Seek(0, SeekOrigin.Begin);
                var responseBody = await new StreamReader(context.Response.Body).ReadToEndAsync();
                context.Response.Body.Seek(0, SeekOrigin.Begin);

                var logLevel = context.Response.StatusCode >= 400 ? LogLevel.Warning : LogLevel.Information;

                _logger.Log(logLevel,
                    "Request {RequestId} completed in {ElapsedMs}ms: {Method} {Path} -> {StatusCode}",
                    requestId,
                    elapsedMs,
                    context.Request.Method,
                    context.Request.Path,
                    context.Response.StatusCode);

                // 记录慢请求
                if (elapsedMs > 5000) // 超过5秒
                {
                    _logger.LogWarning("Slow request detected {RequestId}: {ElapsedMs}ms for {Method} {Path}",
                        requestId, elapsedMs, context.Request.Method, context.Request.Path);
                }

                // 记录错误响应体
                if (context.Response.StatusCode >= 400 && !string.IsNullOrEmpty(responseBody))
                {
                    _logger.LogDebug("Error response {RequestId}: {ResponseBody}", requestId, responseBody);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to log response for {RequestId}", requestId);
            }
        }
    }

    public static class RequestLoggingMiddlewareExtensions
    {
        public static IApplicationBuilder UseRequestLogging(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<RequestLoggingMiddleware>();
        }
    }
}
