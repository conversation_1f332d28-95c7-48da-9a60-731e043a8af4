using ZXCRM.Service.DTOs;

namespace ZXCRM.Service.Interface
{
    public interface IPaymentService
    {
        Task<IEnumerable<PaymentDTO>> GetAllPaymentsAsync();
        Task<IEnumerable<PaymentDTO>> GetPaymentsByOrderIdAsync(int orderId);
        Task<PaymentDTO?> GetPaymentByIdAsync(int id);
        Task<PaymentDTO> CreatePaymentAsync(CreatePaymentDTO createPaymentDto);
        Task<PaymentDTO?> UpdatePaymentAsync(UpdatePaymentDTO updatePaymentDto);
        Task<bool> DeletePaymentAsync(int id);
    }
}
