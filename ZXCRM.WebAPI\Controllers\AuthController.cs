using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ZXCRM.Service.DTOs;
using ZXCRM.Service.Interface;

namespace ZXCRM.WebAPI.Controllers
{
    public class AuthController : BaseController
    {
        private readonly IAuthService _authService;

        public AuthController(IAuthService authService)
        {
            _authService = authService;
        }

        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<IActionResult> Login([FromBody] LoginDTO loginDto)
        {
            try
            {
                // 记录登录请求
                Console.WriteLine($"收到登录请求: 用户名={loginDto.Username}");

                // 验证请求数据
                if (string.IsNullOrEmpty(loginDto.Username) || string.IsNullOrEmpty(loginDto.Password))
                {
                    Console.WriteLine("登录失败: 用户名或密码为空");
                    return Failure("用户名和密码不能为空");
                }

                // 调用登录服务
                var result = await _authService.LoginAsync(loginDto);
                Console.WriteLine($"登录成功: 用户ID={result.User.Id}, 用户名={result.User.Username}");

                return Success(result, "登录成功");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"登录异常: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");
                return Failure(ex.Message);
            }
        }

        [HttpGet("validate-token")]
        public async Task<IActionResult> ValidateToken()
        {
            try
            {
                var token = HttpContext.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
                var isValid = await _authService.ValidateTokenAsync(token);

                if (isValid)
                {
                    var user = await _authService.GetUserFromTokenAsync(token);
                    return Success(user, "Token有效");
                }

                return Failure("Token无效");
            }
            catch (Exception ex)
            {
                return Failure(ex.Message);
            }
        }

        [HttpGet("current-user")]
        [Authorize]
        public async Task<IActionResult> GetCurrentUser()
        {
            try
            {
                var token = HttpContext.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
                var user = await _authService.GetUserFromTokenAsync(token);

                if (user != null)
                {
                    return Success(user);
                }

                return Failure("获取当前用户信息失败");
            }
            catch (Exception ex)
            {
                return Failure(ex.Message);
            }
        }
    }
}
