@using ZXCRM.Service.DTOs
@using ZXCRM.WebUI.Models
@using ZXCRM.WebUI.Helpers
@{
    ViewData["Title"] = "简单数据测试";
}

<div class="container-fluid">
    <h2>订单数据显示测试</h2>
    
    @if (ViewBag.Error != null)
    {
        <div class="alert alert-danger">
            <h4>错误信息</h4>
            <p>@ViewBag.Error</p>
        </div>
    }
    else
    {
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3>API响应信息</h3>
                    </div>
                    <div class="card-body">
                        @if (ViewBag.ApiResponse != null)
                        {
                            var response = ViewBag.ApiResponse as ApiResponse<List<OrderDTO>>;
                            <p><strong>Success:</strong> @response.Success</p>
                            <p><strong>Message:</strong> @response.Message</p>
                            <p><strong>Data Count:</strong> @(response.Data?.Count ?? 0)</p>
                        }
                        else
                        {
                            <p>API响应为空</p>
                        }
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3>第一个订单数据</h3>
                    </div>
                    <div class="card-body">
                        @if (ViewBag.FirstOrder != null)
                        {
                            var order = ViewBag.FirstOrder as OrderDTO;
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>ID:</strong></td>
                                    <td>@order.Id</td>
                                </tr>
                                <tr>
                                    <td><strong>名称:</strong></td>
                                    <td>@order.Name</td>
                                </tr>
                                <tr>
                                    <td><strong>编号:</strong></td>
                                    <td>@order.Code</td>
                                </tr>
                                <tr>
                                    <td><strong>客户:</strong></td>
                                    <td>@order.CustomerName</td>
                                </tr>
                                <tr style="background-color: #fff3cd;">
                                    <td><strong>金额:</strong></td>
                                    <td>
                                        <span style="color: red;">原始值: @order.Amount</span><br>
                                        <span style="color: blue;">格式化: @EnumHelper.FormatAmount(order.Amount, order.Currency)</span>
                                    </td>
                                </tr>
                                <tr style="background-color: #fff3cd;">
                                    <td><strong>币种:</strong></td>
                                    <td>
                                        <span style="color: red;">原始值: "@order.Currency"</span><br>
                                        <span style="color: blue;">是否为空: @(string.IsNullOrEmpty(order.Currency))</span>
                                    </td>
                                </tr>
                                <tr style="background-color: #fff3cd;">
                                    <td><strong>状态:</strong></td>
                                    <td>
                                        <span style="color: red;">原始值: "@order.Status"</span><br>
                                        <span style="color: blue;">格式化: @EnumHelper.GetOrderStatusText(order.Status)</span><br>
                                        <span style="color: green;">颜色: @EnumHelper.GetOrderStatusColor(order.Status)</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>客户经理:</strong></td>
                                    <td>@order.AccountManagerName</td>
                                </tr>
                                <tr>
                                    <td><strong>部门:</strong></td>
                                    <td>@order.DepartmentName</td>
                                </tr>
                                <tr>
                                    <td><strong>签约日期:</strong></td>
                                    <td>@order.SignDate</td>
                                </tr>
                            </table>
                        }
                        else
                        {
                            <p>没有订单数据</p>
                        }
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3>EnumHelper测试</h3>
                    </div>
                    <div class="card-body">
                        <h5>金额格式化测试:</h5>
                        <ul>
                            <li>FormatAmount(100000, "CNY"): @EnumHelper.FormatAmount(100000, "CNY")</li>
                            <li>FormatAmount(100000, "USD"): @EnumHelper.FormatAmount(100000, "USD")</li>
                            <li>FormatAmount(0, "CNY"): @EnumHelper.FormatAmount(0, "CNY")</li>
                            <li>FormatAmount(100000, null): @EnumHelper.FormatAmount(100000, null)</li>
                            <li>FormatAmount(100000, ""): @EnumHelper.FormatAmount(100000, "")</li>
                        </ul>
                        
                        <h5>状态文本测试:</h5>
                        <ul>
                            <li>GetOrderStatusText("New"): @EnumHelper.GetOrderStatusText("New")</li>
                            <li>GetOrderStatusText("InProgress"): @EnumHelper.GetOrderStatusText("InProgress")</li>
                            <li>GetOrderStatusText("Completed"): @EnumHelper.GetOrderStatusText("Completed")</li>
                            <li>GetOrderStatusText("Cancelled"): @EnumHelper.GetOrderStatusText("Cancelled")</li>
                            <li>GetOrderStatusText(""): @EnumHelper.GetOrderStatusText("")</li>
                            <li>GetOrderStatusText(null): @EnumHelper.GetOrderStatusText(null)</li>
                        </ul>
                        
                        <h5>状态颜色测试:</h5>
                        <ul>
                            <li>GetOrderStatusColor("New"): <span class="badge <EMAIL>("New")">@EnumHelper.GetOrderStatusColor("New")</span></li>
                            <li>GetOrderStatusColor("InProgress"): <span class="badge <EMAIL>("InProgress")">@EnumHelper.GetOrderStatusColor("InProgress")</span></li>
                            <li>GetOrderStatusColor("Completed"): <span class="badge <EMAIL>("Completed")">@EnumHelper.GetOrderStatusColor("Completed")</span></li>
                            <li>GetOrderStatusColor("Cancelled"): <span class="badge <EMAIL>("Cancelled")">@EnumHelper.GetOrderStatusColor("Cancelled")</span></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<div class="mt-3">
    <a href="/Debug/CheckDatabaseRaw" class="btn btn-primary" target="_blank">检查数据库原始数据</a>
    <a href="/Debug/TestAutoMapper" class="btn btn-info" target="_blank">测试AutoMapper</a>
    <a href="/Debug/CheckOrderListViewModel" class="btn btn-success" target="_blank">检查视图模型</a>
    <a href="/Order" class="btn btn-secondary">返回订单列表</a>
</div>
