using System.ComponentModel.DataAnnotations;

namespace ZXCRM.WebUI.Models.ViewModels
{
    public class UserListViewModel
    {
        public List<UserItemViewModel> Users { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageIndex { get; set; }
        public int PageSize { get; set; }
        public string SearchTerm { get; set; } = string.Empty;
    }

    public class UserItemViewModel
    {
        public int Id { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public string Role { get; set; } = string.Empty;
        public string Status { get; set; } = "Active";
        public DateTime CreatedAt { get; set; }
    }

    public class CreateUserViewModel
    {
        [Required(ErrorMessage = "用户名不能为空")]
        [StringLength(50, ErrorMessage = "用户名长度不能超过50个字符")]
        [Display(Name = "用户名")]
        public string Username { get; set; } = string.Empty;

        [Required(ErrorMessage = "姓名不能为空")]
        [StringLength(100, ErrorMessage = "姓名长度不能超过100个字符")]
        [Display(Name = "姓名")]
        public string Name { get; set; } = string.Empty;

        [EmailAddress(ErrorMessage = "邮箱格式不正确")]
        [Display(Name = "邮箱")]
        public string? Email { get; set; }

        [Phone(ErrorMessage = "手机号格式不正确")]
        [Display(Name = "手机号")]
        public string? Phone { get; set; }

        [Display(Name = "性别")]
        public string? Gender { get; set; }

        [Required(ErrorMessage = "密码不能为空")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "密码长度必须在6-100个字符之间")]
        [DataType(DataType.Password)]
        [Display(Name = "密码")]
        public string Password { get; set; } = string.Empty;

        [Required(ErrorMessage = "确认密码不能为空")]
        [DataType(DataType.Password)]
        [Compare("Password", ErrorMessage = "密码和确认密码不匹配")]
        [Display(Name = "确认密码")]
        public string ConfirmPassword { get; set; } = string.Empty;

        [Required(ErrorMessage = "请选择部门")]
        [Display(Name = "部门")]
        public int DepartmentId { get; set; }

        [Required(ErrorMessage = "请选择角色")]
        [Display(Name = "角色")]
        public string Role { get; set; } = string.Empty;

        [Display(Name = "是否激活")]
        public bool IsActive { get; set; } = true;

        // 用于下拉列表
        public List<DepartmentSelectItem> Departments { get; set; } = new();
        public List<RoleSelectItem> Roles { get; set; } = new();
    }

    public class EditUserViewModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "用户名不能为空")]
        [StringLength(50, ErrorMessage = "用户名长度不能超过50个字符")]
        [Display(Name = "用户名")]
        public string Username { get; set; } = string.Empty;

        [Required(ErrorMessage = "姓名不能为空")]
        [StringLength(100, ErrorMessage = "姓名长度不能超过100个字符")]
        [Display(Name = "姓名")]
        public string Name { get; set; } = string.Empty;

        [EmailAddress(ErrorMessage = "邮箱格式不正确")]
        [Display(Name = "邮箱")]
        public string? Email { get; set; }

        [Phone(ErrorMessage = "手机号格式不正确")]
        [Display(Name = "手机号")]
        public string? Phone { get; set; }

        [Display(Name = "性别")]
        public string? Gender { get; set; }

        [StringLength(100, MinimumLength = 6, ErrorMessage = "密码长度必须在6-100个字符之间")]
        [DataType(DataType.Password)]
        [Display(Name = "新密码（留空表示不修改）")]
        public string? NewPassword { get; set; }

        [DataType(DataType.Password)]
        [Compare("NewPassword", ErrorMessage = "密码和确认密码不匹配")]
        [Display(Name = "确认新密码")]
        public string? ConfirmNewPassword { get; set; }

        [Required(ErrorMessage = "请选择部门")]
        [Display(Name = "部门")]
        public int DepartmentId { get; set; }

        [Required(ErrorMessage = "请选择角色")]
        [Display(Name = "角色")]
        public string Role { get; set; } = string.Empty;

        [Display(Name = "是否激活")]
        public bool IsActive { get; set; }

        // 用于下拉列表
        public List<DepartmentSelectItem> Departments { get; set; } = new();
        public List<RoleSelectItem> Roles { get; set; } = new();
    }

    public class UserProfileViewModel
    {
        public int Id { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Gender { get; set; } = string.Empty;
        public int? DepartmentId { get; set; }
        public string DepartmentName { get; set; } = string.Empty;
        public string Role { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class DepartmentSelectItem
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public int? ParentId { get; set; }
        public int Level { get; set; }
        public string DisplayName => new string('　', Level * 2) + Name; // 使用全角空格缩进
    }

    public class RoleSelectItem
    {
        public string Value { get; set; } = string.Empty;
        public string Text { get; set; } = string.Empty;
    }

    public class UserDetailViewModel
    {
        public int Id { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Gender { get; set; } = string.Empty;
        public int DepartmentId { get; set; }
        public string DepartmentName { get; set; } = string.Empty;
        public string Role { get; set; } = string.Empty;
        public string Status { get; set; } = "Active";
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }
}
