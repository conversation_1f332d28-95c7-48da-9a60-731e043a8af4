using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace ZXCRM.WebAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class TestApiController : ControllerBase
    {
        [HttpGet]
        [AllowAnonymous]
        public IActionResult Get()
        {
            // 记录请求信息
            Console.WriteLine("收到TestApi请求");
            
            // 返回简单的成功响应
            return Ok(new { 
                success = true, 
                message = "API正常工作", 
                timestamp = DateTime.Now 
            });
        }
        
        [HttpPost("echo")]
        [AllowAnonymous]
        public IActionResult Echo([FromBody] object data)
        {
            // 记录请求信息
            Console.WriteLine($"收到Echo请求: {data}");
            
            // 返回接收到的数据
            return Ok(new { 
                success = true, 
                message = "Echo成功", 
                data = data,
                timestamp = DateTime.Now 
            });
        }
    }
}
