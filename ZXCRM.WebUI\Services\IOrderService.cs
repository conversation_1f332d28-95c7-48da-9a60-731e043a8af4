using ZXCRM.Service.DTOs;
using ZXCRM.WebUI.Models;

namespace ZXCRM.WebUI.Services
{
    public interface IOrderService
    {
        Task<ApiResponse<List<OrderDTO>>> GetOrdersAsync();
        Task<ApiResponse<OrderDTO>> GetOrderByIdAsync(int id);
        Task<ApiResponse<OrderDTO>> CreateOrderAsync(CreateOrderDTO request);
        Task<ApiResponse<OrderDTO>> UpdateOrderAsync(int id, UpdateOrderDTO request);
        Task<ApiResponse<object>> DeleteOrderAsync(int id);
        Task<ApiResponse<List<OrderDTO>>> GetOrdersByStatusAsync(string status);
    }
}
