@model ZXCRM.Service.DTOs.PermissionDto
@{
    ViewData["Title"] = "编辑权限";
}

<!-- 编辑页面容器 -->
<div class="edit-page-container">
    <!-- 页面标题栏 -->
    <div class="edit-header">
        <div class="edit-title">
            <h2>
                <i class="fas fa-edit"></i>
                编辑权限
            </h2>
            <small class="text-muted">权限ID: #@Model.Id</small>
        </div>
        <div class="edit-actions">
            <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info">
                <i class="fas fa-eye"></i> 查看详情
            </a>
            <a asp-action="Index" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> 返回列表
            </a>
        </div>
    </div>

    <!-- 编辑表单 -->
    <div class="edit-content">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-key"></i>
                            权限信息
                        </h5>
                    </div>
                    <div class="card-body">
                        <form asp-action="Edit" method="post" class="needs-validation" novalidate>
                            @Html.AntiForgeryToken()
                            @Html.HiddenFor(model => model.Id)
                            @Html.HiddenFor(model => model.CreatedAt)
                            @Html.HiddenFor(model => model.UpdatedAt)

                            <div class="row">
                                <div class="col-md-12">
                                    <!-- 权限名称 -->
                                    <div class="form-group mb-3">
                                        <label asp-for="Name" class="form-label required">权限名称</label>
                                        <input asp-for="Name" class="form-control" placeholder="请输入权限名称" required maxlength="50">
                                        <span asp-validation-for="Name" class="text-danger"></span>
                                        <div class="form-text">权限的显示名称，如"查看订单"、"编辑用户"等</div>
                                    </div>

                                    <!-- 权限代码 -->
                                    <div class="form-group mb-3">
                                        <label asp-for="Code" class="form-label required">权限代码</label>
                                        <input asp-for="Code" class="form-control" placeholder="请输入权限代码" required maxlength="50">
                                        <span asp-validation-for="Code" class="text-danger"></span>
                                        <div class="form-text">权限的唯一标识符，如"ORDER_VIEW"、"USER_EDIT"等，建议使用大写字母和下划线</div>
                                    </div>

                                    <!-- 模块类型 -->
                                    <div class="form-group mb-3">
                                        <label asp-for="ModuleType" class="form-label">模块类型</label>
                                        <select asp-for="ModuleType" class="form-select">
                                            <option value="">请选择模块类型</option>
                                            <option value="系统管理">系统管理</option>
                                            <option value="用户管理">用户管理</option>
                                            <option value="部门管理">部门管理</option>
                                            <option value="权限管理">权限管理</option>
                                            <option value="商机管理">商机管理</option>
                                            <option value="订单管理">订单管理</option>
                                            <option value="款项管理">款项管理</option>
                                            <option value="发票管理">发票管理</option>
                                            <option value="报表统计">报表统计</option>
                                        </select>
                                        <span asp-validation-for="ModuleType" class="text-danger"></span>
                                        <div class="form-text">权限所属的功能模块</div>
                                    </div>

                                    <!-- 权限描述 -->
                                    <div class="form-group mb-4">
                                        <label asp-for="Description" class="form-label">权限描述</label>
                                        <textarea asp-for="Description" class="form-control" rows="3" placeholder="请输入权限描述" maxlength="200"></textarea>
                                        <span asp-validation-for="Description" class="text-danger"></span>
                                        <div class="form-text">详细描述该权限的作用和使用场景</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 表单按钮 -->
                            <div class="form-actions">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-save"></i> 保存更改
                                                </button>
                                                <button type="reset" class="btn btn-outline-secondary">
                                                    <i class="fas fa-undo"></i> 重置
                                                </button>
                                            </div>
                                            <div>
                                                <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-outline-info">
                                                    <i class="fas fa-eye"></i> 查看详情
                                                </a>
                                                <a asp-action="Index" class="btn btn-outline-secondary">
                                                    <i class="fas fa-list"></i> 返回列表
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 操作提示 -->
                <div class="card mt-3">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-info-circle text-info"></i>
                            编辑提示
                        </h6>
                        <ul class="mb-0 text-muted">
                            <li>权限名称应该简洁明了，便于理解</li>
                            <li>权限代码必须唯一，建议使用模块_操作的格式</li>
                            <li>修改权限信息后，相关用户的权限会立即生效</li>
                            <li>删除权限前请确保没有用户正在使用该权限</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // 表单验证
            $('.needs-validation').on('submit', function(e) {
                if (!this.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                $(this).addClass('was-validated');
            });

            // 权限代码自动转换为大写
            $('#Code').on('input', function() {
                $(this).val($(this).val().toUpperCase());
            });

            // 字符计数
            $('#Description').on('input', function() {
                var maxLength = 200;
                var currentLength = $(this).val().length;
                var remaining = maxLength - currentLength;
                
                var helpText = $(this).siblings('.form-text');
                if (remaining < 20) {
                    helpText.html('还可以输入 ' + remaining + ' 个字符');
                    helpText.removeClass('text-muted').addClass('text-warning');
                } else {
                    helpText.html('详细描述该权限的作用和使用场景');
                    helpText.removeClass('text-warning').addClass('text-muted');
                }
            });
        });

        // 显示提示消息
        @if (TempData["SuccessMessage"] != null)
        {
            <text>
            toastr.success('@TempData["SuccessMessage"]');
            </text>
        }

        @if (TempData["ErrorMessage"] != null)
        {
            <text>
            toastr.error('@TempData["ErrorMessage"]');
            </text>
        }
    </script>

    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}

<style>
    .edit-page-container {
        padding: 20px;
    }

    .edit-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #dee2e6;
    }

    .edit-title h2 {
        margin: 0;
        color: #495057;
        font-size: 1.5rem;
    }

    .edit-title i {
        margin-right: 10px;
        color: #6c757d;
    }

    .edit-actions .btn {
        margin-left: 10px;
    }

    .form-label.required::after {
        content: " *";
        color: #dc3545;
    }

    .form-actions {
        border-top: 1px solid #dee2e6;
        padding-top: 20px;
        margin-top: 20px;
    }

    .card-title i {
        margin-right: 8px;
        color: #6c757d;
    }

    .form-text {
        font-size: 0.875em;
        margin-top: 0.25rem;
    }

    .was-validated .form-control:valid {
        border-color: #28a745;
    }

    .was-validated .form-control:invalid {
        border-color: #dc3545;
    }
</style>
