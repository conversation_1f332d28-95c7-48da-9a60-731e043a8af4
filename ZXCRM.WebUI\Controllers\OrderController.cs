using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ZXCRM.Service.DTOs;
using ZXCRM.WebUI.Services;
using ZXCRM.WebUI.Models.ViewModels;

namespace ZXCRM.WebUI.Controllers
{
    [Authorize]
    public class OrderController : Controller
    {
        private readonly IOrderService _orderService;
        private readonly IUserService _userService;
        private readonly IDepartmentService _departmentService;
        private readonly IOpportunityService _opportunityService;
        private readonly ILogger<OrderController> _logger;

        public OrderController(
            IOrderService orderService,
            IUserService userService,
            IDepartmentService departmentService,
            IOpportunityService opportunityService,
            ILogger<OrderController> logger)
        {
            _orderService = orderService;
            _userService = userService;
            _departmentService = departmentService;
            _opportunityService = opportunityService;
            _logger = logger;
        }

        // GET: Order
        public async Task<IActionResult> Index(string searchTerm = "", string status = "", int pageIndex = 1, int pageSize = 25)
        {
            try
            {
                // 验证分页参数
                pageIndex = Math.Max(1, pageIndex);
                pageSize = Math.Max(1, Math.Min(100, pageSize)); // 限制最大100条每页

                _logger.LogInformation("Getting orders list. SearchTerm: {SearchTerm}, Status: {Status}, PageIndex: {PageIndex}, PageSize: {PageSize}",
                    searchTerm, status, pageIndex, pageSize);

                var response = await _orderService.GetOrdersAsync();

                if (!response.Success || response.Data == null)
                {
                    _logger.LogWarning("Failed to get orders: {Message}", response.Message);
                    TempData["ErrorMessage"] = response.Message ?? "获取订单列表失败";
                    return View(new OrderListViewModel());
                }

                // 应用搜索和状态过滤
                var filteredOrders = response.Data.AsQueryable();

                if (!string.IsNullOrEmpty(searchTerm))
                {
                    filteredOrders = filteredOrders.Where(o =>
                        o.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        o.CustomerName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        o.Code.Contains(searchTerm, StringComparison.OrdinalIgnoreCase));
                }

                if (!string.IsNullOrEmpty(status))
                {
                    filteredOrders = filteredOrders.Where(o => o.Status == status);
                }

                // 应用分页
                var totalCount = filteredOrders.Count();
                var orders = filteredOrders
                    .Skip((pageIndex - 1) * pageSize)
                    .Take(pageSize)
                    .Select(o => new OrderItemViewModel
                    {
                        Id = o.Id,
                        Name = o.Name,
                        Code = o.Code,
                        CustomerName = o.CustomerName,
                        Amount = o.Amount,
                        Currency = o.Currency,
                        Status = o.Status,
                        AccountManagerName = o.AccountManagerName,
                        DepartmentName = o.DepartmentName,
                        SignDate = o.SignDate,
                        CreatedAt = o.CreatedAt
                    })
                    .ToList();

                var viewModel = new OrderListViewModel
                {
                    Orders = orders,
                    TotalCount = totalCount,
                    PageIndex = pageIndex,
                    PageSize = pageSize,
                    SearchTerm = searchTerm,
                    Status = status
                };

                _logger.LogInformation("Successfully retrieved {Count} orders", orders.Count);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting orders list");
                TempData["ErrorMessage"] = "获取订单列表时发生错误";
                return View(new OrderListViewModel());
            }
        }

        // GET: Order/Details/5
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                _logger.LogInformation("Getting order details for ID: {OrderId}", id);

                var response = await _orderService.GetOrderByIdAsync(id);

                if (!response.Success || response.Data == null)
                {
                    _logger.LogWarning("Order not found: {OrderId}", id);
                    TempData["ErrorMessage"] = "订单不存在";
                    return RedirectToAction(nameof(Index));
                }

                var viewModel = new OrderDetailViewModel
                {
                    Id = response.Data.Id,
                    Name = response.Data.Name,
                    Code = response.Data.Code,
                    CustomerName = response.Data.CustomerName,
                    ContactName = response.Data.ContactName ?? string.Empty,
                    ContactPhone = response.Data.ContactPhone ?? string.Empty,
                    Amount = response.Data.Amount,
                    Currency = response.Data.Currency,
                    SettlementAmount = response.Data.SettlementAmount,
                    Status = response.Data.Status,
                    AccountManagerName = response.Data.AccountManagerName,
                    DepartmentName = response.Data.DepartmentName,
                    ProjectManagerName = response.Data.ProjectManagerName ?? string.Empty,
                    PerformanceDepartmentName = response.Data.PerformanceDepartmentName ?? string.Empty,
                    OpportunityName = response.Data.OpportunityName ?? string.Empty,
                    SignDate = response.Data.SignDate,
                    CreatedAt = response.Data.CreatedAt,
                    UpdatedAt = response.Data.UpdatedAt,
                    CreatedByName = response.Data.CreatedByName
                };

                _logger.LogInformation("Successfully retrieved order details for ID: {OrderId}", id);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting order details for ID: {OrderId}", id);
                TempData["ErrorMessage"] = "获取订单详情时发生错误";
                return RedirectToAction(nameof(Index));
            }
        }

        // GET: Order/Create
        public async Task<IActionResult> Create(int? opportunityId = null)
        {
            try
            {
                var viewModel = new CreateOrderViewModel();

                // 如果指定了商机ID，预填充商机信息
                if (opportunityId.HasValue)
                {
                    await PreFillFromOpportunity(viewModel, opportunityId.Value);
                }

                await LoadDropdownData(viewModel);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading create order page");
                TempData["ErrorMessage"] = "加载创建订单页面时发生错误";
                return RedirectToAction(nameof(Index));
            }
        }

        // POST: Order/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CreateOrderViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    await LoadDropdownData(model);
                    return View(model);
                }

                _logger.LogInformation("Creating new order: {OrderName}", model.Name);

                // 获取当前用户ID作为创建人
                var currentUserId = GetCurrentUserId();

                // 生成订单编号
                var orderCode = GenerateOrderCode();

                var createOrderDto = new CreateOrderDTO
                {
                    Name = model.Name,
                    Code = orderCode,
                    CustomerName = model.CustomerName,
                    ContactName = string.IsNullOrWhiteSpace(model.ContactName) ? null : model.ContactName,
                    ContactPhone = string.IsNullOrWhiteSpace(model.ContactPhone) ? null : model.ContactPhone,
                    Amount = model.Amount,
                    Currency = model.Currency,
                    SettlementAmount = model.SettlementAmount,
                    AccountManagerId = model.AccountManagerId,
                    DepartmentId = model.DepartmentId,
                    SignDate = model.SignDate,
                    Status = model.Status,
                    ProjectManagerId = model.ProjectManagerId > 0 ? model.ProjectManagerId : null,
                    PerformanceDepartmentId = model.PerformanceDepartmentId > 0 ? model.PerformanceDepartmentId : null,
                    OpportunityId = model.OpportunityId > 0 ? model.OpportunityId : null,
                    CreatedById = currentUserId
                };

                var response = await _orderService.CreateOrderAsync(createOrderDto);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully created order: {OrderName}", model.Name);
                    TempData["SuccessMessage"] = "订单创建成功";
                    return RedirectToAction(nameof(Index));
                }
                else
                {
                    _logger.LogWarning("Failed to create order: {OrderName}. Message: {Message}", model.Name, response.Message);
                    ModelState.AddModelError(string.Empty, response.Message ?? "创建订单失败");
                    await LoadDropdownData(model);
                    return View(model);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating order: {OrderName}", model.Name);
                ModelState.AddModelError(string.Empty, "创建订单时发生错误");
                await LoadDropdownData(model);
                return View(model);
            }
        }

        private async Task LoadDropdownData<T>(T viewModel) where T : class
        {
            try
            {
                // 加载用户列表
                var usersResponse = await _userService.GetUsersAsync();
                var users = new List<UserSelectItem>();

                if (usersResponse.Success && usersResponse.Data != null)
                {
                    users = usersResponse.Data.Select(u => new UserSelectItem
                    {
                        Id = u.Id,
                        Name = u.Name,
                        Username = u.Username
                    }).ToList();
                }

                // 加载部门列表
                var departmentsResponse = await _departmentService.GetDepartmentsAsync();
                var departments = new List<DepartmentSelectItem>();

                if (departmentsResponse.Success && departmentsResponse.Data != null)
                {
                    departments = departmentsResponse.Data.Select(d => new DepartmentSelectItem
                    {
                        Id = d.Id,
                        Name = d.Name
                    }).ToList();
                }

                // 加载商机列表（仅加载成交状态的商机）
                var opportunities = new List<OpportunitySelectItem>();
                try
                {
                    var opportunitiesResponse = await _opportunityService.GetOpportunitiesAsync();
                    if (opportunitiesResponse.Success && opportunitiesResponse.Data != null)
                    {
                        opportunities = opportunitiesResponse.Data
                            .Where(o => o.Status == "成交") // 只显示成交的商机
                            .Select(o => new OpportunitySelectItem
                            {
                                Id = o.Id,
                                Name = o.Name,
                                CustomerName = o.CustomerName,
                                Status = o.Status
                            }).ToList();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error loading opportunities for dropdown");
                }

                // 使用反射设置属性
                var usersProperty = typeof(T).GetProperty("Users");
                var departmentsProperty = typeof(T).GetProperty("Departments");
                var opportunitiesProperty = typeof(T).GetProperty("Opportunities");

                usersProperty?.SetValue(viewModel, users);
                departmentsProperty?.SetValue(viewModel, departments);
                opportunitiesProperty?.SetValue(viewModel, opportunities);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading dropdown data");
            }
        }

        // GET: Order/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            try
            {
                _logger.LogInformation("Loading edit order page for ID: {OrderId}", id);

                var response = await _orderService.GetOrderByIdAsync(id);

                if (!response.Success || response.Data == null)
                {
                    _logger.LogWarning("Order not found for edit: {OrderId}", id);
                    TempData["ErrorMessage"] = "订单不存在";
                    return RedirectToAction(nameof(Index));
                }

                var viewModel = new EditOrderViewModel
                {
                    Id = response.Data.Id,
                    Name = response.Data.Name,
                    Code = response.Data.Code,
                    CustomerName = response.Data.CustomerName,
                    ContactName = response.Data.ContactName,
                    ContactPhone = response.Data.ContactPhone,
                    Amount = response.Data.Amount,
                    Currency = response.Data.Currency,
                    SettlementAmount = response.Data.SettlementAmount,
                    AccountManagerId = response.Data.AccountManagerId,
                    DepartmentId = response.Data.DepartmentId,
                    SignDate = response.Data.SignDate,
                    Status = response.Data.Status,
                    ProjectManagerId = response.Data.ProjectManagerId,
                    PerformanceDepartmentId = response.Data.PerformanceDepartmentId
                };

                await LoadDropdownData(viewModel);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading edit order page for ID: {OrderId}", id);
                TempData["ErrorMessage"] = "加载编辑订单页面时发生错误";
                return RedirectToAction(nameof(Index));
            }
        }

        // POST: Order/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, EditOrderViewModel model)
        {
            try
            {
                if (id != model.Id)
                {
                    return BadRequest();
                }

                if (!ModelState.IsValid)
                {
                    await LoadDropdownData(model);
                    return View(model);
                }

                _logger.LogInformation("Updating order: {OrderId}", id);

                var updateOrderDto = new UpdateOrderDTO
                {
                    Id = model.Id,
                    Name = model.Name,
                    Code = model.Code,
                    CustomerName = model.CustomerName,
                    ContactName = string.IsNullOrWhiteSpace(model.ContactName) ? null : model.ContactName,
                    ContactPhone = string.IsNullOrWhiteSpace(model.ContactPhone) ? null : model.ContactPhone,
                    Amount = model.Amount,
                    Currency = model.Currency,
                    SettlementAmount = model.SettlementAmount,
                    AccountManagerId = model.AccountManagerId,
                    DepartmentId = model.DepartmentId,
                    SignDate = model.SignDate,
                    Status = model.Status,
                    ProjectManagerId = model.ProjectManagerId > 0 ? model.ProjectManagerId : null,
                    PerformanceDepartmentId = model.PerformanceDepartmentId > 0 ? model.PerformanceDepartmentId : null
                };

                var response = await _orderService.UpdateOrderAsync(id, updateOrderDto);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully updated order: {OrderId}", id);
                    TempData["SuccessMessage"] = "订单更新成功";
                    return RedirectToAction(nameof(Details), new { id = id });
                }
                else
                {
                    _logger.LogWarning("Failed to update order: {OrderId}. Message: {Message}", id, response.Message);
                    ModelState.AddModelError(string.Empty, response.Message ?? "更新订单失败");
                    await LoadDropdownData(model);
                    return View(model);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating order: {OrderId}", id);
                ModelState.AddModelError(string.Empty, "更新订单时发生错误");
                await LoadDropdownData(model);
                return View(model);
            }
        }

        // POST: Order/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                _logger.LogInformation("Deleting order: {OrderId}", id);

                var response = await _orderService.DeleteOrderAsync(id);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully deleted order: {OrderId}", id);
                    TempData["SuccessMessage"] = "订单删除成功";
                }
                else
                {
                    _logger.LogWarning("Failed to delete order: {OrderId}. Message: {Message}", id, response.Message);
                    TempData["ErrorMessage"] = response.Message ?? "删除订单失败";
                }

                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting order: {OrderId}", id);
                TempData["ErrorMessage"] = "删除订单时发生错误";
                return RedirectToAction(nameof(Index));
            }
        }

        private async Task PreFillFromOpportunity(CreateOrderViewModel viewModel, int opportunityId)
        {
            try
            {
                var opportunityResponse = await _opportunityService.GetOpportunityByIdAsync(opportunityId);
                if (opportunityResponse.Success && opportunityResponse.Data != null)
                {
                    var opportunity = opportunityResponse.Data;

                    // 预填充商机信息到订单表单
                    viewModel.OpportunityId = opportunity.Id;
                    viewModel.Name = $"{opportunity.CustomerName} - {opportunity.Name}";
                    viewModel.CustomerName = opportunity.CustomerName;
                    viewModel.ContactName = opportunity.ContactName;
                    viewModel.ContactPhone = opportunity.ContactPhone;

                    _logger.LogInformation("Pre-filled order form from opportunity: {OpportunityId}", opportunityId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error pre-filling from opportunity: {OpportunityId}", opportunityId);
            }
        }

        private string GenerateOrderCode()
        {
            // 生成订单编号：ORD + 年月日 + 6位随机数
            var dateStr = DateTime.Now.ToString("yyyyMMdd");
            var random = new Random().Next(100000, 999999);
            return $"ORD{dateStr}{random}";
        }

        private int GetCurrentUserId()
        {
            // 从Claims中获取用户ID
            var userIdClaim = User.Claims.FirstOrDefault(c => c.Type == "UserId")?.Value;
            if (int.TryParse(userIdClaim, out int userId))
            {
                return userId;
            }

            // 如果获取失败，返回默认值（这里应该根据实际情况处理）
            return 1; // 临时返回1，实际应用中需要更好的错误处理
        }
    }
}
