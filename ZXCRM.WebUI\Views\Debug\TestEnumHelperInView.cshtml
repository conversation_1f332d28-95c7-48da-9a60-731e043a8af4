@using ZXCRM.WebUI.Helpers
@{
    ViewData["Title"] = "视图中EnumHelper测试";
}

<div class="container-fluid">
    <h2>视图中EnumHelper测试</h2>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3>金额格式化测试</h3>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <td>FormatAmount(100000, "CNY"):</td>
                            <td><strong>@EnumHelper.FormatAmount(100000, "CNY")</strong></td>
                        </tr>
                        <tr>
                            <td>FormatAmount(100000, "USD"):</td>
                            <td><strong>@EnumHelper.FormatAmount(100000, "USD")</strong></td>
                        </tr>
                        <tr>
                            <td>FormatAmount(0, "CNY"):</td>
                            <td><strong>@EnumHelper.FormatAmount(0, "CNY")</strong></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3>状态文本测试</h3>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <td>GetOrderStatusText("New"):</td>
                            <td><strong>@EnumHelper.GetOrderStatusText("New")</strong></td>
                        </tr>
                        <tr>
                            <td>GetOrderStatusText("InProgress"):</td>
                            <td><strong>@EnumHelper.GetOrderStatusText("InProgress")</strong></td>
                        </tr>
                        <tr>
                            <td>GetOrderStatusText("Completed"):</td>
                            <td><strong>@EnumHelper.GetOrderStatusText("Completed")</strong></td>
                        </tr>
                        <tr>
                            <td>GetOrderStatusText("Cancelled"):</td>
                            <td><strong>@EnumHelper.GetOrderStatusText("Cancelled")</strong></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3>徽章显示测试</h3>
                </div>
                <div class="card-body">
                    <h5>金额徽章:</h5>
                    <p>
                        <span class="badge bg-success badge-xs text-white">@EnumHelper.FormatAmount(100000, "CNY")</span>
                        <span class="badge bg-success badge-xs text-white">@EnumHelper.FormatAmount(200000, "USD")</span>
                        <span class="badge bg-success badge-xs text-white">@EnumHelper.FormatAmount(0, "CNY")</span>
                    </p>

                    <h5>状态徽章:</h5>
                    <p>
                        <span class="badge <EMAIL>("New") badge-xs text-white">
                            <i class="fas fa-plus"></i>
                            @EnumHelper.GetOrderStatusText("New")
                        </span>
                        <span class="badge <EMAIL>("InProgress") badge-xs text-white">
                            <i class="fas fa-play"></i>
                            @EnumHelper.GetOrderStatusText("InProgress")
                        </span>
                        <span class="badge <EMAIL>("Completed") badge-xs text-white">
                            <i class="fas fa-check-circle"></i>
                            @EnumHelper.GetOrderStatusText("Completed")
                        </span>
                        <span class="badge <EMAIL>("Cancelled") badge-xs text-white">
                            <i class="fas fa-times"></i>
                            @EnumHelper.GetOrderStatusText("Cancelled")
                        </span>
                    </p>
                    
                    <h5>模拟表格行:</h5>
                    <table class="table table-striped table-sm">
                        <thead>
                            <tr>
                                <th>订单名称</th>
                                <th>金额</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>测试订单1</td>
                                <td>
                                    <span class="badge bg-success badge-xs text-white">
                                        @EnumHelper.FormatAmount(100000, "CNY")
                                    </span>
                                </td>
                                <td>
                                    <span class="badge <EMAIL>("New") badge-xs text-white">
                                        <i class="fas fa-plus"></i>
                                        @EnumHelper.GetOrderStatusText("New")
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td>测试订单2</td>
                                <td>
                                    <span class="badge bg-success badge-xs text-white">
                                        @EnumHelper.FormatAmount(200000, "USD")
                                    </span>
                                </td>
                                <td>
                                    <span class="badge <EMAIL>("InProgress") badge-xs text-white">
                                        <i class="fas fa-play"></i>
                                        @EnumHelper.GetOrderStatusText("InProgress")
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td>测试订单3</td>
                                <td>
                                    <span class="badge bg-success badge-xs text-white">
                                        @EnumHelper.FormatAmount(300000, "CNY")
                                    </span>
                                </td>
                                <td>
                                    <span class="badge <EMAIL>("Completed") badge-xs text-white">
                                        <i class="fas fa-check-circle"></i>
                                        @EnumHelper.GetOrderStatusText("Completed")
                                    </span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="mt-3">
    <a href="/Debug/CheckPageData" class="btn btn-primary" target="_blank">检查页面数据</a>
    <a href="/Debug/TestEnumHelper" class="btn btn-info" target="_blank">测试EnumHelper</a>
    <a href="/Order" class="btn btn-secondary">返回订单列表</a>
</div>
