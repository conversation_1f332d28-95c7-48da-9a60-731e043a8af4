@model ZXCRM.WebUI.Models.ViewModels.EditPaymentViewModel
@{
    ViewData["Title"] = "编辑款项";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">编辑款项</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a asp-controller="Dashboard" asp-action="Index">首页</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Payment" asp-action="Index">款项管理</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Payment" asp-action="Details" asp-route-id="@Model.Id">款项详情</a></li>
                    <li class="breadcrumb-item active">编辑款项</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-edit"></i> 编辑款项信息
                        </h3>
                        <div class="card-tools">
                            <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info btn-sm">
                                <i class="fas fa-eye"></i> 查看详情
                            </a>
                        </div>
                    </div>
                    <form asp-action="Edit" method="post">
                        <input asp-for="Id" type="hidden" />
                        <div class="card-body">
                            <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                            <div class="row">
                                <div class="col-md-6">
                                    <!-- 款项名称 -->
                                    <div class="form-group">
                                        <label asp-for="Name" class="form-label required"></label>
                                        <input asp-for="Name" class="form-control" placeholder="请输入款项名称" />
                                        <span asp-validation-for="Name" class="text-danger"></span>
                                    </div>

                                    <!-- 款项编号 -->
                                    <div class="form-group">
                                        <label asp-for="Code" class="form-label required"></label>
                                        <input asp-for="Code" class="form-control" readonly />
                                        <span asp-validation-for="Code" class="text-danger"></span>
                                        <small class="form-text text-muted">款项编号不可修改</small>
                                    </div>

                                    <!-- 关联订单 -->
                                    <div class="form-group">
                                        <label asp-for="OrderId" class="form-label required"></label>
                                        <select asp-for="OrderId" class="form-control" disabled>
                                            <option value="">请选择关联订单</option>
                                            @foreach (var order in Model.Orders)
                                            {
                                                <option value="@order.Id">
                                                    @order.Name (@order.Code) - @order.CustomerName - @order.Amount.ToString("N2") @order.Currency
                                                </option>
                                            }
                                        </select>
                                        <input asp-for="OrderId" type="hidden" />
                                        <span asp-validation-for="OrderId" class="text-danger"></span>
                                        <small class="form-text text-muted">关联订单不可修改</small>
                                    </div>

                                    <!-- 款项类型 -->
                                    <div class="form-group">
                                        <label asp-for="PaymentType" class="form-label required"></label>
                                        <select asp-for="PaymentType" class="form-control">
                                            <option value="">请选择款项类型</option>
                                            <option value="首付">首付</option>
                                            <option value="2付">2付</option>
                                            <option value="3付">3付</option>
                                            <option value="4付">4付</option>
                                            <option value="5付">5付</option>
                                            <option value="尾款">尾款</option>
                                            <option value="全款">全款</option>
                                        </select>
                                        <span asp-validation-for="PaymentType" class="text-danger"></span>
                                    </div>

                                    <!-- 款项金额 -->
                                    <div class="form-group">
                                        <label asp-for="Amount" class="form-label required"></label>
                                        <div class="input-group">
                                            <input asp-for="Amount" class="form-control" placeholder="0.00" step="0.01" />
                                            <div class="input-group-append">
                                                <select asp-for="Currency" class="form-control">
                                                    <option value="CNY">CNY</option>
                                                    <option value="USD">USD</option>
                                                    <option value="JPY">JPY</option>
                                                    <option value="EUR">EUR</option>
                                                </select>
                                            </div>
                                        </div>
                                        <span asp-validation-for="Amount" class="text-danger"></span>
                                    </div>

                                    <!-- 结算金额 -->
                                    <div class="form-group">
                                        <label asp-for="SettlementAmount" class="form-label required"></label>
                                        <input asp-for="SettlementAmount" class="form-control" placeholder="0.00" step="0.01" />
                                        <span asp-validation-for="SettlementAmount" class="text-danger"></span>
                                        <small class="form-text text-muted">结算金额通常与款项金额相同</small>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <!-- 预期回款日期 -->
                                    <div class="form-group">
                                        <label asp-for="ExpectedPaymentDate" class="form-label"></label>
                                        <input asp-for="ExpectedPaymentDate" class="form-control" type="date" />
                                        <span asp-validation-for="ExpectedPaymentDate" class="text-danger"></span>
                                        <small class="form-text text-muted">预期的回款日期</small>
                                    </div>

                                    <!-- 实际回款日期 -->
                                    <div class="form-group">
                                        <label asp-for="ActualPaymentDate" class="form-label"></label>
                                        <input asp-for="ActualPaymentDate" class="form-control" type="date" />
                                        <span asp-validation-for="ActualPaymentDate" class="text-danger"></span>
                                        <small class="form-text text-muted">如果已回款，请填写实际回款日期</small>
                                    </div>

                                    <!-- 开票状态 -->
                                    <div class="form-group">
                                        <label asp-for="InvoiceStatus" class="form-label required"></label>
                                        <select asp-for="InvoiceStatus" class="form-control">
                                            <option value="未开票">未开票</option>
                                            <option value="已开票">已开票</option>
                                            <option value="已寄出">已寄出</option>
                                        </select>
                                        <span asp-validation-for="InvoiceStatus" class="text-danger"></span>
                                    </div>

                                    <!-- 开票日期 -->
                                    <div class="form-group">
                                        <label asp-for="InvoiceDate" class="form-label"></label>
                                        <input asp-for="InvoiceDate" class="form-control" type="date" />
                                        <span asp-validation-for="InvoiceDate" class="text-danger"></span>
                                        <small class="form-text text-muted">如果已开票，请填写开票日期</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card-footer">
                            <div class="row">
                                <div class="col-md-6">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> 保存更改
                                    </button>
                                    <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-secondary ml-2">
                                        <i class="fas fa-times"></i> 取消
                                    </a>
                                </div>
                                <div class="col-md-6 text-right">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        标有 <span class="text-danger">*</span> 的字段为必填项
                                    </small>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="col-md-4">
                <!-- 编辑说明 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-info-circle"></i> 编辑说明
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> 注意事项</h6>
                            <ul class="mb-0">
                                <li>款项编号和关联订单不可修改</li>
                                <li>修改款项金额可能影响相关的发票记录</li>
                                <li>更改开票状态会影响发票管理流程</li>
                                <li>实际回款日期影响回款统计</li>
                            </ul>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb"></i> 提示</h6>
                            <p class="mb-0">
                                如果款项已有发票记录，建议谨慎修改款项金额。
                                状态变更会记录在系统日志中。
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 快捷操作 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-bolt"></i> 快捷操作
                        </h3>
                    </div>
                    <div class="card-body">
                        <button type="button" class="btn btn-outline-primary btn-sm btn-block" onclick="copyAmount()">
                            <i class="fas fa-copy"></i> 复制款项金额到结算金额
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm btn-block" onclick="markAsPaid()">
                            <i class="fas fa-check"></i> 标记为已回款
                        </button>
                        <button type="button" class="btn btn-outline-info btn-sm btn-block" onclick="markAsInvoiced()">
                            <i class="fas fa-file-invoice"></i> 标记为已开票
                        </button>
                    </div>
                </div>

                <!-- 操作历史 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-history"></i> 操作历史
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <div class="time-label">
                                <span class="bg-info">最近操作</span>
                            </div>
                            <div>
                                <i class="fas fa-edit bg-blue"></i>
                                <div class="timeline-item">
                                    <span class="time"><i class="fas fa-clock"></i> 正在编辑</span>
                                    <h3 class="timeline-header">编辑款项信息</h3>
                                    <div class="timeline-body">
                                        当前正在编辑款项信息，请确认所有字段填写正确。
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        function copyAmount() {
            const amount = document.getElementById('Amount').value;
            if (amount) {
                document.getElementById('SettlementAmount').value = amount;
                toastr.info('已复制款项金额到结算金额');
            }
        }

        function markAsPaid() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('ActualPaymentDate').value = today;
            toastr.info('已设置实际回款日期为今天');
        }

        function markAsInvoiced() {
            document.getElementById('InvoiceStatus').value = '已开票';
            const today = new Date().toISOString().split('T')[0];
            if (!document.getElementById('InvoiceDate').value) {
                document.getElementById('InvoiceDate').value = today;
            }
            toastr.info('已标记为已开票');
        }

        // 表单验证增强
        $(document).ready(function() {
            // 金额输入验证
            $('#Amount, #SettlementAmount').on('input', function() {
                const value = parseFloat($(this).val());
                if (value <= 0) {
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });

            // 开票状态变更处理
            $('#InvoiceStatus').on('change', function() {
                const status = $(this).val();
                if (status === '已开票' || status === '已寄出') {
                    if (!$('#InvoiceDate').val()) {
                        const today = new Date().toISOString().split('T')[0];
                        $('#InvoiceDate').val(today);
                        toastr.info('已自动设置开票日期为今天');
                    }
                } else if (status === '未开票') {
                    $('#InvoiceDate').val('');
                }
            });

            // 实际回款日期变更提醒
            $('#ActualPaymentDate').on('change', function() {
                const paymentDate = $(this).val();
                if (paymentDate) {
                    toastr.success('已设置实际回款日期，款项状态将更新为已回款');
                }
            });
        });
    </script>
}

@section Styles {
    <style>
        .required::after {
            content: " *";
            color: red;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .is-invalid {
            border-color: #dc3545;
        }

        .timeline {
            position: relative;
            margin: 0 0 30px 0;
            padding: 0;
            list-style: none;
        }

        .timeline:before {
            content: '';
            position: absolute;
            top: 0;
            left: 25px;
            height: 100%;
            width: 2px;
            background: #dee2e6;
        }

        .timeline > div {
            position: relative;
            margin-right: 10px;
            margin-bottom: 15px;
        }

        .timeline > div > .timeline-item {
            box-shadow: 0 1px 1px rgba(0,0,0,0.1);
            border-radius: 3px;
            margin-top: 0;
            background: #fff;
            color: #444;
            margin-left: 60px;
            margin-right: 15px;
            padding: 0;
            position: relative;
        }

        .timeline > div > .fas {
            width: 30px;
            height: 30px;
            font-size: 15px;
            line-height: 30px;
            position: absolute;
            color: #666;
            background: #f4f4f4;
            border-radius: 50%;
            text-align: center;
            left: 18px;
            top: 0;
        }
    </style>
}
