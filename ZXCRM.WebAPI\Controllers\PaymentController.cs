using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ZXCRM.Service.DTOs;
using ZXCRM.Service.Interface;

namespace ZXCRM.WebAPI.Controllers
{
    [Authorize]
    public class PaymentController : BaseController
    {
        private readonly IPaymentService _paymentService;
        private readonly ILogger<PaymentController> _logger;

        public PaymentController(IPaymentService paymentService, ILogger<PaymentController> logger)
        {
            _paymentService = paymentService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> GetPayments()
        {
            try
            {
                _logger.LogInformation("Getting all payments");
                var payments = await _paymentService.GetAllPaymentsAsync();
                return Success(payments, "获取款项列表成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payments");
                return Failure("获取款项列表失败");
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetPayment(int id)
        {
            try
            {
                _logger.LogInformation("Getting payment by ID: {PaymentId}", id);
                var payment = await _paymentService.GetPaymentByIdAsync(id);

                if (payment == null)
                {
                    _logger.LogWarning("Payment not found: {PaymentId}", id);
                    return Failure($"款项 {id} 不存在");
                }

                return Success(payment, "获取款项详情成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payment by ID: {PaymentId}", id);
                return Failure("获取款项详情失败");
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreatePayment([FromBody] CreatePaymentDTO createPaymentDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Failure("数据验证失败", ModelState);
                }

                _logger.LogInformation("Creating new payment for order: {OrderId}", createPaymentDto.OrderId);
                var payment = await _paymentService.CreatePaymentAsync(createPaymentDto);

                return Success(payment, "创建款项成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating payment for order: {OrderId}", createPaymentDto.OrderId);
                return Failure(ex.Message.Contains("不存在") ? ex.Message : "创建款项失败");
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdatePayment(int id, [FromBody] UpdatePaymentDTO updatePaymentDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Failure("数据验证失败", ModelState);
                }

                // 设置ID到DTO中
                updatePaymentDto.Id = id;

                _logger.LogInformation("Updating payment: {PaymentId}", id);
                var payment = await _paymentService.UpdatePaymentAsync(updatePaymentDto);

                if (payment == null)
                {
                    _logger.LogWarning("Payment not found for update: {PaymentId}", id);
                    return Failure($"款项 {id} 不存在");
                }

                return Success(payment, "更新款项成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating payment: {PaymentId}", id);
                return Failure("更新款项失败");
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeletePayment(int id)
        {
            try
            {
                _logger.LogInformation("Deleting payment: {PaymentId}", id);
                var success = await _paymentService.DeletePaymentAsync(id);

                if (!success)
                {
                    _logger.LogWarning("Payment not found for deletion: {PaymentId}", id);
                    return Failure($"款项 {id} 不存在");
                }

                return Success(null, "删除款项成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting payment: {PaymentId}", id);
                return Failure(ex.Message.Contains("关联发票") ? ex.Message : "删除款项失败");
            }
        }

        [HttpGet("order/{orderId}")]
        public async Task<IActionResult> GetPaymentsByOrder(int orderId)
        {
            try
            {
                _logger.LogInformation("Getting payments by order: {OrderId}", orderId);
                var payments = await _paymentService.GetPaymentsByOrderIdAsync(orderId);
                return Success(payments, "获取订单款项成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payments by order: {OrderId}", orderId);
                return Failure("获取订单款项失败");
            }
        }
    }
}
