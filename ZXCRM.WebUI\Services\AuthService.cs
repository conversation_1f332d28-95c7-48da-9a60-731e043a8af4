using ZXCRM.Service.DTOs;
using ZXCRM.WebUI.Models;

namespace ZXCRM.WebUI.Services
{
    public class AuthService : IAuthService
    {
        private readonly IApiService _apiService;
        private readonly ILogger<AuthService> _logger;

        public AuthService(IApiService apiService, ILogger<AuthService> logger)
        {
            _apiService = apiService;
            _logger = logger;
        }

        public async Task<ApiResponse<TokenDTO>> LoginAsync(LoginDTO request)
        {
            try
            {
                _logger.LogInformation("Attempting login for user: {Username}", request.Username);

                var response = await _apiService.PostAsync<TokenDTO>("api/auth/login", request);

                if (response.Success && response.Data != null)
                {
                    // Set the auth token for subsequent API calls
                    _apiService.SetAuthToken(response.Data.AccessToken);
                    _logger.LogInformation("Login successful for user: {Username}", request.Username);
                }
                else
                {
                    _logger.LogWarning("Login failed for user: {Username}. Message: {Message}",
                        request.Username, response.Message);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login for user: {Username}", request.Username);
                return new ApiResponse<TokenDTO>
                {
                    Success = false,
                    Message = $"Login failed: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<object>> LogoutAsync()
        {
            try
            {
                _logger.LogInformation("Attempting logout");

                var response = await _apiService.PostAsync<object>("api/auth/logout", new { });

                // Clear the auth token regardless of API response
                _apiService.ClearAuthToken();

                _logger.LogInformation("Logout completed");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout");

                // Clear the auth token even if API call fails
                _apiService.ClearAuthToken();

                return new ApiResponse<object>
                {
                    Success = false,
                    Message = $"Logout failed: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<UserDTO>> GetCurrentUserAsync()
        {
            try
            {
                _logger.LogInformation("Getting current user information");

                var response = await _apiService.GetAsync<UserDTO>("api/auth/current-user");

                if (response.Success)
                {
                    _logger.LogInformation("Successfully retrieved current user information");
                }
                else
                {
                    _logger.LogWarning("Failed to retrieve current user information: {Message}", response.Message);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting current user information");
                return new ApiResponse<UserDTO>
                {
                    Success = false,
                    Message = $"Failed to get user information: {ex.Message}"
                };
            }
        }

        public async Task<bool> ValidateTokenAsync(string token)
        {
            try
            {
                _logger.LogInformation("Validating token");

                // Set the token for validation
                _apiService.SetAuthToken(token);

                var response = await _apiService.GetAsync<object>("api/auth/validate");

                if (response.Success)
                {
                    _logger.LogInformation("Token validation successful");
                    return true;
                }
                else
                {
                    _logger.LogWarning("Token validation failed: {Message}", response.Message);
                    _apiService.ClearAuthToken();
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating token");
                _apiService.ClearAuthToken();
                return false;
            }
        }
    }
}
