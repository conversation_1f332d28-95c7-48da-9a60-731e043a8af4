using Microsoft.EntityFrameworkCore;
using ZXCRM.Data.Entities;

namespace ZXCRM.Data.Context
{
    public class ZXCRMDbContext : DbContext
    {
        public ZXCRMDbContext(DbContextOptions<ZXCRMDbContext> options) : base(options)
        {
        }

        // 用户和部门管理
        public DbSet<Department> Departments { get; set; }
        public DbSet<User> Users { get; set; }
        public DbSet<Permission> Permissions { get; set; }
        public DbSet<UserPermission> UserPermissions { get; set; }

        // 商机管理
        public DbSet<Opportunity> Opportunities { get; set; }

        // 订单管理
        public DbSet<Order> Orders { get; set; }

        // 款项管理
        public DbSet<Payment> Payments { get; set; }

        // 发票管理
        public DbSet<Invoice> Invoices { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // 配置User实体
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Username).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Password).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Gender).HasMaxLength(10);
                entity.Property(e => e.Email).HasMaxLength(100);
                entity.Property(e => e.Phone).HasMaxLength(20);
                entity.Property(e => e.Role).IsRequired().HasMaxLength(50).HasDefaultValue("Employee");
                entity.Property(e => e.Status).IsRequired().HasMaxLength(20).HasDefaultValue("Active");
                entity.Property(e => e.CreatedAt).IsRequired();
                entity.Property(e => e.UpdatedAt).IsRequired();
                entity.Property(e => e.IsDeleted).IsRequired().HasDefaultValue(false);
                entity.HasQueryFilter(e => !e.IsDeleted);
            });

            // 配置Department实体
            modelBuilder.Entity<Department>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Code).IsRequired().HasMaxLength(20);
                entity.Property(e => e.Status).IsRequired().HasMaxLength(20).HasDefaultValue("Active");
                entity.Property(e => e.CreatedAt).IsRequired();
                entity.Property(e => e.UpdatedAt).IsRequired();
                entity.Property(e => e.IsDeleted).IsRequired().HasDefaultValue(false);
                entity.HasQueryFilter(e => !e.IsDeleted);
            });

            // 配置Permission实体
            modelBuilder.Entity<Permission>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Code).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Description).HasMaxLength(200);
            });

            // 配置UserPermission实体
            modelBuilder.Entity<UserPermission>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.ModuleType).IsRequired().HasMaxLength(50);
                // 移除查询过滤器以避免与User的查询过滤器冲突
            });

            // 配置Order实体
            modelBuilder.Entity<Order>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Code).IsRequired().HasMaxLength(50);
                entity.Property(e => e.CustomerName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.ContactName).HasMaxLength(100);
                entity.Property(e => e.ContactPhone).HasMaxLength(20);
                entity.Property(e => e.Amount).HasPrecision(18, 2);
                entity.Property(e => e.Currency).IsRequired().HasMaxLength(20);
                entity.Property(e => e.SettlementAmount).HasPrecision(18, 2);
                entity.Property(e => e.Status).IsRequired().HasMaxLength(50);
                entity.Property(e => e.CreatedAt).IsRequired();
                entity.Property(e => e.UpdatedAt).IsRequired();
                entity.Property(e => e.IsDeleted).IsRequired().HasDefaultValue(false);
                entity.HasQueryFilter(e => !e.IsDeleted);
            });

            // 配置Payment实体
            modelBuilder.Entity<Payment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Code).IsRequired().HasMaxLength(50);
                entity.Property(e => e.PaymentType).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Amount).HasPrecision(18, 2);
                entity.Property(e => e.Currency).IsRequired().HasMaxLength(20);
                entity.Property(e => e.SettlementAmount).HasPrecision(18, 2);
                entity.Property(e => e.PaymentStatus).IsRequired().HasMaxLength(20).HasDefaultValue("Pending");
                entity.Property(e => e.InvoiceStatus).IsRequired().HasMaxLength(50);
                entity.Property(e => e.CreatedAt).IsRequired();
                entity.Property(e => e.UpdatedAt).IsRequired();
                entity.Property(e => e.IsDeleted).IsRequired().HasDefaultValue(false);
                entity.HasQueryFilter(e => !e.IsDeleted);
            });

            // 配置Invoice实体
            modelBuilder.Entity<Invoice>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Company).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Type).IsRequired().HasMaxLength(50);
                entity.Property(e => e.TaxRate).HasPrecision(5, 4); // 支持99.9999%的税率
                entity.Property(e => e.Amount).HasPrecision(18, 2);
                entity.Property(e => e.Status).IsRequired().HasMaxLength(20).HasDefaultValue("Normal");
                entity.Property(e => e.Content).HasMaxLength(500);
                entity.Property(e => e.Code).HasMaxLength(50);
                entity.Property(e => e.ReceiverName).HasMaxLength(100);
                entity.Property(e => e.ReceiverPhone).HasMaxLength(20);
                entity.Property(e => e.MailingAddress).HasMaxLength(500);
                entity.Property(e => e.ApplicantPhone).HasMaxLength(20);
                entity.Property(e => e.CustomerEmail).HasMaxLength(100);
                entity.Property(e => e.CreatedAt).IsRequired();
                entity.Property(e => e.UpdatedAt).IsRequired();
                entity.Property(e => e.IsDeleted).IsRequired().HasDefaultValue(false);
                entity.HasQueryFilter(e => !e.IsDeleted);
            });

            // 配置Opportunity实体
            modelBuilder.Entity<Opportunity>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.Property(e => e.CustomerName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.ContactName).HasMaxLength(100);
                entity.Property(e => e.ContactPhone).HasMaxLength(20);
                entity.Property(e => e.Content).HasMaxLength(1000);
                entity.Property(e => e.Status).IsRequired().HasMaxLength(50);
                entity.Property(e => e.CreatedAt).IsRequired();
                entity.Property(e => e.UpdatedAt).IsRequired();
                entity.Property(e => e.IsDeleted).IsRequired().HasDefaultValue(false);
                entity.HasQueryFilter(e => !e.IsDeleted);
            });

            // 配置关系
            // 部门自关联
            modelBuilder.Entity<Department>()
                .HasOne(d => d.Parent)
                .WithMany(d => d.Children)
                .HasForeignKey(d => d.ParentId)
                .OnDelete(DeleteBehavior.Restrict);

            // 用户与部门关系
            modelBuilder.Entity<User>()
                .HasOne(u => u.Department)
                .WithMany(d => d.Users)
                .HasForeignKey(u => u.DepartmentId)
                .OnDelete(DeleteBehavior.Restrict);

            // 用户权限关系
            modelBuilder.Entity<UserPermission>()
                .HasOne(up => up.User)
                .WithMany(u => u.UserPermissions)
                .HasForeignKey(up => up.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<UserPermission>()
                .HasOne(up => up.Permission)
                .WithMany(p => p.UserPermissions)
                .HasForeignKey(up => up.PermissionId)
                .OnDelete(DeleteBehavior.Cascade);

            // 商机与用户关系
            modelBuilder.Entity<Opportunity>()
                .HasOne(o => o.CreatedBy)
                .WithMany(u => u.Opportunities)
                .HasForeignKey(o => o.CreatedById)
                .OnDelete(DeleteBehavior.Restrict);

            // 订单与商机关系
            modelBuilder.Entity<Order>()
                .HasOne(o => o.Opportunity)
                .WithMany(op => op.Orders)
                .HasForeignKey(o => o.OpportunityId)
                .OnDelete(DeleteBehavior.Restrict);

            // 订单与用户关系（客户经理）
            modelBuilder.Entity<Order>()
                .HasOne(o => o.AccountManager)
                .WithMany(u => u.ManagedOrders)
                .HasForeignKey(o => o.AccountManagerId)
                .OnDelete(DeleteBehavior.Restrict);

            // 订单与用户关系（项目经理）
            modelBuilder.Entity<Order>()
                .HasOne(o => o.ProjectManager)
                .WithMany(u => u.ProjectOrders)
                .HasForeignKey(o => o.ProjectManagerId)
                .OnDelete(DeleteBehavior.Restrict);

            // 订单与部门关系（创建部门）
            modelBuilder.Entity<Order>()
                .HasOne(o => o.Department)
                .WithMany(d => d.Orders)
                .HasForeignKey(o => o.DepartmentId)
                .OnDelete(DeleteBehavior.Restrict);

            // 订单与部门关系（业绩归属部门）
            modelBuilder.Entity<Order>()
                .HasOne(o => o.PerformanceDepartment)
                .WithMany(d => d.PerformanceOrders)
                .HasForeignKey(o => o.PerformanceDepartmentId)
                .OnDelete(DeleteBehavior.Restrict);

            // 款项与订单关系
            modelBuilder.Entity<Payment>()
                .HasOne(p => p.Order)
                .WithMany(o => o.Payments)
                .HasForeignKey(p => p.OrderId)
                .OnDelete(DeleteBehavior.Cascade);

            // 发票与款项关系
            modelBuilder.Entity<Invoice>()
                .HasOne(i => i.Payment)
                .WithMany(p => p.Invoices)
                .HasForeignKey(i => i.PaymentId)
                .OnDelete(DeleteBehavior.Cascade);

            // 发票与用户关系（创建人）
            modelBuilder.Entity<Invoice>()
                .HasOne(i => i.CreatedBy)
                .WithMany(u => u.Invoices)
                .HasForeignKey(i => i.CreatedById)
                .OnDelete(DeleteBehavior.Restrict);
        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            UpdateAuditFields();
            return base.SaveChangesAsync(cancellationToken);
        }

        public override int SaveChanges()
        {
            UpdateAuditFields();
            return base.SaveChanges();
        }

        private void UpdateAuditFields()
        {
            var now = DateTime.Now;

            foreach (var entry in ChangeTracker.Entries())
            {
                if (entry.Entity is IAuditableEntity auditableEntity)
                {
                    switch (entry.State)
                    {
                        case EntityState.Added:
                            auditableEntity.CreatedAt = now;
                            auditableEntity.UpdatedAt = now;
                            break;
                        case EntityState.Modified:
                            auditableEntity.UpdatedAt = now;
                            break;
                    }
                }
                else if (entry.Entity is User user)
                {
                    switch (entry.State)
                    {
                        case EntityState.Added:
                            user.CreatedAt = now;
                            user.UpdatedAt = now;
                            break;
                        case EntityState.Modified:
                            user.UpdatedAt = now;
                            break;
                    }
                }
                else if (entry.Entity is Department department)
                {
                    switch (entry.State)
                    {
                        case EntityState.Added:
                            department.CreatedAt = now;
                            department.UpdatedAt = now;
                            break;
                        case EntityState.Modified:
                            department.UpdatedAt = now;
                            break;
                    }
                }
            }
        }
    }
}
