# ZXCRM 系统架构设计

## 整体架构

ZXCRM采用分层架构设计，主要分为以下几层：

1. **数据层 (ZXCRM.Data)**：负责数据访问和持久化
2. **服务层 (ZXCRM.Service)**：负责业务逻辑处理
3. **API层 (ZXCRM.WebAPI)**：负责RESTful API服务
4. **表示层 (ZXCRM.WebUI)**：负责Web用户界面展示

![架构图](https://via.placeholder.com/800x400?text=ZXCRM+Architecture)

## 技术栈

- **.NET 9.0**：应用程序框架
- **SQL Server 2022**：数据库
- **Entity Framework Core**：ORM框架
- **AutoMapper**：对象映射
- **ASP.NET Core MVC**：Web UI框架
- **ASP.NET Core Web API**：API框架
- **Bootstrap 5 + AdminLTE 3**：UI组件库
- **JWT + Cookie Authentication**：身份验证

## 数据层 (ZXCRM.Data)

数据层采用仓储模式和工作单元模式，主要包含以下组件：

- **实体类 (Entities)**：映射数据库表的领域模型
- **数据库上下文 (DbContext)**：EF Core数据库上下文
- **仓储接口和实现 (Repositories)**：数据访问抽象
- **工作单元 (UnitOfWork)**：事务管理

## 服务层 (ZXCRM.Service)

服务层采用RESTful风格设计，主要包含以下组件：

- **数据传输对象 (DTOs)**：数据传输模型
- **服务接口 (Interface)**：服务抽象
- **服务实现 (Service)**：业务逻辑实现
- **对象映射 (Mappings)**：实体与DTO之间的映射
- **扩展方法 (Extensions)**：功能扩展

## API层 (ZXCRM.WebAPI)

API层提供RESTful Web API服务，主要包含以下组件：

- **控制器 (Controllers)**：处理HTTP请求和响应
- **中间件 (Middleware)**：请求处理管道
- **认证授权 (Authentication/Authorization)**：JWT身份验证
- **异常处理 (Exception Handling)**：统一错误处理
- **API文档 (Swagger)**：自动生成API文档

## 表示层 (ZXCRM.WebUI)

表示层采用ASP.NET Core MVC模式设计，主要包含以下组件：

- **控制器 (Controllers)**：处理HTTP请求和业务逻辑
- **视图 (Views)**：Razor页面和布局
- **模型 (Models)**：视图模型和数据传输对象
- **服务 (Services)**：API调用和业务逻辑封装
- **静态资源 (wwwroot)**：CSS、JavaScript、图片等资源

## 安全设计

- **双重身份验证**：
  - API层使用JWT Token认证
  - WebUI层使用Cookie认证
- **授权**：基于角色和权限的授权控制
- **数据安全**：敏感数据加密存储
- **输入验证**：防止SQL注入和XSS攻击
- **HTTPS**：强制使用HTTPS传输
- **CORS**：配置跨域资源共享策略

## 扩展性设计

- **模块化**：各功能模块松耦合
- **依赖注入**：使用DI容器管理依赖
- **接口抽象**：通过接口隔离实现
- **配置驱动**：关键参数可配置
