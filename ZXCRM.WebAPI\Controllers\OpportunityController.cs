using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ZXCRM.Service.DTOs;
using ZXCRM.Service.Interface;
using System.Security.Claims;

namespace ZXCRM.WebAPI.Controllers
{
    [Authorize]
    public class OpportunityController : BaseController
    {
        private readonly IOpportunityService _opportunityService;
        private readonly ILogger<OpportunityController> _logger;

        public OpportunityController(IOpportunityService opportunityService, ILogger<OpportunityController> logger)
        {
            _opportunityService = opportunityService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> GetOpportunities()
        {
            try
            {
                _logger.LogInformation("Getting all opportunities");
                
                // 根据项目设计文档要求：用户只能查看自己创建的商机
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
                {
                    return Failure("无法获取当前用户信息");
                }

                var opportunities = await _opportunityService.GetOpportunitiesByUserIdAsync(userId);
                return Success(opportunities, "获取商机列表成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting opportunities");
                return Failure("获取商机列表失败");
            }
        }

        [HttpGet("all")]
        public async Task<IActionResult> GetAllOpportunities()
        {
            try
            {
                _logger.LogInformation("Getting all opportunities (admin)");
                var opportunities = await _opportunityService.GetAllOpportunitiesAsync();
                return Success(opportunities, "获取所有商机列表成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all opportunities");
                return Failure("获取所有商机列表失败");
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetOpportunity(int id)
        {
            try
            {
                _logger.LogInformation("Getting opportunity by ID: {OpportunityId}", id);
                var opportunity = await _opportunityService.GetOpportunityByIdAsync(id);

                if (opportunity == null)
                {
                    _logger.LogWarning("Opportunity not found: {OpportunityId}", id);
                    return Failure($"商机 {id} 不存在");
                }

                // 检查权限：用户只能查看自己创建的商机
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
                {
                    return Failure("无法获取当前用户信息");
                }

                if (opportunity.CreatedById != userId)
                {
                    return Failure("您没有权限查看此商机");
                }

                return Success(opportunity, "获取商机详情成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting opportunity by ID: {OpportunityId}", id);
                return Failure("获取商机详情失败");
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreateOpportunity([FromBody] CreateOpportunityDTO createOpportunityDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Failure("数据验证失败", ModelState);
                }

                // 获取当前用户ID
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
                {
                    return Failure("无法获取当前用户信息");
                }

                // 设置创建人为当前用户
                createOpportunityDto.CreatedById = userId;

                _logger.LogInformation("Creating new opportunity: {OpportunityName}", createOpportunityDto.Name);
                var opportunity = await _opportunityService.CreateOpportunityAsync(createOpportunityDto);

                return Success(opportunity, "创建商机成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating opportunity: {OpportunityName}", createOpportunityDto.Name);
                return Failure(ex.Message.Contains("不存在") ? ex.Message : "创建商机失败");
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateOpportunity(int id, [FromBody] UpdateOpportunityDTO updateOpportunityDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Failure("数据验证失败", ModelState);
                }

                // 设置ID到DTO中
                updateOpportunityDto.Id = id;

                // 检查权限：用户只能修改自己创建的商机
                var existingOpportunity = await _opportunityService.GetOpportunityByIdAsync(id);
                if (existingOpportunity == null)
                {
                    return Failure($"商机 {id} 不存在");
                }

                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
                {
                    return Failure("无法获取当前用户信息");
                }

                if (existingOpportunity.CreatedById != userId)
                {
                    return Failure("您没有权限修改此商机");
                }

                _logger.LogInformation("Updating opportunity: {OpportunityId}", id);
                var opportunity = await _opportunityService.UpdateOpportunityAsync(updateOpportunityDto);

                if (opportunity == null)
                {
                    _logger.LogWarning("Opportunity not found for update: {OpportunityId}", id);
                    return Failure($"商机 {id} 不存在");
                }

                return Success(opportunity, "更新商机成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating opportunity: {OpportunityId}", id);
                return Failure("更新商机失败");
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteOpportunity(int id)
        {
            try
            {
                // 检查权限：用户只能删除自己创建的商机
                var existingOpportunity = await _opportunityService.GetOpportunityByIdAsync(id);
                if (existingOpportunity == null)
                {
                    return Failure($"商机 {id} 不存在");
                }

                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
                {
                    return Failure("无法获取当前用户信息");
                }

                if (existingOpportunity.CreatedById != userId)
                {
                    return Failure("您没有权限删除此商机");
                }

                _logger.LogInformation("Deleting opportunity: {OpportunityId}", id);
                var success = await _opportunityService.DeleteOpportunityAsync(id);

                if (!success)
                {
                    _logger.LogWarning("Opportunity not found for deletion: {OpportunityId}", id);
                    return Failure($"商机 {id} 不存在");
                }

                return Success(null, "删除商机成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting opportunity: {OpportunityId}", id);
                return Failure(ex.Message.Contains("关联订单") ? ex.Message : "删除商机失败");
            }
        }

        [HttpGet("user/{userId}")]
        public async Task<IActionResult> GetOpportunitiesByUser(int userId)
        {
            try
            {
                // 检查权限：用户只能查看自己的商机
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int currentUserId))
                {
                    return Failure("无法获取当前用户信息");
                }

                if (currentUserId != userId)
                {
                    return Failure("您没有权限查看其他用户的商机");
                }

                _logger.LogInformation("Getting opportunities by user: {UserId}", userId);
                var opportunities = await _opportunityService.GetOpportunitiesByUserIdAsync(userId);
                return Success(opportunities, "获取用户商机成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting opportunities by user: {UserId}", userId);
                return Failure("获取用户商机失败");
            }
        }
    }
}
