using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ZXCRM.Service.DTOs;
using ZXCRM.WebUI.Services;
using ZXCRM.WebUI.Models.ViewModels;
using System.Security.Claims;

namespace ZXCRM.WebUI.Controllers
{
    [Authorize]
    public class InvoiceController : Controller
    {
        private readonly IInvoiceService _invoiceService;
        private readonly IPaymentService _paymentService;
        private readonly ILogger<InvoiceController> _logger;

        public InvoiceController(
            IInvoiceService invoiceService,
            IPaymentService paymentService,
            ILogger<InvoiceController> logger)
        {
            _invoiceService = invoiceService;
            _paymentService = paymentService;
            _logger = logger;
        }

        // GET: Invoice
        public async Task<IActionResult> Index(string searchTerm = "", string company = "", string type = "", string status = "", int? paymentId = null, int pageIndex = 1, int pageSize = 10)
        {
            try
            {
                _logger.LogInformation("Getting invoices list. SearchTerm: {SearchTerm}, Company: {Company}, Type: {Type}, Status: {Status}, PaymentId: {PaymentId}, PageIndex: {PageIndex}, PageSize: {PageSize}",
                    searchTerm, company, type, status, paymentId, pageIndex, pageSize);

                var response = await _invoiceService.GetInvoicesAsync();

                if (!response.Success || response.Data == null)
                {
                    _logger.LogWarning("Failed to get invoices: {Message}", response.Message);
                    TempData["ErrorMessage"] = response.Message ?? "获取发票列表失败";
                    return View(new InvoiceListViewModel());
                }

                // 应用搜索和筛选
                var filteredInvoices = response.Data.AsQueryable();

                if (!string.IsNullOrEmpty(searchTerm))
                {
                    filteredInvoices = filteredInvoices.Where(i =>
                        i.PaymentName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        i.PaymentCode.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        i.OrderName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        i.CustomerName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        (!string.IsNullOrEmpty(i.Code) && i.Code.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)));
                }

                if (!string.IsNullOrEmpty(company))
                {
                    filteredInvoices = filteredInvoices.Where(i => i.Company == company);
                }

                if (!string.IsNullOrEmpty(type))
                {
                    filteredInvoices = filteredInvoices.Where(i => i.Type == type);
                }

                if (!string.IsNullOrEmpty(status))
                {
                    filteredInvoices = filteredInvoices.Where(i => i.Status == status);
                }

                if (paymentId.HasValue)
                {
                    filteredInvoices = filteredInvoices.Where(i => i.PaymentId == paymentId.Value);
                }

                // 应用分页
                var totalCount = filteredInvoices.Count();
                var invoices = filteredInvoices
                    .Skip((pageIndex - 1) * pageSize)
                    .Take(pageSize)
                    .Select(i => new InvoiceItemViewModel
                    {
                        Id = i.Id,
                        PaymentId = i.PaymentId,
                        PaymentName = i.PaymentName,
                        PaymentCode = i.PaymentCode,
                        OrderName = i.OrderName,
                        CustomerName = i.CustomerName,
                        Company = i.Company,
                        Type = i.Type,
                        TaxRate = i.TaxRate,
                        Amount = i.Amount,
                        Status = i.Status,
                        Content = i.Content,
                        Code = i.Code,
                        ReceiverName = i.ReceiverName,
                        ReceiverPhone = i.ReceiverPhone,
                        MailingAddress = i.MailingAddress,
                        ApplicantPhone = i.ApplicantPhone,
                        CustomerEmail = i.CustomerEmail,
                        CreatedByName = i.CreatedByName,
                        CreatedAt = i.CreatedAt
                    })
                    .ToList();

                var viewModel = new InvoiceListViewModel
                {
                    Invoices = invoices,
                    TotalCount = totalCount,
                    PageIndex = pageIndex,
                    PageSize = pageSize,
                    SearchTerm = searchTerm,
                    Company = company,
                    Type = type,
                    Status = status,
                    PaymentId = paymentId
                };

                _logger.LogInformation("Successfully retrieved {Count} invoices", invoices.Count);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoices list");
                TempData["ErrorMessage"] = "获取发票列表时发生错误";
                return View(new InvoiceListViewModel());
            }
        }

        // GET: Invoice/Details/5
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                _logger.LogInformation("Getting invoice details for ID: {InvoiceId}", id);

                var response = await _invoiceService.GetInvoiceByIdAsync(id);

                if (!response.Success || response.Data == null)
                {
                    _logger.LogWarning("Invoice not found: {InvoiceId}", id);
                    TempData["ErrorMessage"] = "发票不存在";
                    return RedirectToAction(nameof(Index));
                }

                var viewModel = new InvoiceDetailViewModel
                {
                    Id = response.Data.Id,
                    PaymentId = response.Data.PaymentId,
                    PaymentName = response.Data.PaymentName,
                    PaymentCode = response.Data.PaymentCode,
                    OrderName = response.Data.OrderName,
                    CustomerName = response.Data.CustomerName,
                    Company = response.Data.Company,
                    Type = response.Data.Type,
                    TaxRate = response.Data.TaxRate,
                    Amount = response.Data.Amount,
                    Status = response.Data.Status,
                    Content = response.Data.Content,
                    Code = response.Data.Code,
                    ReceiverName = response.Data.ReceiverName,
                    ReceiverPhone = response.Data.ReceiverPhone,
                    MailingAddress = response.Data.MailingAddress,
                    ApplicantPhone = response.Data.ApplicantPhone,
                    CustomerEmail = response.Data.CustomerEmail,
                    CreatedByName = response.Data.CreatedByName,
                    CreatedAt = response.Data.CreatedAt,
                    UpdatedAt = response.Data.UpdatedAt
                };

                _logger.LogInformation("Successfully retrieved invoice details for ID: {InvoiceId}", id);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoice details for ID: {InvoiceId}", id);
                TempData["ErrorMessage"] = "获取发票详情时发生错误";
                return RedirectToAction(nameof(Index));
            }
        }

        // GET: Invoice/Create
        public async Task<IActionResult> Create(int? paymentId = null)
        {
            try
            {
                var viewModel = new CreateInvoiceViewModel();

                if (paymentId.HasValue)
                {
                    viewModel.PaymentId = paymentId.Value;
                }

                await LoadPaymentsDropdownData(viewModel);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading create invoice page");
                TempData["ErrorMessage"] = "加载创建发票页面时发生错误";
                return RedirectToAction(nameof(Index));
            }
        }

        // POST: Invoice/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CreateInvoiceViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    await LoadPaymentsDropdownData(model);
                    return View(model);
                }

                _logger.LogInformation("Creating new invoice for payment: {PaymentId}", model.PaymentId);

                // 获取当前用户ID
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
                {
                    ModelState.AddModelError(string.Empty, "无法获取当前用户信息");
                    await LoadPaymentsDropdownData(model);
                    return View(model);
                }

                var createInvoiceDto = new CreateInvoiceDTO
                {
                    PaymentId = model.PaymentId,
                    Company = model.Company,
                    Type = model.Type,
                    TaxRate = model.TaxRate,
                    Amount = model.Amount,
                    Status = model.Status,
                    Content = model.Content,
                    Code = model.Code,
                    ReceiverName = model.ReceiverName,
                    ReceiverPhone = model.ReceiverPhone,
                    MailingAddress = model.MailingAddress,
                    ApplicantPhone = model.ApplicantPhone,
                    CustomerEmail = model.CustomerEmail,
                    CreatedById = userId
                };

                var response = await _invoiceService.CreateInvoiceAsync(createInvoiceDto);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully created invoice for payment: {PaymentId}", model.PaymentId);
                    TempData["SuccessMessage"] = "发票创建成功";
                    return RedirectToAction(nameof(Index));
                }
                else
                {
                    _logger.LogWarning("Failed to create invoice for payment: {PaymentId}. Message: {Message}", model.PaymentId, response.Message);
                    ModelState.AddModelError(string.Empty, response.Message ?? "创建发票失败");
                    await LoadPaymentsDropdownData(model);
                    return View(model);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating invoice for payment: {PaymentId}", model.PaymentId);
                ModelState.AddModelError(string.Empty, "创建发票时发生错误");
                await LoadPaymentsDropdownData(model);
                return View(model);
            }
        }

        // GET: Invoice/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            try
            {
                _logger.LogInformation("Loading edit invoice page for ID: {InvoiceId}", id);

                var response = await _invoiceService.GetInvoiceByIdAsync(id);

                if (!response.Success || response.Data == null)
                {
                    _logger.LogWarning("Invoice not found for edit: {InvoiceId}", id);
                    TempData["ErrorMessage"] = "发票不存在";
                    return RedirectToAction(nameof(Index));
                }

                var viewModel = new EditInvoiceViewModel
                {
                    Id = response.Data.Id,
                    PaymentId = response.Data.PaymentId,
                    Company = response.Data.Company,
                    Type = response.Data.Type,
                    TaxRate = response.Data.TaxRate,
                    Amount = response.Data.Amount,
                    Status = response.Data.Status,
                    Content = response.Data.Content,
                    Code = response.Data.Code,
                    ReceiverName = response.Data.ReceiverName,
                    ReceiverPhone = response.Data.ReceiverPhone,
                    MailingAddress = response.Data.MailingAddress,
                    ApplicantPhone = response.Data.ApplicantPhone,
                    CustomerEmail = response.Data.CustomerEmail
                };

                await LoadPaymentsDropdownData(viewModel);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading edit invoice page for ID: {InvoiceId}", id);
                TempData["ErrorMessage"] = "加载编辑发票页面时发生错误";
                return RedirectToAction(nameof(Index));
            }
        }

        // POST: Invoice/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, EditInvoiceViewModel model)
        {
            try
            {
                if (id != model.Id)
                {
                    return BadRequest();
                }

                if (!ModelState.IsValid)
                {
                    await LoadPaymentsDropdownData(model);
                    return View(model);
                }

                _logger.LogInformation("Updating invoice: {InvoiceId}", id);

                var updateInvoiceDto = new UpdateInvoiceDTO
                {
                    Id = model.Id,
                    Company = model.Company,
                    Type = model.Type,
                    TaxRate = model.TaxRate,
                    Amount = model.Amount,
                    Status = model.Status,
                    Content = model.Content,
                    Code = model.Code,
                    ReceiverName = model.ReceiverName,
                    ReceiverPhone = model.ReceiverPhone,
                    MailingAddress = model.MailingAddress,
                    ApplicantPhone = model.ApplicantPhone,
                    CustomerEmail = model.CustomerEmail
                };

                var response = await _invoiceService.UpdateInvoiceAsync(id, updateInvoiceDto);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully updated invoice: {InvoiceId}", id);
                    TempData["SuccessMessage"] = "发票更新成功";
                    return RedirectToAction(nameof(Details), new { id = id });
                }
                else
                {
                    _logger.LogWarning("Failed to update invoice: {InvoiceId}. Message: {Message}", id, response.Message);
                    ModelState.AddModelError(string.Empty, response.Message ?? "更新发票失败");
                    await LoadPaymentsDropdownData(model);
                    return View(model);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating invoice: {InvoiceId}", id);
                ModelState.AddModelError(string.Empty, "更新发票时发生错误");
                await LoadPaymentsDropdownData(model);
                return View(model);
            }
        }

        // POST: Invoice/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                _logger.LogInformation("Deleting invoice: {InvoiceId}", id);

                var response = await _invoiceService.DeleteInvoiceAsync(id);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully deleted invoice: {InvoiceId}", id);
                    TempData["SuccessMessage"] = "发票删除成功";
                }
                else
                {
                    _logger.LogWarning("Failed to delete invoice: {InvoiceId}. Message: {Message}", id, response.Message);
                    TempData["ErrorMessage"] = response.Message ?? "删除发票失败";
                }

                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting invoice: {InvoiceId}", id);
                TempData["ErrorMessage"] = "删除发票时发生错误";
                return RedirectToAction(nameof(Index));
            }
        }

        private async Task LoadPaymentsDropdownData<T>(T viewModel) where T : class
        {
            try
            {
                // 加载款项列表
                var paymentsResponse = await _paymentService.GetPaymentsAsync();
                var payments = new List<PaymentSelectItem>();

                if (paymentsResponse.Success && paymentsResponse.Data != null)
                {
                    payments = paymentsResponse.Data.Select(p => new PaymentSelectItem
                    {
                        Id = p.Id,
                        Name = p.Name,
                        Code = p.Code,
                        OrderName = p.OrderName,
                        CustomerName = p.CustomerName,
                        Amount = p.Amount,
                        Currency = p.Currency,
                        PaymentType = p.PaymentType
                    }).ToList();
                }

                // 使用反射设置属性
                var paymentsProperty = typeof(T).GetProperty("Payments");
                paymentsProperty?.SetValue(viewModel, payments);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading payments dropdown data");
            }
        }
    }
}
