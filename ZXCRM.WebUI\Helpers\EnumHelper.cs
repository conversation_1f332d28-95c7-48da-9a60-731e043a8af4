using ZXCRM.Service.Enums;

namespace ZXCRM.WebUI.Helpers
{
    public static class EnumHelper
    {
        /// <summary>
        /// 获取订单状态显示文本
        /// </summary>
        public static string GetOrderStatusText(string status)
        {
            if (Enum.TryParse<OrderStatus>(status, out var orderStatus))
            {
                return orderStatus.GetDisplayText();
            }
            return status;
        }

        /// <summary>
        /// 获取订单状态颜色
        /// </summary>
        public static string GetOrderStatusColor(string status)
        {
            if (Enum.TryParse<OrderStatus>(status, out var orderStatus))
            {
                return orderStatus switch
                {
                    OrderStatus.New => "secondary",
                    OrderStatus.InProgress => "info",
                    OrderStatus.Completed => "success",
                    OrderStatus.Cancelled => "danger",
                    _ => "light"
                };
            }
            return "light";
        }

        /// <summary>
        /// 获取商机状态显示文本
        /// </summary>
        public static string GetOpportunityStatusText(string status)
        {
            if (Enum.TryParse<OpportunityStatus>(status, out var opportunityStatus))
            {
                return opportunityStatus.GetDisplayText();
            }
            return status;
        }

        /// <summary>
        /// 获取商机状态颜色
        /// </summary>
        public static string GetOpportunityStatusColor(string status)
        {
            if (Enum.TryParse<OpportunityStatus>(status, out var opportunityStatus))
            {
                return opportunityStatus switch
                {
                    OpportunityStatus.New => "secondary",
                    OpportunityStatus.InProgress => "info",
                    OpportunityStatus.Quoted => "warning",
                    OpportunityStatus.Won => "success",
                    OpportunityStatus.Lost => "danger",
                    OpportunityStatus.Cancelled => "dark",
                    _ => "light"
                };
            }
            return "light";
        }

        /// <summary>
        /// 获取款项类型显示文本
        /// </summary>
        public static string GetPaymentTypeText(string type)
        {
            if (Enum.TryParse<PaymentType>(type, out var paymentType))
            {
                return paymentType.GetDisplayText();
            }
            return type;
        }

        /// <summary>
        /// 获取回款状态显示文本
        /// </summary>
        public static string GetPaymentStatusText(string status)
        {
            if (Enum.TryParse<PaymentStatus>(status, out var paymentStatus))
            {
                return paymentStatus.GetDisplayText();
            }
            return status;
        }

        /// <summary>
        /// 获取回款状态颜色
        /// </summary>
        public static string GetPaymentStatusColor(string status)
        {
            if (Enum.TryParse<PaymentStatus>(status, out var paymentStatus))
            {
                return paymentStatus switch
                {
                    PaymentStatus.Pending => "warning",
                    PaymentStatus.Partial => "info",
                    PaymentStatus.Completed => "success",
                    PaymentStatus.Overdue => "danger",
                    _ => "light"
                };
            }
            return "light";
        }

        /// <summary>
        /// 获取发票状态显示文本
        /// </summary>
        public static string GetInvoiceStatusText(string status)
        {
            if (Enum.TryParse<InvoiceStatus>(status, out var invoiceStatus))
            {
                return invoiceStatus.GetDisplayText();
            }
            return status;
        }

        /// <summary>
        /// 获取发票状态颜色
        /// </summary>
        public static string GetInvoiceStatusColor(string status)
        {
            if (Enum.TryParse<InvoiceStatus>(status, out var invoiceStatus))
            {
                return invoiceStatus switch
                {
                    InvoiceStatus.Pending => "warning",
                    InvoiceStatus.Issued => "success",
                    InvoiceStatus.Cancelled => "danger",
                    _ => "light"
                };
            }
            return "light";
        }

        /// <summary>
        /// 获取发票类型显示文本
        /// </summary>
        public static string GetInvoiceTypeText(string type)
        {
            if (Enum.TryParse<InvoiceType>(type, out var invoiceType))
            {
                return invoiceType.GetDisplayText();
            }
            return type;
        }

        /// <summary>
        /// 获取公司类型显示文本
        /// </summary>
        public static string GetCompanyTypeText(string type)
        {
            if (Enum.TryParse<CompanyType>(type, out var companyType))
            {
                return companyType.GetDisplayText();
            }
            return type;
        }

        /// <summary>
        /// 获取货币类型显示文本
        /// </summary>
        public static string GetCurrencyTypeText(string currency)
        {
            if (Enum.TryParse<CurrencyType>(currency, out var currencyType))
            {
                return currencyType.GetDisplayText();
            }
            return currency;
        }

        /// <summary>
        /// 获取税率显示文本
        /// </summary>
        public static string GetTaxRateText(string taxRate)
        {
            if (Enum.TryParse<TaxRateType>(taxRate, out var taxRateType))
            {
                return taxRateType.GetDisplayText();
            }
            return taxRate;
        }

        /// <summary>
        /// 获取用户状态显示文本
        /// </summary>
        public static string GetUserStatusText(string status)
        {
            if (Enum.TryParse<UserStatus>(status, out var userStatus))
            {
                return userStatus.GetDisplayText();
            }
            return status;
        }

        /// <summary>
        /// 获取用户状态颜色
        /// </summary>
        public static string GetUserStatusColor(string status)
        {
            if (Enum.TryParse<UserStatus>(status, out var userStatus))
            {
                return userStatus switch
                {
                    UserStatus.Active => "success",
                    UserStatus.Inactive => "secondary",
                    UserStatus.Locked => "danger",
                    _ => "light"
                };
            }
            return "light";
        }

        /// <summary>
        /// 获取部门状态显示文本
        /// </summary>
        public static string GetDepartmentStatusText(string status)
        {
            if (Enum.TryParse<DepartmentStatus>(status, out var departmentStatus))
            {
                return departmentStatus.GetDisplayText();
            }
            return status;
        }

        /// <summary>
        /// 获取部门状态颜色
        /// </summary>
        public static string GetDepartmentStatusColor(string status)
        {
            if (Enum.TryParse<DepartmentStatus>(status, out var departmentStatus))
            {
                return departmentStatus switch
                {
                    DepartmentStatus.Active => "success",
                    DepartmentStatus.Inactive => "secondary",
                    _ => "light"
                };
            }
            return "light";
        }

        /// <summary>
        /// 格式化金额显示
        /// </summary>
        public static string FormatAmount(decimal amount, string currency = "CNY")
        {
            var currencySymbol = currency switch
            {
                "CNY" => "¥",
                "USD" => "$",
                "EUR" => "€",
                "JPY" => "¥",
                _ => ""
            };
            return $"{currencySymbol}{amount:N2}";
        }

        /// <summary>
        /// 格式化数量显示
        /// </summary>
        public static string FormatCount(int count, string unit = "个")
        {
            return $"{count}{unit}";
        }
    }
}
