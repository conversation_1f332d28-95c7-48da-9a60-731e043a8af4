{"Version": 1, "WorkspaceRootPath": "D:\\AITest\\ZXCRM\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{B8F15B0C-5F3E-4F9F-9F8B-D7D3C4C7B2A1}|ZXCRM.Data\\ZXCRM.Data.csproj|d:\\aitest\\zxcrm\\zxcrm.data\\scripts\\initializeadminpermissions.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}", "RelativeMoniker": "D:0:0:{B8F15B0C-5F3E-4F9F-9F8B-D7D3C4C7B2A1}|ZXCRM.Data\\ZXCRM.Data.csproj|solutionrelative:zxcrm.data\\scripts\\initializeadminpermissions.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}"}, {"AbsoluteMoniker": "D:0:0:{B8F15B0C-5F3E-4F9F-9F8B-D7D3C4C7B2A1}|ZXCRM.Data\\ZXCRM.Data.csproj|d:\\aitest\\zxcrm\\zxcrm.data\\entities\\userpermission.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B8F15B0C-5F3E-4F9F-9F8B-D7D3C4C7B2A1}|ZXCRM.Data\\ZXCRM.Data.csproj|solutionrelative:zxcrm.data\\entities\\userpermission.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D4A2F5C2-6D8F-4D6E-A7D9-3F5D0F3C1AB0}|Solution Items{D4A2F5C2-6D8F-4D6E-A7D9-3F5D0F3C1AB0}|D:\\AITest\\ZXCRM\\Documentation\\ProjectDesign.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{D4A2F5C2-6D8F-4D6E-A7D9-3F5D0F3C1AB0}|Solution Items{D4A2F5C2-6D8F-4D6E-A7D9-3F5D0F3C1AB0}|solutionrelative:Documentation\\ProjectDesign.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{E4F79FF0-3283-427F-B114-BC51EC7FC727}|ZXCRM.Service\\ZXCRM.Service.csproj|d:\\aitest\\zxcrm\\zxcrm.service\\enums\\invoicestatus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E4F79FF0-3283-427F-B114-BC51EC7FC727}|ZXCRM.Service\\ZXCRM.Service.csproj|solutionrelative:zxcrm.service\\enums\\invoicestatus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E4F79FF0-3283-427F-B114-BC51EC7FC727}|ZXCRM.Service\\ZXCRM.Service.csproj|d:\\aitest\\zxcrm\\zxcrm.service\\enums\\invoicetype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E4F79FF0-3283-427F-B114-BC51EC7FC727}|ZXCRM.Service\\ZXCRM.Service.csproj|solutionrelative:zxcrm.service\\enums\\invoicetype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E4F79FF0-3283-427F-B114-BC51EC7FC727}|ZXCRM.Service\\ZXCRM.Service.csproj|d:\\aitest\\zxcrm\\zxcrm.service\\enums\\orderstatus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E4F79FF0-3283-427F-B114-BC51EC7FC727}|ZXCRM.Service\\ZXCRM.Service.csproj|solutionrelative:zxcrm.service\\enums\\orderstatus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F2A8C5D3-7B4E-4F9F-9F8B-D7D3C4C7B2A2}|ZXCRM.WebUI\\ZXCRM.WebUI.csproj|d:\\aitest\\zxcrm\\zxcrm.webui\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{F2A8C5D3-7B4E-4F9F-9F8B-D7D3C4C7B2A2}|ZXCRM.WebUI\\ZXCRM.WebUI.csproj|solutionrelative:zxcrm.webui\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{E4F79FF0-3283-427F-B114-BC51EC7FC727}|ZXCRM.Service\\ZXCRM.Service.csproj|d:\\aitest\\zxcrm\\zxcrm.service\\enums\\currencytype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E4F79FF0-3283-427F-B114-BC51EC7FC727}|ZXCRM.Service\\ZXCRM.Service.csproj|solutionrelative:zxcrm.service\\enums\\currencytype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D4A2F5C2-6D8F-4D6E-A7D9-3F5D0F3C1AB0}|Solution Items{D4A2F5C2-6D8F-4D6E-A7D9-3F5D0F3C1AB0}|D:\\AITest\\ZXCRM\\Documentation\\SystemArchitecture.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{D4A2F5C2-6D8F-4D6E-A7D9-3F5D0F3C1AB0}|Solution Items{D4A2F5C2-6D8F-4D6E-A7D9-3F5D0F3C1AB0}|solutionrelative:Documentation\\SystemArchitecture.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{D4A2F5C2-6D8F-4D6E-A7D9-3F5D0F3C1AB0}|Solution Items{D4A2F5C2-6D8F-4D6E-A7D9-3F5D0F3C1AB0}|D:\\AITest\\ZXCRM\\Documentation\\DatabaseDesign.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{D4A2F5C2-6D8F-4D6E-A7D9-3F5D0F3C1AB0}|Solution Items{D4A2F5C2-6D8F-4D6E-A7D9-3F5D0F3C1AB0}|solutionrelative:Documentation\\DatabaseDesign.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{D4A2F5C2-6D8F-4D6E-A7D9-3F5D0F3C1AB0}|Solution Items{D4A2F5C2-6D8F-4D6E-A7D9-3F5D0F3C1AB0}|D:\\AITest\\ZXCRM\\Documentation\\ProjectProgress.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{D4A2F5C2-6D8F-4D6E-A7D9-3F5D0F3C1AB0}|Solution Items{D4A2F5C2-6D8F-4D6E-A7D9-3F5D0F3C1AB0}|solutionrelative:Documentation\\ProjectProgress.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{F2A8C5D3-7B4E-4F9F-9F8B-D7D3C4C7B2A2}|ZXCRM.WebUI\\ZXCRM.WebUI.csproj|d:\\aitest\\zxcrm\\zxcrm.webui\\views\\payment\\edit.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{F2A8C5D3-7B4E-4F9F-9F8B-D7D3C4C7B2A2}|ZXCRM.WebUI\\ZXCRM.WebUI.csproj|solutionrelative:zxcrm.webui\\views\\payment\\edit.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{F2A8C5D3-7B4E-4F9F-9F8B-D7D3C4C7B2A2}|ZXCRM.WebUI\\ZXCRM.WebUI.csproj|d:\\aitest\\zxcrm\\zxcrm.webui\\views\\payment\\details.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{F2A8C5D3-7B4E-4F9F-9F8B-D7D3C4C7B2A2}|ZXCRM.WebUI\\ZXCRM.WebUI.csproj|solutionrelative:zxcrm.webui\\views\\payment\\details.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{F2A8C5D3-7B4E-4F9F-9F8B-D7D3C4C7B2A2}|ZXCRM.WebUI\\ZXCRM.WebUI.csproj|d:\\aitest\\zxcrm\\zxcrm.webui\\views\\payment\\create.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{F2A8C5D3-7B4E-4F9F-9F8B-D7D3C4C7B2A2}|ZXCRM.WebUI\\ZXCRM.WebUI.csproj|solutionrelative:zxcrm.webui\\views\\payment\\create.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B8F15B0C-5F3E-4F9F-9F8B-D7D3C4C7B2A1}|ZXCRM.Data\\ZXCRM.Data.csproj|d:\\aitest\\zxcrm\\zxcrm.data\\scripts\\initializeadminpermissions.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}|CodeFrame", "RelativeMoniker": "D:0:0:{B8F15B0C-5F3E-4F9F-9F8B-D7D3C4C7B2A1}|ZXCRM.Data\\ZXCRM.Data.csproj|solutionrelative:zxcrm.data\\scripts\\initializeadminpermissions.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}|CodeFrame"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "InitializeAdminPermissions.sql ", "DocumentMoniker": "D:\\AITest\\ZXCRM\\ZXCRM.Data\\Scripts\\InitializeAdminPermissions.sql", "RelativeDocumentMoniker": "ZXCRM.Data\\Scripts\\InitializeAdminPermissions.sql", "ToolTip": "D:\\AITest\\ZXCRM\\ZXCRM.Data\\Scripts\\InitializeAdminPermissions.sql", "RelativeToolTip": "ZXCRM.Data\\Scripts\\InitializeAdminPermissions.sql", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000826|", "WhenOpened": "2025-06-06T06:24:35.599Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "UserPermission.cs", "DocumentMoniker": "D:\\AITest\\ZXCRM\\ZXCRM.Data\\Entities\\UserPermission.cs", "RelativeDocumentMoniker": "ZXCRM.Data\\Entities\\UserPermission.cs", "ToolTip": "D:\\AITest\\ZXCRM\\ZXCRM.Data\\Entities\\UserPermission.cs", "RelativeToolTip": "ZXCRM.Data\\Entities\\UserPermission.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-05T10:04:10.802Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "InvoiceType.cs", "DocumentMoniker": "D:\\AITest\\ZXCRM\\ZXCRM.Service\\Enums\\InvoiceType.cs", "RelativeDocumentMoniker": "ZXCRM.Service\\Enums\\InvoiceType.cs", "ToolTip": "D:\\AITest\\ZXCRM\\ZXCRM.Service\\Enums\\InvoiceType.cs", "RelativeToolTip": "ZXCRM.Service\\Enums\\InvoiceType.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-05T03:27:04.637Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "OrderStatus.cs", "DocumentMoniker": "D:\\AITest\\ZXCRM\\ZXCRM.Service\\Enums\\OrderStatus.cs", "RelativeDocumentMoniker": "ZXCRM.Service\\Enums\\OrderStatus.cs", "ToolTip": "D:\\AITest\\ZXCRM\\ZXCRM.Service\\Enums\\OrderStatus.cs", "RelativeToolTip": "ZXCRM.Service\\Enums\\OrderStatus.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-04T02:43:57.086Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "launchSettings.json", "DocumentMoniker": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "ZXCRM.WebUI\\Properties\\launchSettings.json", "ToolTip": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\Properties\\launchSettings.json", "RelativeToolTip": "ZXCRM.WebUI\\Properties\\launchSettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-03T08:07:59.753Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "CurrencyType.cs", "DocumentMoniker": "D:\\AITest\\ZXCRM\\ZXCRM.Service\\Enums\\CurrencyType.cs", "RelativeDocumentMoniker": "ZXCRM.Service\\Enums\\CurrencyType.cs", "ToolTip": "D:\\AITest\\ZXCRM\\ZXCRM.Service\\Enums\\CurrencyType.cs", "RelativeToolTip": "ZXCRM.Service\\Enums\\CurrencyType.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T03:47:44.474Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "ProjectProgress.md", "DocumentMoniker": "D:\\AITest\\ZXCRM\\Documentation\\ProjectProgress.md", "RelativeDocumentMoniker": "Documentation\\ProjectProgress.md", "ToolTip": "D:\\AITest\\ZXCRM\\Documentation\\ProjectProgress.md", "RelativeToolTip": "Documentation\\ProjectProgress.md", "ViewState": "AgIAAJAAAAAAAAAAAAAAAKcAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-05-27T07:03:34.067Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "Edit.cshtml", "DocumentMoniker": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\Views\\Payment\\Edit.cshtml", "RelativeDocumentMoniker": "ZXCRM.WebUI\\Views\\Payment\\Edit.cshtml", "ToolTip": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\Views\\Payment\\Edit.cshtml", "RelativeToolTip": "ZXCRM.WebUI\\Views\\Payment\\Edit.cshtml", "ViewState": "AgIAABUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-05-28T01:22:12.532Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "Details.cshtml", "DocumentMoniker": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\Views\\Payment\\Details.cshtml", "RelativeDocumentMoniker": "ZXCRM.WebUI\\Views\\Payment\\Details.cshtml", "ToolTip": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\Views\\Payment\\Details.cshtml", "RelativeToolTip": "ZXCRM.WebUI\\Views\\Payment\\Details.cshtml", "ViewState": "AgIAADkAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-05-28T01:22:06.693Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "ProjectDesign.md", "DocumentMoniker": "D:\\AITest\\ZXCRM\\Documentation\\ProjectDesign.md", "RelativeDocumentMoniker": "Documentation\\ProjectDesign.md", "ToolTip": "D:\\AITest\\ZXCRM\\Documentation\\ProjectDesign.md", "RelativeToolTip": "Documentation\\ProjectDesign.md", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-05-28T01:16:35.734Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "InvoiceStatus.cs", "DocumentMoniker": "D:\\AITest\\ZXCRM\\ZXCRM.Service\\Enums\\InvoiceStatus.cs", "RelativeDocumentMoniker": "ZXCRM.Service\\Enums\\InvoiceStatus.cs", "ToolTip": "D:\\AITest\\ZXCRM\\ZXCRM.Service\\Enums\\InvoiceStatus.cs", "RelativeToolTip": "ZXCRM.Service\\Enums\\InvoiceStatus.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T03:09:52.431Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "Create.cshtml", "DocumentMoniker": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\Views\\Payment\\Create.cshtml", "RelativeDocumentMoniker": "ZXCRM.WebUI\\Views\\Payment\\Create.cshtml", "ToolTip": "D:\\AITest\\ZXCRM\\ZXCRM.WebUI\\Views\\Payment\\Create.cshtml", "RelativeToolTip": "ZXCRM.WebUI\\Views\\Payment\\Create.cshtml", "ViewState": "AgIAADwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-05-28T01:21:55.083Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "DatabaseDesign.md", "DocumentMoniker": "D:\\AITest\\ZXCRM\\Documentation\\DatabaseDesign.md", "RelativeDocumentMoniker": "Documentation\\DatabaseDesign.md", "ToolTip": "D:\\AITest\\ZXCRM\\Documentation\\DatabaseDesign.md", "RelativeToolTip": "Documentation\\DatabaseDesign.md", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-05-27T10:42:18.773Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "SystemArchitecture.md", "DocumentMoniker": "D:\\AITest\\ZXCRM\\Documentation\\SystemArchitecture.md", "RelativeDocumentMoniker": "Documentation\\SystemArchitecture.md", "ToolTip": "D:\\AITest\\ZXCRM\\Documentation\\SystemArchitecture.md", "RelativeToolTip": "Documentation\\SystemArchitecture.md", "ViewState": "AgIAAAkAAAAAAAAAAAAAABQAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-05-26T03:35:06.455Z", "EditorCaption": ""}]}]}]}