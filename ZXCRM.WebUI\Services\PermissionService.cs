using ZXCRM.Service.DTOs;
using ZXCRM.WebUI.Models;

namespace ZXCRM.WebUI.Services
{
    public class PermissionService : IPermissionService
    {
        private readonly IApiService _apiService;
        private readonly ILogger<PermissionService> _logger;

        public PermissionService(IApiService apiService, ILogger<PermissionService> logger)
        {
            _apiService = apiService;
            _logger = logger;
        }

        public async Task<IEnumerable<PermissionDto>> GetAllAsync()
        {
            try
            {
                _logger.LogInformation("Getting all permissions");

                var response = await _apiService.GetAsync<List<PermissionDto>>("api/permission");

                if (response.Success && response.Data != null)
                {
                    _logger.LogInformation("Successfully retrieved {Count} permissions", response.Data.Count);
                    return response.Data;
                }
                else
                {
                    _logger.LogWarning("Failed to retrieve permissions: {Message}", response.Message);
                    return new List<PermissionDto>();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting permissions");
                return new List<PermissionDto>();
            }
        }

        public async Task<PermissionDto?> GetByIdAsync(int id)
        {
            try
            {
                _logger.LogInformation("Getting permission by ID: {PermissionId}", id);

                var response = await _apiService.GetAsync<PermissionDto>($"api/permission/{id}");

                if (response.Success)
                {
                    _logger.LogInformation("Successfully retrieved permission: {PermissionId}", id);
                    return response.Data;
                }
                else
                {
                    _logger.LogWarning("Failed to retrieve permission {PermissionId}: {Message}", id, response.Message);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting permission by ID: {PermissionId}", id);
                return null;
            }
        }

        public async Task<PermissionDto?> CreateAsync(PermissionDto permission)
        {
            try
            {
                _logger.LogInformation("Creating permission: {PermissionName}", permission.Name);

                var response = await _apiService.PostAsync<PermissionDto>("api/permission", permission);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully created permission: {PermissionName}", permission.Name);
                    return response.Data;
                }
                else
                {
                    _logger.LogWarning("Failed to create permission {PermissionName}: {Message}", permission.Name, response.Message);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating permission: {PermissionName}", permission.Name);
                return null;
            }
        }

        public async Task<PermissionDto?> UpdateAsync(PermissionDto permission)
        {
            try
            {
                _logger.LogInformation("Updating permission: {PermissionId}", permission.Id);

                var response = await _apiService.PutAsync<PermissionDto>($"api/permission/{permission.Id}", permission);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully updated permission: {PermissionId}", permission.Id);
                    return response.Data;
                }
                else
                {
                    _logger.LogWarning("Failed to update permission {PermissionId}: {Message}", permission.Id, response.Message);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating permission: {PermissionId}", permission.Id);
                return null;
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                _logger.LogInformation("Deleting permission: {PermissionId}", id);

                var response = await _apiService.DeleteAsync<object>($"api/permission/{id}");

                if (response.Success)
                {
                    _logger.LogInformation("Successfully deleted permission: {PermissionId}", id);
                    return true;
                }
                else
                {
                    _logger.LogWarning("Failed to delete permission {PermissionId}: {Message}", id, response.Message);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting permission: {PermissionId}", id);
                return false;
            }
        }
    }
}
