@model ZXCRM.WebUI.Models.ViewModels.UserProfileViewModel
@{
    ViewData["Title"] = "我的信息";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">我的信息</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a asp-controller="Dashboard" asp-action="Index">首页</a></li>
                    <li class="breadcrumb-item active">我的信息</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <!-- 用户信息卡片 -->
            <div class="col-md-8">
                <div class="card card-primary">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-user"></i> 基本信息
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">用户名:</label>
                                    <p class="form-control-static">@Model.Username</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">姓名:</label>
                                    <p class="form-control-static">@Model.Name</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">邮箱:</label>
                                    <p class="form-control-static">
                                        @if (!string.IsNullOrEmpty(Model.Email))
                                        {
                                            <a href="mailto:@Model.Email">@Model.Email</a>
                                        }
                                        else
                                        {
                                            <span class="text-muted">未设置</span>
                                        }
                                    </p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">电话:</label>
                                    <p class="form-control-static">
                                        @if (!string.IsNullOrEmpty(Model.Phone))
                                        {
                                            <a href="tel:@Model.Phone">@Model.Phone</a>
                                        }
                                        else
                                        {
                                            <span class="text-muted">未设置</span>
                                        }
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">性别:</label>
                                    <p class="form-control-static">
                                        @if (!string.IsNullOrEmpty(Model.Gender))
                                        {
                                            @Model.Gender
                                        }
                                        else
                                        {
                                            <span class="text-muted">未设置</span>
                                        }
                                    </p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">部门:</label>
                                    <p class="form-control-static">
                                        @if (!string.IsNullOrEmpty(Model.DepartmentName))
                                        {
                                            @Model.DepartmentName
                                        }
                                        else
                                        {
                                            <span class="text-muted">未分配</span>
                                        }
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">角色:</label>
                                    <p class="form-control-static">
                                        <span class="badge badge-primary">@Model.Role</span>
                                    </p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">状态:</label>
                                    <p class="form-control-static">
                                        @if (Model.IsActive)
                                        {
                                            <span class="badge badge-success">激活</span>
                                        }
                                        else
                                        {
                                            <span class="badge badge-danger">禁用</span>
                                        }
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <a asp-controller="User" asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary">
                            <i class="fas fa-edit"></i> 编辑信息
                        </a>
                        <a asp-controller="Dashboard" asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回首页
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- 用户头像和统计信息 -->
            <div class="col-md-4">
                <!-- 用户头像卡片 -->
                <div class="card card-widget widget-user">
                    <div class="widget-user-header bg-info">
                        <h3 class="widget-user-username">@Model.Name</h3>
                        <h5 class="widget-user-desc">@Model.Role</h5>
                    </div>
                    <div class="widget-user-image">
                        <img class="img-circle elevation-2" src="~/img/user-avatar.png" alt="User Avatar">
                    </div>
                    <div class="card-footer">
                        <div class="row">
                            <div class="col-sm-6 border-right">
                                <div class="description-block">
                                    <h5 class="description-header">@Model.Id</h5>
                                    <span class="description-text">用户ID</span>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="description-block">
                                    <h5 class="description-header">
                                        @((DateTime.Now - Model.CreatedAt).Days)
                                    </h5>
                                    <span class="description-text">使用天数</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 账户信息卡片 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-info-circle"></i> 账户信息
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="info-box mb-3">
                            <span class="info-box-icon bg-success">
                                <i class="fas fa-calendar-plus"></i>
                            </span>
                            <div class="info-box-content">
                                <span class="info-box-text">创建时间</span>
                                <span class="info-box-number">@Model.CreatedAt.ToString("yyyy-MM-dd")</span>
                            </div>
                        </div>
                        
                        @if (Model.UpdatedAt.HasValue)
                        {
                            <div class="info-box mb-3">
                                <span class="info-box-icon bg-info">
                                    <i class="fas fa-calendar-check"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">最后更新</span>
                                    <span class="info-box-number">@Model.UpdatedAt.Value.ToString("yyyy-MM-dd")</span>
                                </div>
                            </div>
                        }
                        
                        <div class="info-box">
                            <span class="info-box-icon bg-warning">
                                <i class="fas fa-clock"></i>
                            </span>
                            <div class="info-box-content">
                                <span class="info-box-text">在线状态</span>
                                <span class="info-box-number">
                                    <span class="badge badge-success">在线</span>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 快速操作卡片 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-tools"></i> 快速操作
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="btn-group-vertical btn-block" role="group">
                            <a asp-controller="User" asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-outline-primary">
                                <i class="fas fa-edit"></i> 编辑个人信息
                            </a>
                            <button type="button" class="btn btn-outline-warning" onclick="changePassword()">
                                <i class="fas fa-key"></i> 修改密码
                            </button>
                            <a href="#" class="btn btn-outline-info" onclick="showLogoutModal()">
                                <i class="fas fa-sign-out-alt"></i> 退出登录
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        function changePassword() {
            // 这里可以实现修改密码功能
            alert('修改密码功能正在开发中...');
        }

        // 显示提示消息
        @if (TempData["SuccessMessage"] != null)
        {
            <text>
            toastr.success('@TempData["SuccessMessage"]');
            </text>
        }

        @if (TempData["ErrorMessage"] != null)
        {
            <text>
            toastr.error('@TempData["ErrorMessage"]');
            </text>
        }
    </script>
}
