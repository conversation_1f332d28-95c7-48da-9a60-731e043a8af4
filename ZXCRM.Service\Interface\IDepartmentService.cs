using ZXCRM.Service.DTOs;

namespace ZXCRM.Service.Interface
{
    public interface IDepartmentService
    {
        Task<IEnumerable<DepartmentDTO>> GetAllDepartmentsAsync();
        Task<DepartmentDTO?> GetDepartmentByIdAsync(int id);
        Task<IEnumerable<DepartmentDTO>> GetDepartmentTreeAsync();
        Task<DepartmentDTO> CreateDepartmentAsync(DepartmentDTO departmentDto);
        Task<DepartmentDTO> CreateDepartmentAsync(CreateDepartmentDTO createDepartmentDto);
        Task<DepartmentDTO?> UpdateDepartmentAsync(DepartmentDTO departmentDto);
        Task<DepartmentDTO?> UpdateDepartmentAsync(UpdateDepartmentDTO updateDepartmentDto);
        Task<bool> DeleteDepartmentAsync(int id);
        Task<IEnumerable<UserDTO>> GetDepartmentUsersAsync(int departmentId);
    }
}
