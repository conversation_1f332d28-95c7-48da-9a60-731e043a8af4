using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using ZXCRM.Data.Context;
using ZXCRM.Data.Entities;
using ZXCRM.Service.DTOs;
using ZXCRM.Service.Interface;

namespace ZXCRM.Service.Service
{
    public class UserPermissionService : IUserPermissionService
    {
        private readonly ZXCRMDbContext _context;
        private readonly IMapper _mapper;

        public UserPermissionService(ZXCRMDbContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        public async Task<IEnumerable<UserPermissionDto>> GetByUserIdAsync(int userId)
        {
            var userPermissions = await _context.UserPermissions
                .Where(up => up.UserId == userId)
                .ToListAsync();

            return _mapper.Map<IEnumerable<UserPermissionDto>>(userPermissions);
        }

        public async Task UpdateUserPermissionsAsync(int userId, IEnumerable<UserPermissionDto> permissions)
        {
            // 获取用户现有的权限
            var existingPermissions = await _context.UserPermissions
                .Where(up => up.UserId == userId)
                .ToListAsync();

            // 删除现有权限
            _context.UserPermissions.RemoveRange(existingPermissions);

            // 添加新的权限
            foreach (var permissionDto in permissions)
            {
                var permission = new UserPermission
                {
                    UserId = userId,
                    PermissionId = permissionDto.PermissionId,
                    ModuleType = permissionDto.ModuleType
                };
                _context.UserPermissions.Add(permission);
            }

            await _context.SaveChangesAsync();
        }
    }
}