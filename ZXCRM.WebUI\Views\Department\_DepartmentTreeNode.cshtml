@model ZXCRM.Service.DTOs.DepartmentDTO

<div class="tree-node">
    <div class="tree-node-content">
        <!-- 展开/收起按钮 -->
        @if (Model.Children.Any())
        {
            <button type="button" class="tree-toggle expanded" onclick="toggleNode(this)">
                <i class="fas fa-minus"></i>
            </button>
        }
        else
        {
            <button type="button" class="tree-toggle" disabled>
                <i class="fas fa-circle" style="font-size: 6px;"></i>
            </button>
        }
        
        <!-- 部门信息 -->
        <div class="department-info">
            <div class="d-flex align-items-center">
                <!-- 部门图标 -->
                <i class="fas fa-building text-primary mr-2"></i>
                
                <!-- 部门名称 -->
                <span class="department-name">@Model.Name</span>
                
                <!-- 部门编码 -->
                @if (!string.IsNullOrEmpty(Model.Code))
                {
                    <span class="department-code">@Model.Code</span>
                }
                
                <!-- 父部门信息 -->
                @if (!string.IsNullOrEmpty(Model.ParentName))
                {
                    <small class="text-muted">
                        <i class="fas fa-arrow-up"></i> @Model.ParentName
                    </small>
                }
            </div>
            
            <!-- 统计信息和操作按钮 -->
            <div class="d-flex align-items-center">
                <!-- 部门统计 -->
                <div class="department-stats">
                    <!-- 人员数量 -->
                    <span class="badge badge-primary user-count">
                        <i class="fas fa-users"></i> @Model.UserCount 人
                    </span>
                    
                    <!-- 子部门数量 -->
                    @if (Model.Children.Any())
                    {
                        <span class="badge badge-info">
                            <i class="fas fa-sitemap"></i> @Model.Children.Count 个子部门
                        </span>
                    }
                    
                    <!-- 创建时间 -->
                    <small class="text-muted">
                        <i class="fas fa-calendar"></i> @Model.CreatedAt.ToString("yyyy-MM-dd")
                    </small>
                </div>
                
                <!-- 操作按钮 -->
                <div class="department-actions ml-3">
                    <div class="btn-group btn-group-sm" role="group">
                        <!-- 查看详情 -->
                        <a asp-controller="Department" asp-action="Details" asp-route-id="@Model.Id" 
                           class="btn btn-info btn-tree" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </a>
                        
                        <!-- 添加子部门 -->
                        <a asp-controller="Department" asp-action="Create" asp-route-parentId="@Model.Id" 
                           class="btn btn-success btn-tree" title="添加子部门">
                            <i class="fas fa-plus"></i>
                        </a>
                        
                        <!-- 编辑部门 -->
                        <a asp-controller="Department" asp-action="Edit" asp-route-id="@Model.Id" 
                           class="btn btn-warning btn-tree" title="编辑部门">
                            <i class="fas fa-edit"></i>
                        </a>
                        
                        <!-- 删除部门 -->
                        @if (Model.UserCount == 0 && !Model.Children.Any())
                        {
                            <button type="button" class="btn btn-danger btn-tree" title="删除部门"
                                    onclick="confirmDelete(@Model.Id, '@Model.Name')">
                                <i class="fas fa-trash"></i>
                            </button>
                        }
                        else
                        {
                            <button type="button" class="btn btn-secondary btn-tree" title="有人员或子部门，无法删除" disabled>
                                <i class="fas fa-ban"></i>
                            </button>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 子部门 -->
    @if (Model.Children.Any())
    {
        <div class="tree-children">
            @foreach (var child in Model.Children.OrderBy(c => c.Name))
            {
                @await Html.PartialAsync("_DepartmentTreeNode", child)
            }
        </div>
    }
</div>
