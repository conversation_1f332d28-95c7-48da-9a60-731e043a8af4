using System.ComponentModel.DataAnnotations;

namespace ZXCRM.Service.DTOs
{
    public class CreateDepartmentDTO
    {
        [Required(ErrorMessage = "部门名称不能为空")]
        [StringLength(100, ErrorMessage = "部门名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "部门编码长度不能超过50个字符")]
        public string Code { get; set; } = string.Empty;

        public int? ParentId { get; set; }
    }
}
