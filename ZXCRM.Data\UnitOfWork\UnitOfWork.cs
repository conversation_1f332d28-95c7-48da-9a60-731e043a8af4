using Microsoft.EntityFrameworkCore.Storage;
using ZXCRM.Data.Context;
using ZXCRM.Data.Entities;
using ZXCRM.Data.Repositories;

namespace ZXCRM.Data.UnitOfWork
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly ZXCRMDbContext _context;
        private IDbContextTransaction? _transaction;
        private bool _disposed = false;

        private IRepository<Department>? _departmentRepository;
        private IRepository<User>? _userRepository;
        private IRepository<Permission>? _permissionRepository;
        private IRepository<UserPermission>? _userPermissionRepository;
        private IRepository<Opportunity>? _opportunityRepository;
        private IRepository<Order>? _orderRepository;
        private IRepository<Payment>? _paymentRepository;
        private IRepository<Invoice>? _invoiceRepository;

        public UnitOfWork(ZXCRMDbContext context)
        {
            _context = context;
        }

        public IRepository<Department> Departments => _departmentRepository ??= new Repository<Department>(_context);
        public IRepository<User> Users => _userRepository ??= new Repository<User>(_context);
        public IRepository<Permission> Permissions => _permissionRepository ??= new Repository<Permission>(_context);
        public IRepository<UserPermission> UserPermissions => _userPermissionRepository ??= new Repository<UserPermission>(_context);
        public IRepository<Opportunity> Opportunities => _opportunityRepository ??= new Repository<Opportunity>(_context);
        public IRepository<Order> Orders => _orderRepository ??= new Repository<Order>(_context);
        public IRepository<Payment> Payments => _paymentRepository ??= new Repository<Payment>(_context);
        public IRepository<Invoice> Invoices => _invoiceRepository ??= new Repository<Invoice>(_context);

        public async Task<int> SaveChangesAsync()
        {
            return await _context.SaveChangesAsync();
        }

        public async Task BeginTransactionAsync()
        {
            _transaction = await _context.Database.BeginTransactionAsync();
        }

        public async Task CommitTransactionAsync()
        {
            try
            {
                await _context.SaveChangesAsync();
                if (_transaction != null)
                {
                    await _transaction.CommitAsync();
                }
            }
            finally
            {
                if (_transaction != null)
                {
                    await _transaction.DisposeAsync();
                    _transaction = null;
                }
            }
        }

        public async Task RollbackTransactionAsync()
        {
            try
            {
                if (_transaction != null)
                {
                    await _transaction.RollbackAsync();
                }
            }
            finally
            {
                if (_transaction != null)
                {
                    await _transaction.DisposeAsync();
                    _transaction = null;
                }
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _transaction?.Dispose();
                    _context.Dispose();
                }
                _disposed = true;
            }
        }
    }
}
