@using ZXCRM.WebUI.Services
@inject IPermissionCheckService PermissionService
@{
    ViewData["Title"] = "权限调试";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2>权限调试信息</h2>
            
            <!-- 用户基本信息 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5>用户基本信息</h5>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <td><strong>IsAuthenticated:</strong></td>
                            <td>@User.Identity?.IsAuthenticated</td>
                        </tr>
                        <tr>
                            <td><strong>Identity Name:</strong></td>
                            <td>@User.Identity?.Name</td>
                        </tr>
                        <tr>
                            <td><strong>User ID (GetUserId):</strong></td>
                            <td>@User.GetUserId()</td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- 用户Claims -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5>用户Claims</h5>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Type</th>
                                <th>Value</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var claim in User.Claims)
                            {
                                <tr>
                                    <td><code>@claim.Type</code></td>
                                    <td>@claim.Value</td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>

            @{
                var userId = User.GetUserId();
            }

            @if (userId > 0)
            {
                <!-- 权限检查结果 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5>权限检查结果</h5>
                    </div>
                    <div class="card-body">
                        @{
                            var userModules = await PermissionService.GetUserModulesAsync(userId);
                            var userPermissions = await PermissionService.GetUserPermissionsAsync(userId);
                            var userOverview = await PermissionService.GetUserPermissionOverviewAsync(userId);
                        }

                        <h6>用户模块列表 (@userModules.Count 个):</h6>
                        @if (userModules.Any())
                        {
                            <ul>
                                @foreach (var module in userModules)
                                {
                                    <li>@module</li>
                                }
                            </ul>
                        }
                        else
                        {
                            <p class="text-muted">无可访问模块</p>
                        }

                        <h6>用户权限详情:</h6>
                        @if (userPermissions.Any())
                        {
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>模块</th>
                                        <th>权限</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var kvp in userPermissions)
                                    {
                                        <tr>
                                            <td>@kvp.Key</td>
                                            <td>@string.Join(", ", kvp.Value)</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        }
                        else
                        {
                            <p class="text-muted">无权限配置</p>
                        }

                        <h6>用户权限概览:</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Username:</strong></td>
                                <td>@userOverview.Username</td>
                            </tr>
                            <tr>
                                <td><strong>Name:</strong></td>
                                <td>@userOverview.Name</td>
                            </tr>
                            <tr>
                                <td><strong>Role:</strong></td>
                                <td>@userOverview.Role</td>
                            </tr>
                            <tr>
                                <td><strong>IsSuperAdmin:</strong></td>
                                <td>@userOverview.IsSuperAdmin</td>
                            </tr>
                            <tr>
                                <td><strong>HasBusinessAccess:</strong></td>
                                <td>@userOverview.HasBusinessAccess</td>
                            </tr>
                            <tr>
                                <td><strong>HasSystemAccess:</strong></td>
                                <td>@userOverview.HasSystemAccess</td>
                            </tr>
                            <tr>
                                <td><strong>Business Modules:</strong></td>
                                <td>@string.Join(", ", userOverview.BusinessModules)</td>
                            </tr>
                            <tr>
                                <td><strong>System Modules:</strong></td>
                                <td>@string.Join(", ", userOverview.SystemModules)</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- 权限测试 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5>权限测试</h5>
                    </div>
                    <div class="card-body">
                        @{
                            var testModules = new[] { "商机管理", "订单管理", "用户管理", "部门管理", "权限管理" };
                            var testPermissions = new[] { "Query", "Create", "Edit", "Delete" };
                        }

                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>模块</th>
                                    <th>查询</th>
                                    <th>新建</th>
                                    <th>修改</th>
                                    <th>删除</th>
                                    <th>模块访问</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var module in testModules)
                                {
                                    <tr>
                                        <td>@module</td>
                                        @foreach (var permission in testPermissions)
                                        {
                                            var hasPermission = await PermissionService.HasPermissionAsync(userId, module, permission);
                                            <td>
                                                @if (hasPermission)
                                                {
                                                    <span class="badge bg-success">✓</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">✗</span>
                                                }
                                            </td>
                                        }
                                        @{ var hasModuleAccess = await PermissionService.HasModuleAccessAsync(userId, module); }
                                        <td>
                                            @if (hasModuleAccess)
                                            {
                                                <span class="badge bg-success">✓</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-danger">✗</span>
                                            }
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            }
            else
            {
                <div class="alert alert-danger">
                    <h5>错误</h5>
                    <p>无法获取用户ID，请检查登录状态和Claims配置。</p>
                </div>
            }
        </div>
    </div>
</div>
