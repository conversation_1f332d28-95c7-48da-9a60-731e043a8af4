# ZXCRM企业运营数据管理系统 - 项目设计文档

## 1. 项目概述

### 1.1 项目背景
ZXCRM（ZX Customer Relationship Management）是一个专为企业运营数据管理设计的系统，主要用于跟踪和管理企业的业务运营数据，包括订单、付款、发票等核心业务信息。
系统概要的功能需求信息如下：
1、部门、用户、权限管理，需要后台管理界面有查询、新建、删除部门、用户的功能，用户信息按通常需要设计，姓名、登录名、密码、性别、所属部门等；部门信息按照通常需要设计，包括部门名称、部门编号等，部门有层级关系，可以在部门下创建子部门；部门下可以关联一至多个用户。权限管理主要赋予用户是有关商机、订单、款项等模块的数据该用户是否拥有查询、新建、修改、删除的权限，默认权限为查询权限。
2、商机管理，已登录的用户查询自己已创建的商机，可以新建和删除、修改商机信息，商机信息按通常需要设计，包括客户名称、联系人、商机内容等；
3、订单管理，已登录的用户查询自己已创建的订单，可以新建和删除、修改订单信息，订单信息按通常需要设计，包括客户名称、联系人、商机来自于相关的商机信息，还包括订单名称、订单编号、订单金额、订单币种（人民币、美元、日元、欧元）、结算金额（人民币）、客户经理、创建部门、签订日期、订单状态、项目经理、业绩归属部门等；客户经理默认为订单创建人，在创建时和后续可以修改为其他用户
4、款项管理，每一个订单可以创建一至多笔款项，对应到合同里面的回款分项信息，款项基本信息来自订单信息，如客户名称、订单名称、客户经理、项目经理，还包括款项名称、款项编号、款项类型（首付、2付、3付、4付、5付、尾款、全款）、款项金额、款项币种（人民币、美元、日元、欧元）、结算金额（人民币）、预计回款日期、回款日期、开票状态、开票日期。
5、发票管理，每一个款项可以创建一至多个发票，对应到款项开出发票用于回款的业务，发票信息根据通常需要设计，包括款项编号（发票对应到订单的某一笔款项）、发票公司（A公司、B公司、C公司）、发票类型（增值税专用发票、增值税普通发票、收据）、发票税率（16%、13%、6%、免税）、发票内容、发票代号、收件人、收件人电话、发票邮寄地址、申请人电话、客户电子邮件等。

### 1.2 项目目标  
- 提供统一的企业运营数据管理平台
- 实现订单、付款、发票的全生命周期管理
- 提供数据分析和报表功能
- 支持多用户、多部门的协作管理

### 1.3 技术栈选择
- **后端框架**: ASP.NET Core 9.0
- **数据库**: SQL Server
- **ORM**: Entity Framework Core
- **前端框架**: ASP.NET Core MVC + Razor Pages
- **UI框架**: Bootstrap 5 + AdminLTE 3
- **认证方式**: Cookie Authentication + JWT Token (API)
- **API设计**: RESTful API

## 2. 系统架构

### 2.1 整体架构
```
┌─────────────────┐    HTTP请求    ┌─────────────────┐
│   Web UI        │ ──────────→   │   Web API       │
│ (ASP.NET MVC)   │               │  (ASP.NET Core) │
└─────────────────┘               └─────────────────┘
                                          │
                                          ▼
                                  ┌─────────────────┐
                                  │   业务逻辑层     │
                                  │   (Service)     │
                                  └─────────────────┘
                                          │
                                          ▼
                                  ┌─────────────────┐
                                  │   数据访问层     │
                                  │ (Entity Framework) │
                                  └─────────────────┘
                                          │
                                          ▼
                                  ┌─────────────────┐
                                  │   数据库        │
                                  │  (SQL Server)   │
                                  └─────────────────┘
```

### 2.2 项目结构
```
ZXCRM/
├── ZXCRM.Data/              # 数据访问层
│   ├── Entities/            # 实体类
│   ├── Repositories/        # 仓储接口和实现
│   ├── UnitOfWork/          # 工作单元
│   └── ZXCRMDbContext.cs    # 数据库上下文
├── ZXCRM.Service/           # 业务逻辑层
│   ├── DTOs/                # 数据传输对象
│   ├── Service/             # 业务服务实现
│   ├── Interface/           # 服务接口
│   └── Mappings/            # 对象映射配置
├── ZXCRM.WebAPI/            # Web API项目
│   ├── Controllers/         # API控制器
│   ├── Middleware/          # 中间件
│   └── Program.cs           # API启动配置
├── ZXCRM.WebUI/             # Web UI项目
│   ├── Controllers/         # MVC控制器
│   ├── Views/               # Razor视图
│   ├── Services/            # API调用服务
│   ├── Models/              # 视图模型
│   └── wwwroot/             # 静态资源
└── Documentation/           # 项目文档
```

## 3. 核心功能模块

### 3.1 用户管理模块
- **用户注册与登录**: 支持用户注册、登录、登出
- **用户信息管理**: 用户基本信息维护
- **部门管理**: 组织架构管理
- **权限控制**: 基于角色的权限管理

### 3.2 订单管理模块
- **订单创建**: 支持订单信息录入
- **订单查询**: 多条件订单查询和筛选
- **订单状态管理**: 订单生命周期状态跟踪
- **订单统计**: 订单数据统计分析

### 3.3 款项管理模块
- **款项记录**: 回款信息记录和管理
- **款项状态**: 款项状态跟踪
- **回款计划**: 回款计划制定和提醒
- **款项统计**: 回款数据统计分析

### 3.4 发票管理模块
- **发票开具**: 发票信息录入和管理
- **发票状态**: 发票状态跟踪
- **发票打印**: 发票格式化输出
- **发票统计**: 发票数据统计分析

### 3.5 数据分析模块
- **仪表盘**: 关键指标展示
- **报表生成**: 各类业务报表
- **数据可视化**: 图表展示
- **数据导出**: 支持Excel等格式导出

## 4. 技术实现

### 4.1 数据访问层 (ZXCRM.Data)
- **实体设计**: 基于EF Core的实体类设计
- **仓储模式**: 统一的数据访问接口
- **工作单元**: 事务管理和数据一致性
- **数据库迁移**: 支持数据库版本管理

### 4.2 业务逻辑层 (ZXCRM.Service)
- **服务接口**: 定义业务操作契约
- **服务实现**: 具体业务逻辑实现
- **DTO设计**: 数据传输对象设计
- **对象映射**: AutoMapper配置

### 4.3 API层 (ZXCRM.WebAPI)
- **RESTful API**: 标准的REST API设计
- **JWT认证**: 基于Token的身份验证
- **异常处理**: 统一的错误处理机制
- **API文档**: Swagger自动生成文档

### 4.4 表示层 (ZXCRM.WebUI)
- **MVC架构**: 标准的MVC设计模式
- **Razor视图**: 服务端渲染页面
- **Cookie认证**: 基于Cookie的身份验证
- **响应式设计**: 支持多设备访问

## 5. 安全设计

### 5.1 身份认证
- **双重认证机制**:
  - WebUI使用Cookie Authentication
  - WebAPI使用JWT Token Authentication
- **密码安全**: BCrypt密码哈希
- **会话管理**: 安全的会话管理

### 5.2 授权控制
- **基于角色的访问控制**: RBAC权限模型
- **API权限**: 细粒度的API访问控制
- **页面权限**: 基于角色的页面访问控制

### 5.3 数据安全
- **输入验证**: 防止SQL注入和XSS攻击
- **数据加密**: 敏感数据加密存储
- **HTTPS**: 强制使用HTTPS传输
- **CORS配置**: 安全的跨域资源共享

## 6. 部署架构

### 6.1 开发环境
- **本地开发**: Visual Studio + SQL Server LocalDB
- **调试模式**: 同时运行WebAPI和WebUI项目
- **热重载**: 支持代码热重载

### 6.2 生产环境
- **Web服务器**: IIS或Nginx
- **数据库**: SQL Server 2019+
- **负载均衡**: 支持多实例部署
- **监控日志**: 结构化日志和监控

## 7. 开发规范

### 7.1 代码规范
- **命名规范**: 遵循C#命名约定
- **代码注释**: 完整的XML文档注释
- **异常处理**: 统一的异常处理策略
- **日志记录**: 结构化日志记录

### 7.2 数据库规范
- **表命名**: 使用复数形式
- **字段命名**: 使用PascalCase
- **索引设计**: 合理的索引设计
- **约束设计**: 完整的约束定义

### 7.3 API规范
- **RESTful设计**: 遵循REST设计原则
- **HTTP状态码**: 正确使用HTTP状态码
- **错误响应**: 统一的错误响应格式
- **版本控制**: API版本管理策略

## 8. 测试策略

### 8.1 单元测试
- **业务逻辑测试**: Service层单元测试
- **数据访问测试**: Repository层测试
- **API测试**: Controller层测试

### 8.2 集成测试
- **数据库集成测试**: 完整的数据流测试
- **API集成测试**: 端到端API测试
- **UI集成测试**: 用户界面集成测试

### 8.3 性能测试
- **负载测试**: 系统负载能力测试
- **压力测试**: 系统极限测试
- **数据库性能**: 查询性能优化

## 9. 维护和扩展

### 9.1 系统维护
- **日志监控**: 系统运行状态监控
- **性能监控**: 关键指标监控
- **备份策略**: 数据备份和恢复
- **版本升级**: 系统版本管理

### 9.2 功能扩展
- **模块化设计**: 支持新功能模块扩展
- **插件架构**: 支持第三方插件
- **API扩展**: 支持新的API端点
- **集成能力**: 支持第三方系统集成

## 10. 项目里程碑

### 10.1 第一阶段 (已完成)
- 基础架构搭建
- 数据库设计
- 认证系统实现
- 基础UI框架

### 10.2 第二阶段 (进行中)
- 用户管理功能
- 订单管理功能
- 基础报表功能

### 10.3 第三阶段 (计划中)
- 款项管理功能
- 发票管理功能
- 高级报表功能
- 系统优化

### 10.4 第四阶段 (计划中)
- 移动端适配
- 第三方集成
- 高级分析功能
- 性能优化
