using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using ZXCRM.Data.Context;
using ZXCRM.Data.Entities;
using ZXCRM.Service.DTOs;
using ZXCRM.Service.Interface;

namespace ZXCRM.Service.Service
{
    public class PermissionService : IPermissionService
    {
        private readonly ZXCRMDbContext _context;
        private readonly IMapper _mapper;

        public PermissionService(ZXCRMDbContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        public async Task<IEnumerable<PermissionDto>> GetAllAsync()
        {
            var permissions = await _context.Permissions.ToListAsync();
            return _mapper.Map<IEnumerable<PermissionDto>>(permissions);
        }

        public async Task<PermissionDto> GetByIdAsync(int id)
        {
            var permission = await _context.Permissions
                .FirstOrDefaultAsync(p => p.Id == id);

            return _mapper.Map<PermissionDto>(permission);
        }

        public async Task<PermissionDto> CreateAsync(PermissionDto permissionDto)
        {
            var permission = _mapper.Map<Permission>(permissionDto);

            _context.Permissions.Add(permission);
            await _context.SaveChangesAsync();

            return _mapper.Map<PermissionDto>(permission);
        }

        public async Task<PermissionDto> UpdateAsync(PermissionDto permissionDto)
        {
            var permission = await _context.Permissions
                .FirstOrDefaultAsync(p => p.Id == permissionDto.Id);

            if (permission == null)
            {
                throw new Exception("权限不存在");
            }

            _mapper.Map(permissionDto, permission);

            await _context.SaveChangesAsync();

            return _mapper.Map<PermissionDto>(permission);
        }

        public async Task DeleteAsync(int id)
        {
            var permission = await _context.Permissions
                .FirstOrDefaultAsync(p => p.Id == id);

            if (permission == null)
            {
                throw new Exception("权限不存在");
            }

            _context.Permissions.Remove(permission);
            await _context.SaveChangesAsync();
        }
    }
}