namespace ZXCRM.WebUI.Models.ViewModels
{
    public class DashboardViewModel
    {
        public DashboardSummaryViewModel Summary { get; set; } = new();
        public List<RecentActivityViewModel> RecentActivities { get; set; } = new();
        public ChartDataViewModel ChartData { get; set; } = new();
    }

    public class DashboardSummaryViewModel
    {
        public int TotalOrders { get; set; }
        public decimal TotalRevenue { get; set; }
        public int TotalCustomers { get; set; }
        public int PendingOrders { get; set; }
        public int CompletedOrders { get; set; }
        public int TotalUsers { get; set; }
        public int TotalInvoices { get; set; }
        public decimal TotalPayments { get; set; }
    }

    public class RecentActivityViewModel
    {
        public int Id { get; set; }
        public string Type { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
    }

    public class ChartDataViewModel
    {
        public List<string> Labels { get; set; } = new();
        public List<decimal> Data { get; set; } = new();
        public string ChartType { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
    }
}
