using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ZXCRM.Service.DTOs;
using ZXCRM.WebUI.Services;
using ZXCRM.WebUI.Models.ViewModels;

namespace ZXCRM.WebUI.Controllers
{
    [Authorize]
    public class PaymentController : Controller
    {
        private readonly IPaymentService _paymentService;
        private readonly IOrderService _orderService;
        private readonly ILogger<PaymentController> _logger;

        public PaymentController(
            IPaymentService paymentService,
            IOrderService orderService,
            ILogger<PaymentController> logger)
        {
            _paymentService = paymentService;
            _orderService = orderService;
            _logger = logger;
        }

        // GET: Payment
        public async Task<IActionResult> Index(string searchTerm = "", string paymentType = "", string invoiceStatus = "", int? orderId = null, int pageIndex = 1, int pageSize = 10)
        {
            try
            {
                _logger.LogInformation("Getting payments list. SearchTerm: {SearchTerm}, PaymentType: {PaymentType}, InvoiceStatus: {InvoiceStatus}, OrderId: {OrderId}, PageIndex: {PageIndex}, PageSize: {PageSize}",
                    searchTerm, paymentType, invoiceStatus, orderId, pageIndex, pageSize);

                var response = await _paymentService.GetPaymentsAsync();

                if (!response.Success || response.Data == null)
                {
                    _logger.LogWarning("Failed to get payments: {Message}", response.Message);
                    TempData["ErrorMessage"] = response.Message ?? "获取款项列表失败";
                    return View(new PaymentListViewModel());
                }

                // 应用搜索和筛选
                var filteredPayments = response.Data.AsQueryable();

                if (!string.IsNullOrEmpty(searchTerm))
                {
                    filteredPayments = filteredPayments.Where(p =>
                        p.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        p.Code.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        p.OrderName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        p.CustomerName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase));
                }

                if (!string.IsNullOrEmpty(paymentType))
                {
                    filteredPayments = filteredPayments.Where(p => p.PaymentType == paymentType);
                }

                if (!string.IsNullOrEmpty(invoiceStatus))
                {
                    filteredPayments = filteredPayments.Where(p => p.InvoiceStatus == invoiceStatus);
                }

                if (orderId.HasValue)
                {
                    filteredPayments = filteredPayments.Where(p => p.OrderId == orderId.Value);
                }

                // 应用分页
                var totalCount = filteredPayments.Count();
                var payments = filteredPayments
                    .Skip((pageIndex - 1) * pageSize)
                    .Take(pageSize)
                    .Select(p => new PaymentItemViewModel
                    {
                        Id = p.Id,
                        Name = p.Name,
                        Code = p.Code,
                        OrderId = p.OrderId,
                        OrderName = p.OrderName,
                        OrderCode = p.OrderCode,
                        CustomerName = p.CustomerName,
                        PaymentType = p.PaymentType,
                        Amount = p.Amount,
                        Currency = p.Currency,
                        SettlementAmount = p.SettlementAmount,
                        ExpectedPaymentDate = p.ExpectedPaymentDate,
                        ActualPaymentDate = p.ActualPaymentDate,
                        PaymentStatus = p.PaymentStatus,
                        InvoiceStatus = p.InvoiceStatus,
                        InvoiceDate = p.InvoiceDate,
                        AccountManagerName = p.AccountManagerName,
                        ProjectManagerName = p.ProjectManagerName,
                        CreatedAt = p.CreatedAt
                    })
                    .ToList();

                var viewModel = new PaymentListViewModel
                {
                    Payments = payments,
                    TotalCount = totalCount,
                    PageIndex = pageIndex,
                    PageSize = pageSize,
                    SearchTerm = searchTerm,
                    PaymentType = paymentType,
                    InvoiceStatus = invoiceStatus,
                    OrderId = orderId
                };

                _logger.LogInformation("Successfully retrieved {Count} payments", payments.Count);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payments list");
                TempData["ErrorMessage"] = "获取款项列表时发生错误";
                return View(new PaymentListViewModel());
            }
        }

        // GET: Payment/Details/5
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                _logger.LogInformation("Getting payment details for ID: {PaymentId}", id);

                var response = await _paymentService.GetPaymentByIdAsync(id);

                if (!response.Success || response.Data == null)
                {
                    _logger.LogWarning("Payment not found: {PaymentId}", id);
                    TempData["ErrorMessage"] = "款项不存在";
                    return RedirectToAction(nameof(Index));
                }

                var viewModel = new PaymentDetailViewModel
                {
                    Id = response.Data.Id,
                    Name = response.Data.Name,
                    Code = response.Data.Code,
                    OrderId = response.Data.OrderId,
                    OrderName = response.Data.OrderName,
                    OrderCode = response.Data.OrderCode,
                    CustomerName = response.Data.CustomerName,
                    PaymentType = response.Data.PaymentType,
                    Amount = response.Data.Amount,
                    Currency = response.Data.Currency,
                    SettlementAmount = response.Data.SettlementAmount,
                    ExpectedPaymentDate = response.Data.ExpectedPaymentDate,
                    ActualPaymentDate = response.Data.ActualPaymentDate,
                    PaymentStatus = response.Data.PaymentStatus,
                    InvoiceStatus = response.Data.InvoiceStatus,
                    InvoiceDate = response.Data.InvoiceDate,
                    AccountManagerName = response.Data.AccountManagerName,
                    ProjectManagerName = response.Data.ProjectManagerName,
                    CreatedAt = response.Data.CreatedAt,
                    UpdatedAt = response.Data.UpdatedAt
                };

                _logger.LogInformation("Successfully retrieved payment details for ID: {PaymentId}", id);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payment details for ID: {PaymentId}", id);
                TempData["ErrorMessage"] = "获取款项详情时发生错误";
                return RedirectToAction(nameof(Index));
            }
        }

        // GET: Payment/Create
        public async Task<IActionResult> Create(int? orderId = null)
        {
            try
            {
                var viewModel = new CreatePaymentViewModel();

                if (orderId.HasValue)
                {
                    viewModel.OrderId = orderId.Value;
                }

                await LoadOrdersDropdownData(viewModel);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading create payment page");
                TempData["ErrorMessage"] = "加载创建款项页面时发生错误";
                return RedirectToAction(nameof(Index));
            }
        }

        // POST: Payment/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CreatePaymentViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    await LoadOrdersDropdownData(model);
                    return View(model);
                }

                _logger.LogInformation("Creating new payment: {PaymentName}", model.Name);

                // 生成款项编号
                var paymentCode = GeneratePaymentCode();

                var createPaymentDto = new CreatePaymentDTO
                {
                    Name = model.Name,
                    Code = paymentCode,
                    OrderId = model.OrderId,
                    PaymentType = model.PaymentType,
                    Amount = model.Amount,
                    Currency = model.Currency,
                    SettlementAmount = model.SettlementAmount,
                    ExpectedPaymentDate = model.ExpectedPaymentDate,
                    ActualPaymentDate = model.ActualPaymentDate,
                    InvoiceStatus = model.InvoiceStatus,
                    InvoiceDate = model.InvoiceDate
                };

                var response = await _paymentService.CreatePaymentAsync(createPaymentDto);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully created payment: {PaymentName}", model.Name);
                    TempData["SuccessMessage"] = "款项创建成功";
                    return RedirectToAction(nameof(Index));
                }
                else
                {
                    _logger.LogWarning("Failed to create payment: {PaymentName}. Message: {Message}", model.Name, response.Message);
                    ModelState.AddModelError(string.Empty, response.Message ?? "创建款项失败");
                    await LoadOrdersDropdownData(model);
                    return View(model);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating payment: {PaymentName}", model.Name);
                ModelState.AddModelError(string.Empty, "创建款项时发生错误");
                await LoadOrdersDropdownData(model);
                return View(model);
            }
        }

        private async Task LoadOrdersDropdownData<T>(T viewModel) where T : class
        {
            try
            {
                // 加载订单列表
                var ordersResponse = await _orderService.GetOrdersAsync();
                var orders = new List<OrderSelectItem>();

                if (ordersResponse.Success && ordersResponse.Data != null)
                {
                    orders = ordersResponse.Data.Select(o => new OrderSelectItem
                    {
                        Id = o.Id,
                        Name = o.Name,
                        Code = o.Code,
                        CustomerName = o.CustomerName,
                        Amount = o.Amount,
                        Currency = o.Currency
                    }).ToList();
                }

                // 使用反射设置属性
                var ordersProperty = typeof(T).GetProperty("Orders");
                ordersProperty?.SetValue(viewModel, orders);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading orders dropdown data");
            }
        }

        // GET: Payment/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            try
            {
                _logger.LogInformation("Loading edit payment page for ID: {PaymentId}", id);

                var response = await _paymentService.GetPaymentByIdAsync(id);

                if (!response.Success || response.Data == null)
                {
                    _logger.LogWarning("Payment not found for edit: {PaymentId}", id);
                    TempData["ErrorMessage"] = "款项不存在";
                    return RedirectToAction(nameof(Index));
                }

                var viewModel = new EditPaymentViewModel
                {
                    Id = response.Data.Id,
                    Name = response.Data.Name,
                    Code = response.Data.Code,
                    OrderId = response.Data.OrderId,
                    PaymentType = response.Data.PaymentType,
                    Amount = response.Data.Amount,
                    Currency = response.Data.Currency,
                    SettlementAmount = response.Data.SettlementAmount,
                    ExpectedPaymentDate = response.Data.ExpectedPaymentDate,
                    ActualPaymentDate = response.Data.ActualPaymentDate,
                    InvoiceStatus = response.Data.InvoiceStatus,
                    InvoiceDate = response.Data.InvoiceDate
                };

                await LoadOrdersDropdownData(viewModel);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading edit payment page for ID: {PaymentId}", id);
                TempData["ErrorMessage"] = "加载编辑款项页面时发生错误";
                return RedirectToAction(nameof(Index));
            }
        }

        // POST: Payment/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, EditPaymentViewModel model)
        {
            try
            {
                if (id != model.Id)
                {
                    return BadRequest();
                }

                if (!ModelState.IsValid)
                {
                    await LoadOrdersDropdownData(model);
                    return View(model);
                }

                _logger.LogInformation("Updating payment: {PaymentId}", id);

                var updatePaymentDto = new UpdatePaymentDTO
                {
                    Id = model.Id,
                    Name = model.Name,
                    Code = model.Code,
                    PaymentType = model.PaymentType,
                    Amount = model.Amount,
                    Currency = model.Currency,
                    SettlementAmount = model.SettlementAmount,
                    ExpectedPaymentDate = model.ExpectedPaymentDate,
                    ActualPaymentDate = model.ActualPaymentDate,
                    InvoiceStatus = model.InvoiceStatus,
                    InvoiceDate = model.InvoiceDate
                };

                var response = await _paymentService.UpdatePaymentAsync(id, updatePaymentDto);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully updated payment: {PaymentId}", id);
                    TempData["SuccessMessage"] = "款项更新成功";
                    return RedirectToAction(nameof(Details), new { id = id });
                }
                else
                {
                    _logger.LogWarning("Failed to update payment: {PaymentId}. Message: {Message}", id, response.Message);
                    ModelState.AddModelError(string.Empty, response.Message ?? "更新款项失败");
                    await LoadOrdersDropdownData(model);
                    return View(model);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating payment: {PaymentId}", id);
                ModelState.AddModelError(string.Empty, "更新款项时发生错误");
                await LoadOrdersDropdownData(model);
                return View(model);
            }
        }

        // POST: Payment/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                _logger.LogInformation("Deleting payment: {PaymentId}", id);

                var response = await _paymentService.DeletePaymentAsync(id);

                if (response.Success)
                {
                    _logger.LogInformation("Successfully deleted payment: {PaymentId}", id);
                    TempData["SuccessMessage"] = "款项删除成功";
                }
                else
                {
                    _logger.LogWarning("Failed to delete payment: {PaymentId}. Message: {Message}", id, response.Message);
                    TempData["ErrorMessage"] = response.Message ?? "删除款项失败";
                }

                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting payment: {PaymentId}", id);
                TempData["ErrorMessage"] = "删除款项时发生错误";
                return RedirectToAction(nameof(Index));
            }
        }

        private string GeneratePaymentCode()
        {
            // 生成款项编号：PAY + 年月日 + 6位随机数
            var dateStr = DateTime.Now.ToString("yyyyMMdd");
            var random = new Random().Next(100000, 999999);
            return $"PAY{dateStr}{random}";
        }
    }
}
