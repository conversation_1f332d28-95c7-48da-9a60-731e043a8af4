@model ZXCRM.WebUI.Models.ViewModels.UserDetailViewModel
@using ZXCRM.WebUI.Helpers
@{
    ViewData["Title"] = "用户详情";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">用户详情</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a asp-controller="Dashboard" asp-action="Index">首页</a></li>
                    <li class="breadcrumb-item"><a asp-controller="User" asp-action="Index">用户管理</a></li>
                    <li class="breadcrumb-item active">用户详情</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <!-- 用户基本信息 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-user"></i> 基本信息
                        </h3>
                        <div class="card-tools">
                            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning btn-sm">
                                <i class="fas fa-edit"></i> 编辑
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">用户名:</dt>
                                    <dd class="col-sm-8">
                                        <strong>@Model.Username</strong>
                                    </dd>

                                    <dt class="col-sm-4">姓名:</dt>
                                    <dd class="col-sm-8">@Model.Name</dd>

                                    <dt class="col-sm-4">性别:</dt>
                                    <dd class="col-sm-8">
                                        @if (!string.IsNullOrEmpty(Model.Gender))
                                        {
                                            @Model.Gender
                                        }
                                        else
                                        {
                                            <span class="text-muted">未设置</span>
                                        }
                                    </dd>

                                    <dt class="col-sm-4">邮箱:</dt>
                                    <dd class="col-sm-8">
                                        @if (!string.IsNullOrEmpty(Model.Email))
                                        {
                                            <a href="mailto:@Model.Email">@Model.Email</a>
                                        }
                                        else
                                        {
                                            <span class="text-muted">未设置</span>
                                        }
                                    </dd>
                                </dl>
                            </div>
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">电话:</dt>
                                    <dd class="col-sm-8">
                                        @if (!string.IsNullOrEmpty(Model.Phone))
                                        {
                                            <a href="tel:@Model.Phone">@Model.Phone</a>
                                        }
                                        else
                                        {
                                            <span class="text-muted">未设置</span>
                                        }
                                    </dd>

                                    <dt class="col-sm-4">部门:</dt>
                                    <dd class="col-sm-8">
                                        <span class="badge badge-secondary">@Model.DepartmentName</span>
                                    </dd>

                                    <dt class="col-sm-4">角色:</dt>
                                    <dd class="col-sm-8">
                                        <span class="badge badge-info">@Model.Role</span>
                                    </dd>

                                    <dt class="col-sm-4">状态:</dt>
                                    <dd class="col-sm-8">
                                        <span class="badge <EMAIL>(Model.Status)">
                                            @if (Model.Status == "Active")
                                            {
                                                <i class="fas fa-check"></i>
                                            }
                                            else if (Model.Status == "Locked")
                                            {
                                                <i class="fas fa-lock"></i>
                                            }
                                            else
                                            {
                                                <i class="fas fa-times"></i>
                                            }
                                            @EnumHelper.GetUserStatusText(Model.Status)
                                        </span>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 时间信息 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-clock"></i> 时间信息
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">创建时间:</dt>
                                    <dd class="col-sm-8">
                                        <i class="fas fa-calendar-plus text-success"></i>
                                        @Model.CreatedAt.ToString("yyyy年MM月dd日 HH:mm:ss")
                                    </dd>
                                </dl>
                            </div>
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">更新时间:</dt>
                                    <dd class="col-sm-8">
                                        <i class="fas fa-calendar-edit text-warning"></i>
                                        @Model.UpdatedAt.ToString("yyyy年MM月dd日 HH:mm:ss")
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <!-- 操作面板 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-cogs"></i> 操作
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning btn-block">
                                <i class="fas fa-edit"></i> 编辑用户
                            </a>

                            <button type="button" class="btn btn-danger btn-block" onclick="confirmDelete(@Model.Id, '@Model.Username')">
                                <i class="fas fa-trash"></i> 删除用户
                            </button>

                            <hr>

                            <a asp-action="Index" class="btn btn-secondary btn-block">
                                <i class="fas fa-arrow-left"></i> 返回列表
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-bar"></i> 统计信息
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="info-box">
                            <span class="info-box-icon bg-info">
                                <i class="fas fa-shopping-cart"></i>
                            </span>
                            <div class="info-box-content">
                                <span class="info-box-text">创建订单</span>
                                <span class="info-box-number">0</span>
                            </div>
                        </div>

                        <div class="info-box">
                            <span class="info-box-icon bg-success">
                                <i class="fas fa-dollar-sign"></i>
                            </span>
                            <div class="info-box-content">
                                <span class="info-box-text">处理款项</span>
                                <span class="info-box-number">0</span>
                            </div>
                        </div>

                        <div class="info-box">
                            <span class="info-box-icon bg-warning">
                                <i class="fas fa-file-invoice"></i>
                            </span>
                            <div class="info-box-content">
                                <span class="info-box-text">开具发票</span>
                                <span class="info-box-number">0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>您确定要删除用户 <strong id="deleteUserName"></strong> 吗？</p>
                <p class="text-danger"><small>此操作不可撤销！</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(userId, userName) {
            $('#deleteUserName').text(userName);
            $('#deleteForm').attr('action', '@Url.Action("Delete", "User")/' + userId);
            $('#deleteModal').modal('show');
        }
    </script>
}
