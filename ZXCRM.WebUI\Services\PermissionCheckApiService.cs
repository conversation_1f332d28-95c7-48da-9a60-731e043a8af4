using System.Security.Claims;
using System.Text.Json;

namespace ZXCRM.WebUI.Services
{
    public interface IPermissionCheckService
    {
        Task<bool> HasPermissionAsync(int userId, string moduleType, string permissionCode);
        Task<bool> HasModuleAccessAsync(int userId, string moduleType);
        Task<List<string>> GetUserModulesAsync(int userId);
        Task<Dictionary<string, List<string>>> GetUserPermissionsAsync(int userId);
        bool IsSystemModule(string moduleType);
        bool IsBusinessModule(string moduleType);
        Task<UserPermissionOverviewDto> GetUserPermissionOverviewAsync(int userId);
    }

    public class PermissionCheckApiService : IPermissionCheckService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<PermissionCheckApiService> _logger;

        // 定义模块类型（与Service层保持一致）
        private readonly string[] _businessModules = {
            "商机管理", "订单管理", "款项管理", "发票管理", "报表统计"
        };

        private readonly string[] _systemModules = {
            "用户管理", "部门管理", "权限管理"
        };

        public PermissionCheckApiService(HttpClient httpClient, ILogger<PermissionCheckApiService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
        }

        /// <summary>
        /// 检查用户是否有特定权限
        /// </summary>
        public async Task<bool> HasPermissionAsync(int userId, string moduleType, string permissionCode)
        {
            try
            {
                var response = await _httpClient.GetAsync(
                    $"api/permissioncheck/user/{userId}/permission?moduleType={Uri.EscapeDataString(moduleType)}&permissionCode={Uri.EscapeDataString(permissionCode)}");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<PermissionCheckResponse>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    return result?.HasPermission ?? false;
                }

                _logger.LogWarning("Permission check API call failed for user {UserId}: {ModuleType}.{PermissionCode}. Status: {StatusCode}",
                    userId, moduleType, permissionCode, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling permission check API for user {UserId}: {ModuleType}.{PermissionCode}",
                    userId, moduleType, permissionCode);
                return false;
            }
        }

        /// <summary>
        /// 检查用户是否有模块访问权限
        /// </summary>
        public async Task<bool> HasModuleAccessAsync(int userId, string moduleType)
        {
            try
            {
                var response = await _httpClient.GetAsync(
                    $"api/permissioncheck/user/{userId}/module/{Uri.EscapeDataString(moduleType)}/access");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<ModuleAccessResponse>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    return result?.HasAccess ?? false;
                }

                _logger.LogWarning("Module access check API call failed for user {UserId}: {ModuleType}. Status: {StatusCode}",
                    userId, moduleType, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling module access check API for user {UserId}: {ModuleType}",
                    userId, moduleType);
                return false;
            }
        }

        /// <summary>
        /// 获取用户有权限的模块列表
        /// </summary>
        public async Task<List<string>> GetUserModulesAsync(int userId)
        {
            try
            {
                var response = await _httpClient.GetAsync($"api/permissioncheck/user/{userId}/modules");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<UserModulesResponse>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    return result?.Modules ?? new List<string>();
                }

                _logger.LogWarning("User modules API call failed for user {UserId}. Status: {StatusCode}",
                    userId, response.StatusCode);
                return new List<string>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling user modules API for user {UserId}", userId);
                return new List<string>();
            }
        }

        /// <summary>
        /// 获取用户的完整权限映射
        /// </summary>
        public async Task<Dictionary<string, List<string>>> GetUserPermissionsAsync(int userId)
        {
            try
            {
                var response = await _httpClient.GetAsync($"api/permissioncheck/user/{userId}/permissions");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<UserPermissionsResponse>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    return result?.Permissions ?? new Dictionary<string, List<string>>();
                }

                _logger.LogWarning("User permissions API call failed for user {UserId}. Status: {StatusCode}",
                    userId, response.StatusCode);
                return new Dictionary<string, List<string>>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling user permissions API for user {UserId}", userId);
                return new Dictionary<string, List<string>>();
            }
        }

        /// <summary>
        /// 获取用户权限概览
        /// </summary>
        public async Task<UserPermissionOverviewDto> GetUserPermissionOverviewAsync(int userId)
        {
            try
            {
                var response = await _httpClient.GetAsync($"api/permissioncheck/user/{userId}/overview");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<UserPermissionOverviewDto>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    return result ?? new UserPermissionOverviewDto();
                }

                _logger.LogWarning("User permission overview API call failed for user {UserId}. Status: {StatusCode}",
                    userId, response.StatusCode);
                return new UserPermissionOverviewDto();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling user permission overview API for user {UserId}", userId);
                return new UserPermissionOverviewDto();
            }
        }

        /// <summary>
        /// 判断是否是系统模块
        /// </summary>
        public bool IsSystemModule(string moduleType)
        {
            return _systemModules.Contains(moduleType);
        }

        /// <summary>
        /// 判断是否是业务模块
        /// </summary>
        public bool IsBusinessModule(string moduleType)
        {
            return _businessModules.Contains(moduleType);
        }
    }

    /// <summary>
    /// API响应模型
    /// </summary>
    public class PermissionCheckResponse
    {
        public int UserId { get; set; }
        public string ModuleType { get; set; } = string.Empty;
        public string PermissionCode { get; set; } = string.Empty;
        public bool HasPermission { get; set; }
    }

    public class ModuleAccessResponse
    {
        public int UserId { get; set; }
        public string ModuleType { get; set; } = string.Empty;
        public bool HasAccess { get; set; }
    }

    public class UserModulesResponse
    {
        public int UserId { get; set; }
        public List<string> Modules { get; set; } = new();
    }

    public class UserPermissionsResponse
    {
        public int UserId { get; set; }
        public Dictionary<string, List<string>> Permissions { get; set; } = new();
    }

    public class UserPermissionOverviewDto
    {
        public int UserId { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Role { get; set; } = string.Empty;
        public bool IsSuperAdmin { get; set; }
        public List<string> BusinessModules { get; set; } = new();
        public List<string> SystemModules { get; set; } = new();
        public Dictionary<string, List<string>> ModulePermissions { get; set; } = new();
        public bool HasBusinessAccess { get; set; }
        public bool HasSystemAccess { get; set; }
    }

    /// <summary>
    /// 权限检查扩展方法
    /// </summary>
    public static class PermissionCheckExtensions
    {
        /// <summary>
        /// 从ClaimsPrincipal获取用户ID
        /// </summary>
        public static int GetUserId(this ClaimsPrincipal user)
        {
            var userIdClaim = user.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
            {
                return userId;
            }
            return 0;
        }

        /// <summary>
        /// 检查当前用户权限
        /// </summary>
        public static async Task<bool> HasPermissionAsync(this ClaimsPrincipal user, 
            IPermissionCheckService permissionService, string moduleType, string permissionCode)
        {
            var userId = user.GetUserId();
            if (userId == 0) return false;

            return await permissionService.HasPermissionAsync(userId, moduleType, permissionCode);
        }

        /// <summary>
        /// 检查当前用户模块访问权限
        /// </summary>
        public static async Task<bool> HasModuleAccessAsync(this ClaimsPrincipal user, 
            IPermissionCheckService permissionService, string moduleType)
        {
            var userId = user.GetUserId();
            if (userId == 0) return false;

            return await permissionService.HasModuleAccessAsync(userId, moduleType);
        }
    }
}
