-- 检查并修复Users表结构
USE ZXCRM;
GO

-- 检查Users表是否存在CreatedAt列
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND name = 'CreatedAt')
BEGIN
    ALTER TABLE [dbo].[Users] ADD [CreatedAt] [datetime] NOT NULL DEFAULT(GETDATE());
    PRINT 'Added CreatedAt column to Users table';
END
ELSE
BEGIN
    PRINT 'CreatedAt column already exists in Users table';
END
GO

-- 检查Users表是否存在UpdatedAt列
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND name = 'UpdatedAt')
BEGIN
    ALTER TABLE [dbo].[Users] ADD [UpdatedAt] [datetime] NOT NULL DEFAULT(GETDATE());
    PRINT 'Added UpdatedAt column to Users table';
END
ELSE
BEGIN
    PRINT 'UpdatedAt column already exists in Users table';
END
GO

-- 检查Users表是否存在IsDeleted列
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND name = 'IsDeleted')
BEGIN
    ALTER TABLE [dbo].[Users] ADD [IsDeleted] [bit] NOT NULL DEFAULT(0);
    PRINT 'Added IsDeleted column to Users table';
END
ELSE
BEGIN
    PRINT 'IsDeleted column already exists in Users table';
END
GO

-- 检查Departments表是否存在CreatedAt列
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Departments]') AND name = 'CreatedAt')
BEGIN
    ALTER TABLE [dbo].[Departments] ADD [CreatedAt] [datetime] NOT NULL DEFAULT(GETDATE());
    PRINT 'Added CreatedAt column to Departments table';
END
ELSE
BEGIN
    PRINT 'CreatedAt column already exists in Departments table';
END
GO

-- 检查Departments表是否存在UpdatedAt列
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Departments]') AND name = 'UpdatedAt')
BEGIN
    ALTER TABLE [dbo].[Departments] ADD [UpdatedAt] [datetime] NOT NULL DEFAULT(GETDATE());
    PRINT 'Added UpdatedAt column to Departments table';
END
ELSE
BEGIN
    PRINT 'UpdatedAt column already exists in Departments table';
END
GO

-- 检查Departments表是否存在IsDeleted列
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Departments]') AND name = 'IsDeleted')
BEGIN
    ALTER TABLE [dbo].[Departments] ADD [IsDeleted] [bit] NOT NULL DEFAULT(0);
    PRINT 'Added IsDeleted column to Departments table';
END
ELSE
BEGIN
    PRINT 'IsDeleted column already exists in Departments table';
END
GO

-- 显示Users表的列结构
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'Users' 
ORDER BY ORDINAL_POSITION;

-- 显示Departments表的列结构
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'Departments' 
ORDER BY ORDINAL_POSITION;
