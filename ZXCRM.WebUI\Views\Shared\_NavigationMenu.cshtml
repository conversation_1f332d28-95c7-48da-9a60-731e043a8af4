@using ZXCRM.WebUI.Services
@inject IPermissionCheckService PermissionService

@{
    var userId = User.GetUserId();

    // 添加调试日志
    System.Diagnostics.Debug.WriteLine($"[NavigationMenu] Current User ID: {userId}");
    System.Diagnostics.Debug.WriteLine($"[NavigationMenu] User Identity: {User.Identity?.Name}");
    System.Diagnostics.Debug.WriteLine($"[NavigationMenu] User IsAuthenticated: {User.Identity?.IsAuthenticated}");

    var userModules = await PermissionService.GetUserModulesAsync(userId);
    System.Diagnostics.Debug.WriteLine($"[NavigationMenu] User {userId} modules: {string.Join(", ", userModules)}");

    var userPermissions = await PermissionService.GetUserPermissionsAsync(userId);
    System.Diagnostics.Debug.WriteLine($"[NavigationMenu] User {userId} permissions count: {userPermissions.Count}");

    // 定义业务模块
    var businessModules = new Dictionary<string, (string Controller, string Icon)>
    {
        { "商机管理", ("Opportunity", "fas fa-seedling") },
        { "订单管理", ("Order", "fas fa-shopping-cart") },
        { "款项管理", ("Payment", "fas fa-money-bill-wave") },
        { "发票管理", ("Invoice", "fas fa-file-invoice") },
        { "报表统计", ("Report", "fas fa-chart-bar") }
    };

    // 定义系统模块
    var systemModules = new Dictionary<string, (string Controller, string Icon)>
    {
        { "用户管理", ("User", "fas fa-users") },
        { "部门管理", ("Department", "fas fa-building") },
        { "权限管理", ("Permission", "fas fa-shield-alt") }
    };

    // 检查用户是否有业务模块权限
    var hasBusinessAccess = businessModules.Keys.Any(module => userModules.Contains(module));
    
    // 检查用户是否有系统模块权限
    var hasSystemAccess = systemModules.Keys.Any(module => userModules.Contains(module));
}

<!-- 导航标签页 -->
<div class="sidebar-tabs">
    @if (hasBusinessAccess)
    {
        <div class="tab-item active" data-tab="business">
            <i class="fas fa-briefcase"></i>
            <span class="tab-text">经营数据</span>
        </div>
    }
    
    @if (hasSystemAccess)
    {
        <div class="tab-item @(hasBusinessAccess ? "" : "active")" data-tab="system">
            <i class="fas fa-cogs"></i>
            <span class="tab-text">系统管理</span>
        </div>
    }
</div>

<!-- 导航菜单区域 -->
<nav class="sidebar-menu">
    @if (hasBusinessAccess)
    {
        <!-- 业务模块菜单组 -->
        <div class="menu-group active" data-tab-content="business">
            <ul class="menu-list">
                @foreach (var module in businessModules)
                {
                    @if (userModules.Contains(module.Key))
                    {
                        <li class="menu-item">
                            <a asp-controller="@module.Value.Controller" asp-action="Index" class="menu-link">
                                <i class="@module.Value.Icon"></i>
                                <span class="menu-text">@module.Key</span>
                                @if (userPermissions.ContainsKey(module.Key))
                                {
                                    <span class="permission-indicator" title="权限: @string.Join(", ", userPermissions[module.Key])">
                                        <i class="fas fa-key"></i>
                                    </span>
                                }
                            </a>
                        </li>
                    }
                }
            </ul>
        </div>
    }

    @if (hasSystemAccess)
    {
        <!-- 系统管理菜单组 -->
        <div class="menu-group @(hasBusinessAccess ? "hidden" : "active")" data-tab-content="system">
            <ul class="menu-list">
                @foreach (var module in systemModules)
                {
                    @if (userModules.Contains(module.Key))
                    {
                        <li class="menu-item">
                            <a asp-controller="@module.Value.Controller" asp-action="Index" class="menu-link">
                                <i class="@module.Value.Icon"></i>
                                <span class="menu-text">@module.Key</span>
                                @if (userPermissions.ContainsKey(module.Key))
                                {
                                    <span class="permission-indicator" title="权限: @string.Join(", ", userPermissions[module.Key])">
                                        <i class="fas fa-key"></i>
                                    </span>
                                }
                            </a>
                        </li>
                    }
                }
            </ul>
        </div>
    }

    @if (!hasBusinessAccess && !hasSystemAccess)
    {
        <!-- 无权限提示 -->
        <div class="menu-group active" data-tab-content="empty">
            <div class="no-permission-notice">
                <div class="no-permission-icon">
                    <i class="fas fa-lock"></i>
                </div>
                <div class="no-permission-text">
                    <h6>暂无访问权限</h6>
                    <p>请联系管理员为您分配相应的模块权限</p>
                </div>
            </div>
        </div>
    }
</nav>

<style>
    .permission-indicator {
        margin-left: auto;
        opacity: 0.6;
        font-size: 0.75rem;
        color: #28a745;
    }

    .menu-link {
        display: flex;
        align-items: center;
        position: relative;
    }

    .menu-link .menu-text {
        flex: 1;
    }

    .no-permission-notice {
        padding: 30px 20px;
        text-align: center;
        color: #6c757d;
    }

    .no-permission-icon {
        font-size: 2rem;
        margin-bottom: 15px;
        opacity: 0.5;
    }

    .no-permission-text h6 {
        margin-bottom: 10px;
        color: #495057;
    }

    .no-permission-text p {
        font-size: 0.875rem;
        margin-bottom: 0;
        line-height: 1.4;
    }

    /* 隐藏没有权限的标签页 */
    .tab-item.hidden {
        display: none;
    }

    /* 权限提示样式 */
    .permission-indicator:hover {
        opacity: 1;
    }

    /* 响应式调整 */
    @@media (max-width: 768px) {
        .permission-indicator {
            display: none;
        }
    }
</style>

<script>
    // 权限相关的JavaScript函数
    $(document).ready(function() {
        // 如果只有一个标签页，隐藏标签页区域
        const visibleTabs = $('.tab-item:not(.hidden)').length;
        if (visibleTabs <= 1) {
            $('.sidebar-tabs').hide();
        }

        // 权限提示
        $('.permission-indicator').tooltip({
            placement: 'right',
            trigger: 'hover'
        });
    });
</script>
