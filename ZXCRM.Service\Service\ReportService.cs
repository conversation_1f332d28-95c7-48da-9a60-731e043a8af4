using ZXCRM.Data.UnitOfWork;
using ZXCRM.Service.DTOs;
using ZXCRM.Service.Interface;

namespace ZXCRM.Service.Service
{
    public class ReportService : IReportService
    {
        private readonly IUnitOfWork _unitOfWork;

        public ReportService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<DashboardStatsDTO> GetDashboardStatsAsync(ReportQueryDTO? query = null)
        {
            var startDate = query?.StartDate ?? DateTime.Now.AddMonths(-12);
            var endDate = query?.EndDate ?? DateTime.Now;
            var monthStart = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);

            // 获取商机统计
            var opportunities = await _unitOfWork.Opportunities.FindAsync(o =>
                o.CreatedAt >= startDate && o.CreatedAt <= endDate);
            var totalOpportunities = opportunities.Count();
            var wonOpportunities = opportunities.Count(o => o.Status == "成交");

            // 获取订单统计
            var orders = await _unitOfWork.Orders.FindAsync(o =>
                o.CreatedAt >= startDate && o.CreatedAt <= endDate);
            var totalOrders = orders.Count();
            var totalOrderAmount = orders.Sum(o => o.Amount);
            var monthlyOrders = orders.Where(o => o.CreatedAt >= monthStart);
            var monthlyOrderAmount = monthlyOrders.Sum(o => o.Amount);

            // 获取款项统计
            var payments = await _unitOfWork.Payments.FindAsync(p =>
                p.CreatedAt >= startDate && p.CreatedAt <= endDate);
            var totalPayments = payments.Count();
            var totalPaymentAmount = payments.Sum(p => p.Amount);
            var monthlyPayments = payments.Where(p => p.CreatedAt >= monthStart);
            var monthlyPaymentAmount = monthlyPayments.Sum(p => p.Amount);

            // 获取发票统计
            var invoices = await _unitOfWork.Invoices.FindAsync(i =>
                i.CreatedAt >= startDate && i.CreatedAt <= endDate && i.Status == "Normal");
            var totalInvoices = invoices.Count();
            var totalInvoiceAmount = invoices.Sum(i => i.Amount);
            var monthlyInvoices = invoices.Where(i => i.CreatedAt >= monthStart);
            var monthlyInvoiceAmount = monthlyInvoices.Sum(i => i.Amount);

            return new DashboardStatsDTO
            {
                TotalOpportunities = totalOpportunities,
                WonOpportunities = wonOpportunities,
                OpportunityWinRate = totalOpportunities > 0 ? (decimal)wonOpportunities / totalOpportunities * 100 : 0,

                TotalOrders = totalOrders,
                TotalOrderAmount = totalOrderAmount,
                MonthlyOrderAmount = monthlyOrderAmount,

                TotalPayments = totalPayments,
                TotalPaymentAmount = totalPaymentAmount,
                MonthlyPaymentAmount = monthlyPaymentAmount,

                TotalInvoices = totalInvoices,
                TotalInvoiceAmount = totalInvoiceAmount,
                MonthlyInvoiceAmount = monthlyInvoiceAmount
            };
        }

        public async Task<OpportunityReportDTO> GetOpportunityReportAsync(ReportQueryDTO? query = null)
        {
            var startDate = query?.StartDate ?? DateTime.Now.AddMonths(-12);
            var endDate = query?.EndDate ?? DateTime.Now;

            var opportunities = await _unitOfWork.Opportunities.GetAllWithIncludesAsync(o => o.CreatedBy);
            var filteredOpportunities = opportunities.Where(o =>
                o.CreatedAt >= startDate && o.CreatedAt <= endDate);

            if (query?.UserId.HasValue == true)
            {
                filteredOpportunities = filteredOpportunities.Where(o => o.CreatedById == query.UserId.Value);
            }

            var opportunityList = filteredOpportunities.ToList();
            var totalCount = opportunityList.Count;

            // 状态统计
            var statusStats = opportunityList
                .GroupBy(o => o.Status)
                .Select(g => new OpportunityStatusStatsDTO
                {
                    Status = g.Key,
                    Count = g.Count(),
                    Percentage = totalCount > 0 ? (decimal)g.Count() / totalCount * 100 : 0
                })
                .OrderByDescending(s => s.Count)
                .ToList();

            // 月度统计
            var monthlyStats = opportunityList
                .GroupBy(o => o.CreatedAt.ToString("yyyy-MM"))
                .Select(g => new MonthlyOpportunityStatsDTO
                {
                    Month = g.Key,
                    TotalCount = g.Count(),
                    WonCount = g.Count(o => o.Status == "成交"),
                    WinRate = g.Count() > 0 ? (decimal)g.Count(o => o.Status == "成交") / g.Count() * 100 : 0
                })
                .OrderBy(s => s.Month)
                .ToList();

            // 用户统计
            var userStats = opportunityList
                .GroupBy(o => o.CreatedBy.Name)
                .Select(g => new UserOpportunityStatsDTO
                {
                    UserName = g.Key,
                    TotalCount = g.Count(),
                    WonCount = g.Count(o => o.Status == "成交"),
                    WinRate = g.Count() > 0 ? (decimal)g.Count(o => o.Status == "成交") / g.Count() * 100 : 0
                })
                .OrderByDescending(s => s.TotalCount)
                .ToList();

            var overallWinRate = totalCount > 0 ?
                (decimal)opportunityList.Count(o => o.Status == "成交") / totalCount * 100 : 0;

            return new OpportunityReportDTO
            {
                StatusStats = statusStats,
                MonthlyStats = monthlyStats,
                UserStats = userStats,
                OverallWinRate = overallWinRate,
                TotalOpportunities = totalCount
            };
        }

        public async Task<OrderReportDTO> GetOrderReportAsync(ReportQueryDTO? query = null)
        {
            var startDate = query?.StartDate ?? DateTime.Now.AddMonths(-12);
            var endDate = query?.EndDate ?? DateTime.Now;

            var orders = await _unitOfWork.Orders.GetAllWithIncludesAsync(
                o => o.AccountManager,
                o => o.Department
            );

            var filteredOrders = orders.Where(o =>
                o.CreatedAt >= startDate && o.CreatedAt <= endDate);

            if (query?.DepartmentId.HasValue == true)
            {
                filteredOrders = filteredOrders.Where(o => o.DepartmentId == query.DepartmentId.Value);
            }

            if (query?.UserId.HasValue == true)
            {
                filteredOrders = filteredOrders.Where(o => o.AccountManagerId == query.UserId.Value);
            }

            if (!string.IsNullOrEmpty(query?.Currency))
            {
                filteredOrders = filteredOrders.Where(o => o.Currency == query.Currency);
            }

            var orderList = filteredOrders.ToList();
            var totalAmount = orderList.Sum(o => o.Amount);
            var totalCount = orderList.Count;

            // 月度统计
            var monthlyStats = orderList
                .GroupBy(o => o.CreatedAt.ToString("yyyy-MM"))
                .Select(g => new MonthlyOrderStatsDTO
                {
                    Month = g.Key,
                    Count = g.Count(),
                    Amount = g.Sum(o => o.Amount),
                    AverageAmount = g.Count() > 0 ? g.Sum(o => o.Amount) / g.Count() : 0
                })
                .OrderBy(s => s.Month)
                .ToList();

            // 币种统计
            var currencyStats = orderList
                .GroupBy(o => o.Currency)
                .Select(g => new CurrencyOrderStatsDTO
                {
                    Currency = g.Key,
                    Count = g.Count(),
                    Amount = g.Sum(o => o.Amount),
                    Percentage = totalAmount > 0 ? g.Sum(o => o.Amount) / totalAmount * 100 : 0
                })
                .OrderByDescending(s => s.Amount)
                .ToList();

            // 部门统计
            var departmentStats = orderList
                .GroupBy(o => o.Department.Name)
                .Select(g => new DepartmentOrderStatsDTO
                {
                    DepartmentName = g.Key,
                    Count = g.Count(),
                    Amount = g.Sum(o => o.Amount),
                    Percentage = totalAmount > 0 ? g.Sum(o => o.Amount) / totalAmount * 100 : 0
                })
                .OrderByDescending(s => s.Amount)
                .ToList();

            // 用户统计
            var userStats = orderList
                .GroupBy(o => o.AccountManager.Name)
                .Select(g => new UserOrderStatsDTO
                {
                    UserName = g.Key,
                    Count = g.Count(),
                    Amount = g.Sum(o => o.Amount),
                    AverageAmount = g.Count() > 0 ? g.Sum(o => o.Amount) / g.Count() : 0
                })
                .OrderByDescending(s => s.Amount)
                .ToList();

            return new OrderReportDTO
            {
                MonthlyStats = monthlyStats,
                CurrencyStats = currencyStats,
                DepartmentStats = departmentStats,
                UserStats = userStats,
                TotalAmount = totalAmount,
                TotalCount = totalCount
            };
        }

        public async Task<PaymentReportDTO> GetPaymentReportAsync(ReportQueryDTO? query = null)
        {
            var startDate = query?.StartDate ?? DateTime.Now.AddMonths(-12);
            var endDate = query?.EndDate ?? DateTime.Now;

            var payments = await _unitOfWork.Payments.GetAllWithIncludesAsync(p => p.Order);
            var filteredPayments = payments.Where(p =>
                p.CreatedAt >= startDate && p.CreatedAt <= endDate);

            if (!string.IsNullOrEmpty(query?.Currency))
            {
                filteredPayments = filteredPayments.Where(p => p.Currency == query.Currency);
            }

            var paymentList = filteredPayments.ToList();
            var totalAmount = paymentList.Sum(p => p.Amount);
            var totalCount = paymentList.Count;

            // 计算回款率（假设订单总金额为基准）
            var orderTotalAmount = paymentList.Sum(p => p.Order.Amount);
            var collectionRate = orderTotalAmount > 0 ? totalAmount / orderTotalAmount * 100 : 0;

            // 月度统计
            var monthlyStats = paymentList
                .GroupBy(p => p.CreatedAt.ToString("yyyy-MM"))
                .Select(g => new MonthlyPaymentStatsDTO
                {
                    Month = g.Key,
                    Count = g.Count(),
                    Amount = g.Sum(p => p.Amount),
                    CollectionRate = g.Sum(p => p.Order.Amount) > 0 ?
                        g.Sum(p => p.Amount) / g.Sum(p => p.Order.Amount) * 100 : 0
                })
                .OrderBy(s => s.Month)
                .ToList();

            // 类型统计
            var typeStats = paymentList
                .GroupBy(p => p.PaymentType)
                .Select(g => new PaymentTypeStatsDTO
                {
                    Type = g.Key,
                    Count = g.Count(),
                    Amount = g.Sum(p => p.Amount),
                    Percentage = totalAmount > 0 ? g.Sum(p => p.Amount) / totalAmount * 100 : 0
                })
                .OrderByDescending(s => s.Amount)
                .ToList();

            // 币种统计
            var currencyStats = paymentList
                .GroupBy(p => p.Currency)
                .Select(g => new CurrencyPaymentStatsDTO
                {
                    Currency = g.Key,
                    Count = g.Count(),
                    Amount = g.Sum(p => p.Amount),
                    Percentage = totalAmount > 0 ? g.Sum(p => p.Amount) / totalAmount * 100 : 0
                })
                .OrderByDescending(s => s.Amount)
                .ToList();

            return new PaymentReportDTO
            {
                MonthlyStats = monthlyStats,
                TypeStats = typeStats,
                CurrencyStats = currencyStats,
                TotalAmount = totalAmount,
                TotalCount = totalCount,
                CollectionRate = collectionRate
            };
        }

        public async Task<InvoiceReportDTO> GetInvoiceReportAsync(ReportQueryDTO? query = null)
        {
            var startDate = query?.StartDate ?? DateTime.Now.AddMonths(-12);
            var endDate = query?.EndDate ?? DateTime.Now;

            var invoices = await _unitOfWork.Invoices.FindAsync(i =>
                i.CreatedAt >= startDate && i.CreatedAt <= endDate && i.Status == "Normal");

            var invoiceList = invoices.ToList();
            var totalAmount = invoiceList.Sum(i => i.Amount);
            var totalCount = invoiceList.Count;
            var totalTaxAmount = invoiceList.Sum(i => i.Amount * i.TaxRate / 100);

            // 月度统计
            var monthlyStats = invoiceList
                .GroupBy(i => i.CreatedAt.ToString("yyyy-MM"))
                .Select(g => new MonthlyInvoiceStatsDTO
                {
                    Month = g.Key,
                    Count = g.Count(),
                    Amount = g.Sum(i => i.Amount),
                    TaxAmount = g.Sum(i => i.Amount * i.TaxRate / 100)
                })
                .OrderBy(s => s.Month)
                .ToList();

            // 类型统计
            var typeStats = invoiceList
                .GroupBy(i => i.Type)
                .Select(g => new InvoiceTypeStatsDTO
                {
                    Type = g.Key,
                    Count = g.Count(),
                    Amount = g.Sum(i => i.Amount),
                    Percentage = totalAmount > 0 ? g.Sum(i => i.Amount) / totalAmount * 100 : 0
                })
                .OrderByDescending(s => s.Amount)
                .ToList();

            // 公司统计
            var companyStats = invoiceList
                .GroupBy(i => i.Company)
                .Select(g => new InvoiceCompanyStatsDTO
                {
                    Company = g.Key,
                    Count = g.Count(),
                    Amount = g.Sum(i => i.Amount),
                    Percentage = totalAmount > 0 ? g.Sum(i => i.Amount) / totalAmount * 100 : 0
                })
                .OrderByDescending(s => s.Amount)
                .ToList();

            // 税率统计
            var taxRateStats = invoiceList
                .GroupBy(i => i.TaxRate)
                .Select(g => new TaxRateStatsDTO
                {
                    TaxRate = g.Key,
                    Count = g.Count(),
                    Amount = g.Sum(i => i.Amount),
                    TaxAmount = g.Sum(i => i.Amount * i.TaxRate / 100),
                    Percentage = totalAmount > 0 ? g.Sum(i => i.Amount) / totalAmount * 100 : 0
                })
                .OrderByDescending(s => s.Amount)
                .ToList();

            return new InvoiceReportDTO
            {
                MonthlyStats = monthlyStats,
                TypeStats = typeStats,
                CompanyStats = companyStats,
                TaxRateStats = taxRateStats,
                TotalAmount = totalAmount,
                TotalCount = totalCount,
                TotalTaxAmount = totalTaxAmount
            };
        }

        public async Task<byte[]> ExportReportAsync(string reportType, ReportQueryDTO? query = null)
        {
            // TODO: 实现报表导出功能（Excel/PDF）
            // 这里可以使用 EPPlus 或其他库来生成 Excel 文件
            await Task.CompletedTask;
            return Array.Empty<byte>();
        }
    }
}
