using AutoMapper;
using Microsoft.EntityFrameworkCore;
using ZXCRM.Data.Entities;
using ZXCRM.Data.UnitOfWork;
using ZXCRM.Service.DTOs;
using ZXCRM.Service.Interface;

namespace ZXCRM.Service.Service
{
    public class UserService : IUserService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public UserService(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task<IEnumerable<UserDTO>> GetAllUsersAsync()
        {
            var users = await _unitOfWork.Users.GetAllWithIncludesAsync(u => u.Department);
            return _mapper.Map<IEnumerable<UserDTO>>(users);
        }

        public async Task<UserDTO?> GetUserByIdAsync(int id)
        {
            var user = await _unitOfWork.Users.GetByIdWithIncludesAsync(id, u => u.Department);
            return user != null ? _mapper.Map<UserDTO>(user) : null;
        }

        public async Task<UserDTO?> GetUserByUsernameAsync(string username)
        {
            var users = await _unitOfWork.Users.FindAsync(u => u.Username == username);
            var user = users.FirstOrDefault();
            return user != null ? _mapper.Map<UserDTO>(user) : null;
        }

        public async Task<UserDTO> CreateUserAsync(CreateUserDTO createUserDto)
        {
            try
            {
                // 开始事务
                await _unitOfWork.BeginTransactionAsync();

                // 检查用户名是否已存在
                var existingUsers = await _unitOfWork.Users.FindAsync(u => u.Username == createUserDto.Username);
                if (existingUsers.Any())
                {
                    throw new Exception("用户名已存在");
                }

                // 检查部门是否存在
                var department = await _unitOfWork.Departments.GetByIdAsync(createUserDto.DepartmentId);
                if (department == null)
                {
                    throw new Exception("部门不存在");
                }

                // 创建用户
                var user = new User
                {
                    Username = createUserDto.Username,
                    Password = BCrypt.Net.BCrypt.HashPassword(createUserDto.Password),
                    Name = createUserDto.Name,
                    Gender = createUserDto.Gender,
                    Email = createUserDto.Email,
                    Phone = createUserDto.Phone,
                    DepartmentId = createUserDto.DepartmentId
                };

                await _unitOfWork.Users.AddAsync(user);
                await _unitOfWork.SaveChangesAsync();

                // 检查是否存在查询权限
                var permissions = await _unitOfWork.Permissions.FindAsync(p => p.Code == "Query");
                var queryPermission = permissions.FirstOrDefault();

                if (queryPermission != null)
                {
                    var moduleTypes = new[] { "商机", "订单", "款项", "发票" };

                    foreach (var moduleType in moduleTypes)
                    {
                        var userPermission = new UserPermission
                        {
                            UserId = user.Id,
                            PermissionId = queryPermission.Id,
                            ModuleType = moduleType
                        };

                        await _unitOfWork.UserPermissions.AddAsync(userPermission);
                    }

                    await _unitOfWork.SaveChangesAsync();
                }

                // 提交事务
                await _unitOfWork.CommitTransactionAsync();

                return _mapper.Map<UserDTO>(user);
            }
            catch (Exception)
            {
                // 回滚事务
                await _unitOfWork.RollbackTransactionAsync();
                throw;
            }
        }

        public async Task<UserDTO?> UpdateUserAsync(UpdateUserDTO updateUserDto)
        {
            var user = await _unitOfWork.Users.GetByIdAsync(updateUserDto.Id);
            if (user == null)
            {
                return null;
            }

            // 更新用户信息
            if (!string.IsNullOrEmpty(updateUserDto.Name))
            {
                user.Name = updateUserDto.Name;
            }

            if (updateUserDto.Gender != null)
            {
                user.Gender = updateUserDto.Gender;
            }

            if (updateUserDto.Email != null)
            {
                user.Email = updateUserDto.Email;
            }

            if (updateUserDto.Phone != null)
            {
                user.Phone = updateUserDto.Phone;
            }

            if (updateUserDto.DepartmentId.HasValue)
            {
                // 检查部门是否存在
                var department = await _unitOfWork.Departments.GetByIdAsync(updateUserDto.DepartmentId.Value);
                if (department == null)
                {
                    throw new Exception("部门不存在");
                }

                user.DepartmentId = updateUserDto.DepartmentId.Value;
            }

            if (!string.IsNullOrEmpty(updateUserDto.Password))
            {
                user.Password = BCrypt.Net.BCrypt.HashPassword(updateUserDto.Password);
            }

            await _unitOfWork.Users.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();

            return _mapper.Map<UserDTO>(user);
        }

        public async Task<bool> DeleteUserAsync(int id)
        {
            var user = await _unitOfWork.Users.GetByIdAsync(id);
            if (user == null)
            {
                return false;
            }

            // 软删除
            user.IsDeleted = true;

            await _unitOfWork.Users.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();

            return true;
        }

        public async Task<bool> CheckUserPermissionAsync(int userId, string moduleType, string permissionCode)
        {
            var user = await _unitOfWork.Users.GetByIdAsync(userId);
            if (user == null)
            {
                return false;
            }

            var permissions = await _unitOfWork.Permissions.FindAsync(p => p.Code == permissionCode);
            var permission = permissions.FirstOrDefault();
            if (permission == null)
            {
                return false;
            }

            var userPermissions = await _unitOfWork.UserPermissions.FindAsync(
                up => up.UserId == userId && up.PermissionId == permission.Id && up.ModuleType == moduleType);

            return userPermissions.Any();
        }
    }
}
