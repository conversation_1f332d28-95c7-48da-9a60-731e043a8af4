using AutoMapper;
using ZXCRM.Data.Entities;
using ZXCRM.Service.DTOs;

namespace ZXCRM.Service.Mappings
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            // 现有的映射配置...

            // 权限映射
            CreateMap<Permission, PermissionDto>();
            CreateMap<PermissionDto, Permission>();

            // 用户权限映射
            CreateMap<UserPermission, UserPermissionDto>();
            CreateMap<UserPermissionDto, UserPermission>();
        }
    }
} 