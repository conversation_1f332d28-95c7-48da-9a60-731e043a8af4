@using ZXCRM.WebUI.Services
@{
    ViewData["Title"] = "API连接测试";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2>API连接测试</h2>
            
            <div class="card mb-3">
                <div class="card-header">
                    <h5>API连接状态</h5>
                </div>
                <div class="card-body">
                    <button id="testApiConnection" class="btn btn-primary">测试API连接</button>
                    <button id="testPermissionApi" class="btn btn-secondary">测试权限API</button>
                    <div id="apiTestResult" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('testApiConnection').addEventListener('click', async function() {
    const resultDiv = document.getElementById('apiTestResult');
    resultDiv.innerHTML = '<div class="alert alert-info">正在测试API连接...</div>';
    
    try {
        // 测试基本API连接
        const response = await fetch('http://localhost:5000/api/user', {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer @ViewContext.HttpContext.Request.Cookies["AccessToken"]',
                'Content-Type': 'application/json'
            }
        });
        
        const result = {
            status: response.status,
            statusText: response.statusText,
            ok: response.ok,
            url: response.url
        };
        
        if (response.ok) {
            const data = await response.text();
            result.data = data;
        }
        
        resultDiv.innerHTML = `
            <div class="alert alert-${response.ok ? 'success' : 'danger'}">
                <h6>API连接测试结果</h6>
                <pre>${JSON.stringify(result, null, 2)}</pre>
            </div>
        `;
    } catch (error) {
        resultDiv.innerHTML = `
            <div class="alert alert-danger">
                <h6>API连接失败</h6>
                <p><strong>错误:</strong> ${error.message}</p>
                <p><strong>可能原因:</strong></p>
                <ul>
                    <li>WebAPI服务未启动</li>
                    <li>端口配置错误（当前配置: http://localhost:5000/）</li>
                    <li>CORS配置问题</li>
                    <li>网络连接问题</li>
                </ul>
            </div>
        `;
    }
});

document.getElementById('testPermissionApi').addEventListener('click', async function() {
    const resultDiv = document.getElementById('apiTestResult');
    resultDiv.innerHTML = '<div class="alert alert-info">正在测试权限API...</div>';
    
    try {
        // 测试权限API
        const userId = @User.GetUserId();
        const response = await fetch(`http://localhost:5000/api/permissioncheck/user/${userId}/modules`, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer @ViewContext.HttpContext.Request.Cookies["AccessToken"]',
                'Content-Type': 'application/json'
            }
        });
        
        const result = {
            status: response.status,
            statusText: response.statusText,
            ok: response.ok,
            url: response.url
        };
        
        if (response.ok) {
            const data = await response.json();
            result.data = data;
        } else {
            const errorText = await response.text();
            result.error = errorText;
        }
        
        resultDiv.innerHTML = `
            <div class="alert alert-${response.ok ? 'success' : 'danger'}">
                <h6>权限API测试结果</h6>
                <pre>${JSON.stringify(result, null, 2)}</pre>
            </div>
        `;
    } catch (error) {
        resultDiv.innerHTML = `
            <div class="alert alert-danger">
                <h6>权限API测试失败</h6>
                <p><strong>错误:</strong> ${error.message}</p>
                <p><strong>测试URL:</strong> http://localhost:5000/api/permissioncheck/user/@User.GetUserId()/modules</p>
            </div>
        `;
    }
});
</script>
