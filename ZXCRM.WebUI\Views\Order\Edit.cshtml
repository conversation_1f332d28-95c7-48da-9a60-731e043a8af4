@model ZXCRM.WebUI.Models.ViewModels.EditOrderViewModel
@{
    ViewData["Title"] = "编辑订单";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">编辑订单</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a asp-controller="Dashboard" asp-action="Index">首页</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Order" asp-action="Index">订单管理</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Order" asp-action="Details" asp-route-id="@Model.Id">订单详情</a></li>
                    <li class="breadcrumb-item active">编辑订单</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-edit"></i> 编辑订单信息
                        </h3>
                        <div class="card-tools">
                            <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info btn-sm">
                                <i class="fas fa-eye"></i> 查看详情
                            </a>
                        </div>
                    </div>
                    <form asp-action="Edit" method="post">
                        <input asp-for="Id" type="hidden" />
                        <div class="card-body">
                            <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                            <div class="row">
                                <div class="col-md-6">
                                    <!-- 订单名称 -->
                                    <div class="form-group">
                                        <label asp-for="Name" class="form-label required"></label>
                                        <input asp-for="Name" class="form-control" placeholder="请输入订单名称" />
                                        <span asp-validation-for="Name" class="text-danger"></span>
                                    </div>

                                    <!-- 客户名称 -->
                                    <div class="form-group">
                                        <label asp-for="CustomerName" class="form-label required"></label>
                                        <input asp-for="CustomerName" class="form-control" placeholder="请输入客户名称" />
                                        <span asp-validation-for="CustomerName" class="text-danger"></span>
                                    </div>

                                    <!-- 联系人 -->
                                    <div class="form-group">
                                        <label asp-for="ContactName" class="form-label"></label>
                                        <input asp-for="ContactName" class="form-control" placeholder="请输入联系人姓名" />
                                        <span asp-validation-for="ContactName" class="text-danger"></span>
                                    </div>

                                    <!-- 联系电话 -->
                                    <div class="form-group">
                                        <label asp-for="ContactPhone" class="form-label"></label>
                                        <input asp-for="ContactPhone" class="form-control" placeholder="请输入联系电话" />
                                        <span asp-validation-for="ContactPhone" class="text-danger"></span>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <!-- 订单金额 -->
                                    <div class="form-group">
                                        <label asp-for="Amount" class="form-label required"></label>
                                        <div class="input-group">
                                            <input asp-for="Amount" class="form-control" placeholder="0.00" step="0.01" />
                                            <div class="input-group-append">
                                                <select asp-for="Currency" class="form-control">
                                                    <option value="CNY">CNY</option>
                                                    <option value="USD">USD</option>
                                                    <option value="JPY">JPY</option>
                                                    <option value="EUR">EUR</option>
                                                </select>
                                            </div>
                                        </div>
                                        <span asp-validation-for="Amount" class="text-danger"></span>
                                    </div>

                                    <!-- 结算金额 -->
                                    <div class="form-group">
                                        <label asp-for="SettlementAmount" class="form-label required"></label>
                                        <input asp-for="SettlementAmount" class="form-control" placeholder="0.00" step="0.01" />
                                        <span asp-validation-for="SettlementAmount" class="text-danger"></span>
                                        <small class="form-text text-muted">结算金额通常与订单金额相同</small>
                                    </div>

                                    <!-- 签约日期 -->
                                    <div class="form-group">
                                        <label asp-for="SignDate" class="form-label required"></label>
                                        <input asp-for="SignDate" class="form-control" type="date" />
                                        <span asp-validation-for="SignDate" class="text-danger"></span>
                                    </div>

                                    <!-- 订单状态 -->
                                    <div class="form-group">
                                        <label asp-for="Status" class="form-label required"></label>
                                        <select asp-for="Status" class="form-control">
                                            <option value="">请选择状态</option>
                                            <option value="待确认">待确认</option>
                                            <option value="进行中">进行中</option>
                                            <option value="已完成">已完成</option>
                                            <option value="已取消">已取消</option>
                                        </select>
                                        <span asp-validation-for="Status" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <div class="row">
                                <div class="col-md-6">
                                    <!-- 客户经理 -->
                                    <div class="form-group">
                                        <label asp-for="AccountManagerId" class="form-label required"></label>
                                        <select asp-for="AccountManagerId" class="form-control">
                                            <option value="">请选择客户经理</option>
                                            @foreach (var user in Model.Users)
                                            {
                                                <option value="@user.Id">@user.Name (@user.Username)</option>
                                            }
                                        </select>
                                        <span asp-validation-for="AccountManagerId" class="text-danger"></span>
                                    </div>

                                    <!-- 创建部门 -->
                                    <div class="form-group">
                                        <label asp-for="DepartmentId" class="form-label required"></label>
                                        <select asp-for="DepartmentId" class="form-control">
                                            <option value="">请选择创建部门</option>
                                            @foreach (var dept in Model.Departments)
                                            {
                                                <option value="@dept.Id">@dept.Name</option>
                                            }
                                        </select>
                                        <span asp-validation-for="DepartmentId" class="text-danger"></span>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <!-- 项目经理 -->
                                    <div class="form-group">
                                        <label asp-for="ProjectManagerId" class="form-label"></label>
                                        <select asp-for="ProjectManagerId" class="form-control">
                                            <option value="">请选择项目经理（可选）</option>
                                            @foreach (var user in Model.Users)
                                            {
                                                <option value="@user.Id">@user.Name (@user.Username)</option>
                                            }
                                        </select>
                                        <span asp-validation-for="ProjectManagerId" class="text-danger"></span>
                                    </div>

                                    <!-- 业绩归属部门 -->
                                    <div class="form-group">
                                        <label asp-for="PerformanceDepartmentId" class="form-label"></label>
                                        <select asp-for="PerformanceDepartmentId" class="form-control">
                                            <option value="">请选择业绩归属部门（可选）</option>
                                            @foreach (var dept in Model.Departments)
                                            {
                                                <option value="@dept.Id">@dept.Name</option>
                                            }
                                        </select>
                                        <span asp-validation-for="PerformanceDepartmentId" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card-footer">
                            <div class="row">
                                <div class="col-md-6">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> 保存更改
                                    </button>
                                    <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-secondary ml-2">
                                        <i class="fas fa-times"></i> 取消
                                    </a>
                                </div>
                                <div class="col-md-6 text-right">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        标有 <span class="text-danger">*</span> 的字段为必填项
                                    </small>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="col-md-4">
                <!-- 编辑说明 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-info-circle"></i> 编辑说明
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> 注意事项</h6>
                            <ul class="mb-0">
                                <li>修改订单金额可能影响相关的款项记录</li>
                                <li>更改订单状态会影响统计报表</li>
                                <li>客户经理变更需要通知相关人员</li>
                                <li>项目经理负责订单的执行进度</li>
                            </ul>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb"></i> 提示</h6>
                            <p class="mb-0">
                                如果订单已有款项记录，建议谨慎修改订单金额。
                                状态变更会记录在系统日志中。
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 快捷操作 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-bolt"></i> 快捷操作
                        </h3>
                    </div>
                    <div class="card-body">
                        <button type="button" class="btn btn-outline-primary btn-sm btn-block" onclick="copyAmount()">
                            <i class="fas fa-copy"></i> 复制订单金额到结算金额
                        </button>
                        <button type="button" class="btn btn-outline-info btn-sm btn-block" onclick="setStatusInProgress()">
                            <i class="fas fa-play"></i> 设置状态为进行中
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm btn-block" onclick="setStatusCompleted()">
                            <i class="fas fa-check"></i> 设置状态为已完成
                        </button>
                    </div>
                </div>

                <!-- 操作历史 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-history"></i> 操作历史
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <div class="time-label">
                                <span class="bg-info">最近操作</span>
                            </div>
                            <div>
                                <i class="fas fa-edit bg-blue"></i>
                                <div class="timeline-item">
                                    <span class="time"><i class="fas fa-clock"></i> 正在编辑</span>
                                    <h3 class="timeline-header">编辑订单信息</h3>
                                    <div class="timeline-body">
                                        当前正在编辑订单信息，请确认所有字段填写正确。
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        function copyAmount() {
            const amount = document.getElementById('Amount').value;
            if (amount) {
                document.getElementById('SettlementAmount').value = amount;
                toastr.info('已复制订单金额到结算金额');
            }
        }

        function setStatusInProgress() {
            document.getElementById('Status').value = '进行中';
            toastr.info('已设置状态为进行中');
        }

        function setStatusCompleted() {
            document.getElementById('Status').value = '已完成';
            toastr.info('已设置状态为已完成');
        }

        // 表单验证增强
        $(document).ready(function() {
            // 金额输入验证
            $('#Amount, #SettlementAmount').on('input', function() {
                const value = parseFloat($(this).val());
                if (value <= 0) {
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });

            // 状态变更提醒
            $('#Status').on('change', function() {
                const status = $(this).val();
                if (status === '已完成') {
                    toastr.warning('设置为已完成状态后，请确保所有工作已完成');
                } else if (status === '已取消') {
                    toastr.warning('设置为已取消状态后，请确认是否需要处理相关事务');
                }
            });
        });
    </script>
}

@section Styles {
    <style>
        .required::after {
            content: " *";
            color: red;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .is-invalid {
            border-color: #dc3545;
        }

        .timeline {
            position: relative;
            margin: 0 0 30px 0;
            padding: 0;
            list-style: none;
        }

        .timeline:before {
            content: '';
            position: absolute;
            top: 0;
            left: 25px;
            height: 100%;
            width: 2px;
            background: #dee2e6;
        }

        .timeline > div {
            position: relative;
            margin-right: 10px;
            margin-bottom: 15px;
        }

        .timeline > div > .timeline-item {
            box-shadow: 0 1px 1px rgba(0,0,0,0.1);
            border-radius: 3px;
            margin-top: 0;
            background: #fff;
            color: #444;
            margin-left: 60px;
            margin-right: 15px;
            padding: 0;
            position: relative;
        }

        .timeline > div > .fas {
            width: 30px;
            height: 30px;
            font-size: 15px;
            line-height: 30px;
            position: absolute;
            color: #666;
            background: #f4f4f4;
            border-radius: 50%;
            text-align: center;
            left: 18px;
            top: 0;
        }
    </style>
}
