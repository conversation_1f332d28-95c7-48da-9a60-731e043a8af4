namespace ZXCRM.Data.Entities
{
    public class Order : BaseEntity
    {
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string? ContactName { get; set; }
        public string? ContactPhone { get; set; }
        public int? OpportunityId { get; set; }
        public decimal Amount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public decimal SettlementAmount { get; set; }
        public int AccountManagerId { get; set; }
        public int DepartmentId { get; set; }
        public DateTime SignDate { get; set; }
        public string Status { get; set; } = string.Empty;
        public int? ProjectManagerId { get; set; }
        public int? PerformanceDepartmentId { get; set; }
        public int CreatedById { get; set; }
        
        // 导航属性
        public Opportunity? Opportunity { get; set; }
        public User AccountManager { get; set; } = null!;
        public User? ProjectManager { get; set; }
        public Department Department { get; set; } = null!;
        public Department? PerformanceDepartment { get; set; }
        public User CreatedBy { get; set; } = null!;
        public ICollection<Payment> Payments { get; set; } = new List<Payment>();
    }
}
