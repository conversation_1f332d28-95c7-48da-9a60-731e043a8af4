@model ZXCRM.WebUI.Models.ViewModels.ReportIndexViewModel
@{
    ViewData["Title"] = "报表统计";
}

<!-- 数据页面容器 -->
<div class="data-page-container">
    <!-- 查询区域（可收缩） -->
    <div class="query-section" id="querySection">
        <div class="query-header">
            <div class="query-title">
                <i class="fas fa-filter"></i>
                <span>查询条件</span>
                <span class="badge badge-info ml-2">统计分析</span>
            </div>
            <button type="button" class="query-toggle" onclick="toggleQuerySection()">
                <i class="fas fa-chevron-up" id="queryToggleIcon"></i>
            </button>
        </div>
        <div class="query-content" id="queryContent">
            <form asp-action="Index" method="get" class="query-form">
                <div class="query-row">
                    <div class="query-item">
                        <label>开始日期:</label>
                        <input asp-for="Query.StartDate" type="date" class="form-control form-control-sm" />
                    </div>
                    <div class="query-item">
                        <label>结束日期:</label>
                        <input asp-for="Query.EndDate" type="date" class="form-control form-control-sm" />
                    </div>
                    <div class="query-item">
                        <label>部门:</label>
                        <select asp-for="Query.DepartmentId" class="form-control form-control-sm">
                            <option value="">全部部门</option>
                            @foreach (var dept in Model.Departments)
                            {
                                <option value="@dept.Id">@dept.Name</option>
                            }
                        </select>
                    </div>
                    <div class="query-item">
                        <label>用户:</label>
                        <select asp-for="Query.UserId" class="form-control form-control-sm">
                            <option value="">全部用户</option>
                            @foreach (var user in Model.Users)
                            {
                                <option value="@user.Id">@user.Name</option>
                            }
                        </select>
                    </div>
                    <div class="query-item">
                        <label>&nbsp;</label>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="fas fa-search"></i> 查询
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
        <div class="action-left">
            <div class="btn-group btn-group-sm" role="group">
                <a asp-action="Dashboard" class="btn btn-primary">
                    <i class="fas fa-tachometer-alt"></i> 综合仪表盘
                </a>
                <a asp-action="Opportunity" class="btn btn-success">
                    <i class="fas fa-seedling"></i> 商机分析
                </a>
                <a asp-action="Order" class="btn btn-info">
                    <i class="fas fa-shopping-cart"></i> 订单分析
                </a>
                <a asp-action="Payment" class="btn btn-warning">
                    <i class="fas fa-money-bill"></i> 款项分析
                </a>
                <a asp-action="Invoice" class="btn btn-danger">
                    <i class="fas fa-file-invoice"></i> 发票分析
                </a>
            </div>
        </div>
        <div class="action-right">
            <div class="view-options">
                <span class="text-muted">报表导出:</span>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-secondary">
                        <i class="fas fa-file-excel"></i> Excel
                    </button>
                    <button type="button" class="btn btn-outline-secondary">
                        <i class="fas fa-file-pdf"></i> PDF
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 仪表盘内容区域 -->
    <div class="dashboard-section">
        @if (Model.DashboardStats != null)
        {
            <!-- 核心统计卡片 -->
            <div class="stats-cards">
                <div class="stats-row">
                    <div class="stats-card bg-info">
                        <div class="stats-content">
                            <div class="stats-number">@Model.DashboardStats.TotalOpportunities</div>
                            <div class="stats-label">总商机数</div>
                        </div>
                        <div class="stats-icon">
                            <i class="fas fa-seedling"></i>
                        </div>
                        <a asp-action="Opportunity" class="stats-footer">
                            查看详情 <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>

                    <div class="stats-card bg-success">
                        <div class="stats-content">
                            <div class="stats-number">@Model.DashboardStats.TotalOrders</div>
                            <div class="stats-label">总订单数</div>
                        </div>
                        <div class="stats-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <a asp-action="Order" class="stats-footer">
                            查看详情 <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>

                    <div class="stats-card bg-warning">
                        <div class="stats-content">
                            <div class="stats-number">@Model.DashboardStats.TotalPayments</div>
                            <div class="stats-label">总款项数</div>
                        </div>
                        <div class="stats-icon">
                            <i class="fas fa-money-bill"></i>
                        </div>
                        <a asp-action="Payment" class="stats-footer">
                            查看详情 <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>

                    <div class="stats-card bg-danger">
                        <div class="stats-content">
                            <div class="stats-number">@Model.DashboardStats.TotalInvoices</div>
                            <div class="stats-label">总发票数</div>
                        </div>
                        <div class="stats-icon">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                        <a asp-action="Invoice" class="stats-footer">
                            查看详情 <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 金额统计信息框 -->
            <div class="info-boxes">
                <div class="info-box-row">
                    <div class="info-box">
                        <span class="info-box-icon bg-info"><i class="fas fa-chart-line"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">商机成交率</span>
                            <span class="info-box-number">@Model.DashboardStats.OpportunityWinRate.ToString("F1")%</span>
                        </div>
                    </div>
                    <div class="info-box">
                        <span class="info-box-icon bg-success"><i class="fas fa-dollar-sign"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">订单总金额</span>
                            <span class="info-box-number">@Model.DashboardStats.TotalOrderAmount.ToString("N0")</span>
                        </div>
                    </div>
                    <div class="info-box">
                        <span class="info-box-icon bg-warning"><i class="fas fa-coins"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">回款总金额</span>
                            <span class="info-box-number">@Model.DashboardStats.TotalPaymentAmount.ToString("N0")</span>
                        </div>
                    </div>
                    <div class="info-box">
                        <span class="info-box-icon bg-danger"><i class="fas fa-receipt"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">发票总金额</span>
                            <span class="info-box-number">@Model.DashboardStats.TotalInvoiceAmount.ToString("N0")</span>
                        </div>
                    </div>
                </div>
            </div>
        }

        <!-- 详细报表导航 -->
        <div class="report-navigation">
            <div class="section-header">
                <h5><i class="fas fa-chart-bar"></i> 详细报表</h5>
            </div>
            <div class="report-grid">
                @foreach (var reportType in ZXCRM.WebUI.Models.ViewModels.ReportTypes.GetAllTypes())
                {
                    <div class="report-item">
                        <div class="report-icon <EMAIL>">
                            <i class="@reportType.Icon"></i>
                        </div>
                        <div class="report-content">
                            <div class="report-name">@reportType.Name</div>
                            <a asp-action="@reportType.Value.Substring(0, 1).ToUpper()@reportType.Value.Substring(1)"
                               class="btn <EMAIL> btn-sm">
                                查看报表
                            </a>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // 查询区收缩切换
        function toggleQuerySection() {
            const queryContent = document.getElementById('queryContent');
            const queryToggleIcon = document.getElementById('queryToggleIcon');
            const isCollapsed = queryContent.style.display === 'none';

            if (isCollapsed) {
                queryContent.style.display = 'block';
                queryToggleIcon.className = 'fas fa-chevron-up';
                localStorage.setItem('querySection_collapsed', 'false');
            } else {
                queryContent.style.display = 'none';
                queryToggleIcon.className = 'fas fa-chevron-down';
                localStorage.setItem('querySection_collapsed', 'true');
            }
        }

        $(document).ready(function() {
            // 恢复查询区收缩状态
            const isCollapsed = localStorage.getItem('querySection_collapsed') === 'true';
            if (isCollapsed) {
                document.getElementById('queryContent').style.display = 'none';
                document.getElementById('queryToggleIcon').className = 'fas fa-chevron-down';
            }

            // 设置默认日期范围
            if (!$('#Query_StartDate').val()) {
                var threeMonthsAgo = new Date();
                threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
                $('#Query_StartDate').val(threeMonthsAgo.toISOString().split('T')[0]);
            }

            if (!$('#Query_EndDate').val()) {
                var today = new Date();
                $('#Query_EndDate').val(today.toISOString().split('T')[0]);
            }

            // 显示提示消息
            @if (TempData["SuccessMessage"] != null)
            {
                <text>
                toastr.success('@TempData["SuccessMessage"]');
                </text>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <text>
                toastr.error('@TempData["ErrorMessage"]');
                </text>
            }
        });
    </script>
}
